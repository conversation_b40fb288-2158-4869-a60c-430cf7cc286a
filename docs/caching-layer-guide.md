# Review It - Caching Layer Guide

## 📋 Overview

The Review It application uses a sophisticated Redis-based caching layer to optimize database performance and provide fast response times. This guide covers the architecture, configuration, and usage of the caching system.

## 🏗️ Architecture

### Cache Strategy
- **Cache-Aside Pattern**: Application manages cache alongside database
- **Automatic Fallback**: All cache operations gracefully fall back to database queries
- **Proactive Invalidation**: <PERSON><PERSON> is invalidated immediately when data changes
- **TTL Safety Net**: Time-based expiration prevents stale data in edge cases

### Technology Stack
- **Redis Provider**: Upstash Redis (cloud-hosted)
- **Client Library**: ioredis
- **Connection**: Single client instance with connection pooling
- **Protocol**: Redis with TLS encryption

## ⚙️ Configuration

### Environment Variables
```env
# Required Redis Configuration
UPSTASH_REDIS_HOST=maximum-airedale-25006.upstash.io
REDIS_PORT=6379
REDIS_PASSWORD=your_password_here
REDIS_TLS_ENABLED=true
```

### Connection Options
```typescript
const redisOptions: RedisOptions = {
  host: process.env.UPSTASH_REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  connectTimeout: 30000,    // 30 second connection timeout
  commandTimeout: 10000,    // 10 second command timeout
  lazyConnect: true,        // Connect on first operation
  maxRetriesPerRequest: 1,  // Minimal retries for debugging
  enableOfflineQueue: false, // Disable queue for immediate error feedback
  retryStrategy: () => null, // No automatic retries
  family: 4,                // Force IPv4
  keepAlive: 30000,         // 30 second keep-alive
  showFriendlyErrorStack: true,
  tls: {} // Enabled when REDIS_TLS_ENABLED=true
};
```

## 📊 Cache Functions & TTL Configuration

### Product & Business Data
| Function | Cache Key Pattern | TTL | Description |
|----------|------------------|-----|-------------|
| `getAllProductsFromCache()` | `reviewit:all_products` | **1 hour** | All active products with reviews |
| `getProductDetailsFromCache()` | `reviewit:product_details:{id}` | 15 minutes | Individual product with relations |
| `getProductSearchFromCache()` | `reviewit:product_search:{hash}` | 20 minutes | Search results by query |
| `getProductReviewsFromCache()` | `reviewit:product_reviews:{id}:{public}` | 10 minutes | Product reviews |

### Review Analytics
| Function | Cache Key Pattern | TTL | Description |
|----------|------------------|-----|-------------|
| `getTopReviewersFromCache()` | `reviewit:top_reviewers:{limit}` | **30 minutes** | Top reviewers by count |
| `getLatestReviewsFromCache()` | `reviewit:reviews:latest` | **10 minutes** | Most recent reviews |
| `getPopularReviewsFromCache()` | `reviewit:reviews:popular` | **30 minutes** | Highest rated reviews |
| `getTrendingReviewsFromCache()` | `reviewit:reviews:trending` | **15 minutes** | Trending reviews (7 days) |

### Admin Analytics
| Function | Cache Key Pattern | TTL | Description |
|----------|------------------|-----|-------------|
| `getAdminRecentReviewsFromCache()` | `reviewit:admin:recent_reviews` | 5 minutes | Recent reviews for admin |
| `getAdminReviewStatsFromCache()` | `reviewit:admin:review_stats` | 10 minutes | Review statistics |
| `getAdminDashboardMetricsFromCache()` | `reviewit:admin:dashboard_metrics` | 15 minutes | Dashboard metrics |

### View Counting
| Function | Cache Key Pattern | TTL | Description |
|----------|------------------|-----|-------------|
| `incrementViewCountInRedis()` | `reviewit:view_count:{id}` | **1 hour** | Product view accumulator |
| `getCurrentViewCount()` | N/A | N/A | Combined Redis + DB count |

## 🔄 Cache Invalidation Strategy

### Automatic Invalidation
Cache is automatically invalidated when data changes:

#### Product Operations
```typescript
// Product Creation - invalidates:
await invalidateAllProductsCache();    // All products list
await invalidateSearchCache();         // All search results

// Product Updates - invalidates:
await invalidateProductCache(id);      // Specific product cache
await invalidateSearchCache();         // All search results  
await invalidateAllProductsCache();    // All products list
```

#### Review Operations
```typescript
// Review Creation/Update - invalidates:
await invalidateProductCacheOnReviewChange(productId);
await invalidateAdminCacheOnReviewChange();
```

### Manual Invalidation
```typescript
// Clear specific product cache
await invalidateProductCache(productId);

// Clear all search results
await invalidateSearchCache();

// Clear admin caches
await invalidateAdminCache();
```

## 🛡️ Error Handling & Resilience

### Health Monitoring
```typescript
// Circuit breaker pattern with 30-second health checks
async function isRedisHealthy(): Promise<boolean> {
  // Checks Redis status every 30 seconds
  // Forces reconnection if needed
  // Times out after 5 seconds
}
```

### Safe Operations
All Redis operations use timeout protection:

```typescript
// GET operations - 5 second timeout
async function safeRedisGet(key: string): Promise<string | null> {
  // Returns null on failure (cache miss)
  // Never blocks application
}

// SET operations - 2 second timeout  
async function safeRedisSet(key: string, value: string, ttl: number): Promise<void> {
  // Fails silently on error
  // Application continues normally
}

// DELETE operations - 2 second timeout
async function safeRedisDel(key: string): Promise<void> {
  // Logs deletion success
  // Continues on failure
}
```

### Graceful Degradation
- **Redis Down**: All functions fall back to direct database queries
- **Network Issues**: Timeouts prevent application blocking
- **Memory Full**: Operations fail gracefully with logging
- **Authentication Errors**: Health checks detect and log issues

## 📈 Performance Benefits

### Cache Hit Rates
```typescript
// Monitoring built-in:
trackCacheHit(key);   // Increments hit counter
trackCacheMiss(key);  // Increments miss counter
getCacheStats();      // Returns hit rate statistics
```

### Expected Performance
- **Cache Hit**: ~10-50ms response time
- **Cache Miss**: ~100-500ms (database query + cache population)
- **Hit Rate Target**: >90% for frequently accessed data

### Memory Usage
- **Product Cache**: ~1-10MB (depending on product count)
- **Search Cache**: ~5-50MB (varies with query diversity)  
- **Review Cache**: ~2-20MB (based on review volume)
- **Admin Cache**: ~1-5MB (dashboard data)

## 🔧 Usage Examples

### Basic Cache Usage
```typescript
// Get cached products (automatically handles cache miss)
const products = await getAllProductsFromCache();

// Search with caching
const searchResults = await getProductSearchFromCache("restaurant");

// Get top reviewers
const topReviewers = await getTopReviewersFromCache(6);
```

### Cache Invalidation After Data Changes
```typescript
// After creating a new product
const newProduct = await prisma.product.create({...});
await invalidateAllProductsCache();
await invalidateSearchCache();

// After updating product
const updated = await prisma.product.update({...});
await invalidateProductCache(productId);
await invalidateSearchCache();
await invalidateAllProductsCache();
```

### View Count Optimization
```typescript
// Increment view count in Redis (batched)
const newCount = await incrementViewCountInRedis(productId);

// Flush accumulated counts to database (scheduled)
await flushViewCountsToDatabase();
```

## 🐛 Troubleshooting

### Common Issues

#### Redis Connection Failed
```bash
# Check environment variables
echo $UPSTASH_REDIS_HOST
echo $REDIS_PASSWORD

# Verify Redis status in logs
tail -f logs/application.log | grep -i redis
```

#### Cache Not Updating
```typescript
// Manual cache clear for testing
await invalidateAllProductsCache();
await invalidateSearchCache();
await invalidateProductCache(productId);
```

#### High Memory Usage
```typescript
// Check view count accumulation
const keys = await redisClient.keys('reviewit:view_count:*');
console.log(`View count keys: ${keys.length}`);

// Manual flush if needed
await manualFlushViewCounts();
```

### Debug Logging
Set environment variable for detailed Redis logs:
```env
REDIS_DEBUG=true
```

Console output includes:
- Connection status
- Health check results  
- Cache hit/miss ratios
- Error details with stack traces

### Performance Monitoring
```typescript
// Get cache statistics
const stats = getCacheStats();
console.log(`Hit rate: ${stats.hitRate}%`);
console.log(`Total requests: ${stats.totalRequests}`);

// Monitor view count batching
const flushResult = await manualFlushViewCounts();
console.log(`Flushed: ${flushResult.flushed}, Errors: ${flushResult.errors}`);
```

## 🎯 Best Practices

### Cache Key Design
- **Consistent Naming**: Use `reviewit:` prefix for all keys
- **Descriptive Patterns**: Include entity type and identifiers
- **Avoid Conflicts**: Use specific separators (`:`) and avoid spaces

### TTL Selection Guidelines
- **Frequently Changed Data**: 5-10 minutes (reviews, admin stats)
- **Moderately Changed Data**: 15-30 minutes (product details, popular content)
- **Rarely Changed Data**: 1+ hours (product lists, search results)
- **Accumulated Data**: 1 hour (view counts before database flush)

### Invalidation Timing
- **Immediate**: After successful database writes
- **Batch Operations**: Invalidate related caches together
- **Error Handling**: Don't fail requests if invalidation fails

### Monitoring
- **Track Hit Rates**: Aim for >90% on frequently accessed data
- **Monitor TTL Effectiveness**: Adjust based on data change frequency
- **Watch Memory Usage**: Set up alerts for Redis memory limits
- **Log Errors**: Monitor connection issues and timeout patterns

## 🔮 Future Enhancements

### Potential Improvements
- **Cache Warming**: Pre-populate cache with popular data
- **Smart TTL**: Dynamic TTL based on data change frequency
- **Regional Caching**: Multiple Redis instances for global users
- **Cache Compression**: Reduce memory usage for large datasets
- **Background Refresh**: Update cache before TTL expires

### Metrics & Analytics
- **Prometheus Integration**: Export cache metrics
- **Dashboard**: Visual cache performance monitoring  
- **Alerting**: Automated notifications for cache issues
- **A/B Testing**: Compare cache strategies performance

---

## 📚 Related Documentation
- [Redis Configuration Guide](./redis-setup.md)
- [Database Schema](./database-schema.md)  
- [API Performance Guide](./api-performance.md)
- [Monitoring & Alerting](./monitoring.md) 