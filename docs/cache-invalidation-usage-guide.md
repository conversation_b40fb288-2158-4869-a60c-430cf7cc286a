# Cache Invalidation Usage Guide

## Overview

The cache invalidation system provides robust, event-driven cache management with circuit breaker patterns, batch operations, and comprehensive monitoring. This guide explains how to use the implemented cache invalidation functions.

## Core Functions

### Basic Cache Invalidation

```typescript
import { 
  invalidateAllProductsCache,
  invalidateSearchCache,
  invalidateAdminCache,
  invalidateProductCache
} from '@/app/util/databaseAnalytics';

// Invalidate all products cache
await invalidateAllProductsCache();

// Invalidate search cache
await invalidateSearchCache();

// Invalidate admin dashboard cache
await invalidateAdminCache();

// Invalidate specific product cache
await invalidateProductCache('product-id-123');
```

### Business-Specific Cache Invalidation

```typescript
import { 
  invalidateBusinessCaches,
  invalidateCachesOnOwnershipChange
} from '@/app/util/databaseAnalytics';

// Invalidate all caches related to a business
await invalidateBusinessCaches('business-id-123');

// Invalidate caches when product ownership changes
await invalidateCachesOnOwnershipChange('product-id-123', 'business-id-123');
```

### Safe Cache Operations with Circuit Breaker

```typescript
import { 
  safeGetFromCache,
  safeSetToCache
} from '@/app/util/databaseAnalytics';

// Safe cache get (returns null if circuit breaker is open)
const cachedData = await safeGetFromCache<MyDataType>('cache-key');

// Safe cache set (returns false if circuit breaker is open)
const success = await safeSetToCache('cache-key', data, 3600); // TTL in seconds
```

### Batch Cache Invalidation

```typescript
import { batchInvalidateCache } from '@/app/util/databaseAnalytics';

// Batch invalidate multiple keys with retry logic
const keysToInvalidate = [
  'product_details_123',
  'product_reviews_123',
  'business_analytics_456'
];

await batchInvalidateCache(keysToInvalidate);
```

## Event-Based Cache Invalidation

### Product Events

When creating or updating products, use these patterns:

```typescript
// In product creation/update routes
import { 
  invalidateAllProductsCache,
  invalidateSearchCache,
  invalidateProductCache
} from '@/app/util/databaseAnalytics';

try {
  // Your product creation/update logic here
  const product = await prisma.product.create(/* ... */);
  
  // Invalidate relevant caches
  await invalidateProductCache(product.id);
  await invalidateAllProductsCache();
  await invalidateSearchCache();
  
  console.log(`Cache invalidated for product: ${product.id}`);
} catch (cacheError) {
  console.warn('Failed to invalidate cache:', cacheError);
  // Don't fail the request if cache invalidation fails
}
```

### Review Events

When reviews are created or updated:

```typescript
import { invalidateAggregatedCachesOnReviewChange } from '@/app/util/databaseAnalytics';

// After review creation/update
await invalidateAggregatedCachesOnReviewChange();
```

### Business Claim Events

When handling business claims:

```typescript
import { 
  invalidateAdminCache,
  invalidateCachesOnOwnershipChange,
  invalidateBusinessCaches
} from '@/app/util/databaseAnalytics';

// When claim is submitted
await invalidateAdminCache();

// When claim is approved
if (status === 'APPROVED') {
  await invalidateCachesOnOwnershipChange(productId, businessId);
  await invalidateBusinessCaches(businessId);
}
```

## Client-Side Cache Synchronization

### Using Enhanced Jotai Atoms

The `allProductsAtom` now automatically triggers cache revalidation:

```typescript
import { useAtom } from 'jotai';
import { allProductsAtom } from '@/app/store/store';

function MyComponent() {
  const [products, setProducts] = useAtom(allProductsAtom);
  
  // When you update products, cache revalidation is triggered automatically
  const updateProducts = (newProducts) => {
    setProducts(newProducts); // This triggers cache revalidation
  };
  
  return (
    // Your component JSX
  );
}
```

### Manual Cache Revalidation

```typescript
// Trigger Next.js cache revalidation
const revalidateCache = async (path: string) => {
  try {
    const response = await fetch(`/api/revalidate?path=${encodeURIComponent(path)}`);
    if (response.ok) {
      console.log(`Cache revalidated for path: ${path}`);
    }
  } catch (error) {
    console.warn('Failed to revalidate cache:', error);
  }
};

// Usage
await revalidateCache('/browse');
await revalidateCache('/product/123');
```

## Monitoring and Health Checks

### Cache Health Monitoring

```typescript
import { checkCacheHealth } from '@/app/util/databaseAnalytics';

const healthStatus = await checkCacheHealth();
console.log('Cache Health:', healthStatus);

// Response structure:
// {
//   isHealthy: boolean,
//   stats: {
//     hits: number,
//     misses: number,
//     hitRate: number,
//     totalRequests: number,
//     errors: number,
//     lastError: Date | null
//   },
//   circuitBreakerStatus: {
//     isOpen: boolean,
//     failureCount: number,
//     lastFailureTime: Date | null,
//     threshold: number,
//     timeout: number
//   }
// }
```

### Admin Health Endpoint

```bash
# Check cache health via API
GET /api/admin/cache/health

# Response:
{
  "success": true,
  "data": {
    "isHealthy": true,
    "stats": { /* ... */ },
    "circuitBreakerStatus": { /* ... */ },
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### Cache Testing

```bash
# Test cache operations
POST /api/admin/cache/test
{
  "action": "test-cache-operations"
}

# Test cache invalidation
POST /api/admin/cache/test
{
  "action": "test-invalidation"
}

# Test batch invalidation
POST /api/admin/cache/test
{
  "action": "test-batch-invalidation"
}

# Test health check
POST /api/admin/cache/test
{
  "action": "health-check"
}
```

## Best Practices

### 1. Always Use Try-Catch for Cache Operations

```typescript
try {
  await invalidateProductCache(productId);
} catch (cacheError) {
  console.warn('Cache invalidation failed:', cacheError);
  // Don't fail the main operation
}
```

### 2. Use Appropriate Cache Invalidation Scope

```typescript
// For product changes - invalidate product-specific caches
await invalidateProductCache(productId);

// For business changes - invalidate business-specific caches
await invalidateBusinessCaches(businessId);

// For ownership changes - invalidate both product and business caches
await invalidateCachesOnOwnershipChange(productId, businessId);
```

### 3. Batch Operations for Multiple Keys

```typescript
// Instead of multiple individual invalidations
// await invalidateProductCache(id1);
// await invalidateProductCache(id2);
// await invalidateProductCache(id3);

// Use batch invalidation
const keys = [
  `reviewit:v2:product_details:${id1}`,
  `reviewit:v2:product_details:${id2}`,
  `reviewit:v2:product_details:${id3}`
];
await batchInvalidateCache(keys);
```

### 4. Monitor Circuit Breaker Status

```typescript
// Check circuit breaker status before critical operations
const health = await checkCacheHealth();
if (!health.isHealthy || health.circuitBreakerStatus.isOpen) {
  console.warn('Cache is unhealthy, using database fallback');
  // Implement fallback logic
}
```

## Error Handling

The cache invalidation system includes comprehensive error handling:

- **Circuit Breaker**: Automatically opens after 5 failures, closes after 30 seconds
- **Graceful Degradation**: Cache failures don't break application functionality
- **Retry Logic**: Batch operations include exponential backoff retry
- **Fallback Strategies**: Individual deletions if batch operations fail

## Cache Key Versioning

Cache keys are versioned (`v2`) to handle schema changes:

```typescript
// Old cache keys (v1) are automatically invalidated when version changes
// New cache keys use v2 format:
// reviewit:v2:product_details:123
// reviewit:v2:business_analytics:456:2024-01-01:2024-01-31
```

This ensures cache consistency during application updates and schema migrations.