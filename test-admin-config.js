// Test script to verify admin configuration
require('dotenv').config();

// Import the admin functions
const { isAdmin, validateAdminConfig, getAdminEmailsList } = require('./src/app/config/admin.ts');

console.log('Testing Admin Configuration...\n');

// Test validation
console.log('1. Validating admin config:');
const isValid = validateAdminConfig();
console.log(`Config valid: ${isValid}\n`);

// Test admin email list
console.log('2. Admin emails configured:');
const adminEmails = getAdminEmailsList();
console.log(adminEmails);
console.log(`Total admin emails: ${adminEmails.length}\n`);

// Test isAdmin function
console.log('3. Testing isAdmin function:');
const testEmails = [
    '<EMAIL>',
    '<EMAIL>', // Test case sensitivity
    '<EMAIL>',
    '<EMAIL>',
    '',
    null
];

testEmails.forEach(email => {
    const result = isAdmin(email);
    console.log(`isAdmin("${email}"): ${result}`);
});