# Cache Invalidation Strategy Guide

## Core Principles
1. **Atomic Operations**: Cache updates must be atomic to prevent partial state
2. **Layer Synchronization**: Invalidate both Redis cache and client-state atoms
3. **Event-Based Triggers**: Link cache clears to specific business events

## Product Events
```typescript
// Example from <mcfile name="route.ts" path="/src/app/api/create/product/route.ts"></mcfile>
await invalidateAllProductsCache();
await invalidateSearchCache();
```

## Business Claim Handling
```typescript
// New utility function to add
const invalidateBusinessCaches = async (businessId: string) => {
  await redisService.del(`business_${businessId}_analytics`);
  await invalidateAdminCache();
};
```

## Client-State Synchronization
```typescript
// From <mcfile name="store.ts" path="/src/app/store/store.ts"></mcfile>
export const allProductsAtom = atom<PaginatedProductList>(
  (get) => get(allProductsStore),
  (_, set, update) => {
    set(allProductsStore, update);
    // Trigger Redis refresh on client update
    fetch('/api/revalidate?path=/all-products');
  }
);
```

## Safety Measures
1. **Circuit Breakers**: Automatic fallback to stale data
2. **Batch Operations**: Use Redis pipelines for multi-key operations
3. **Versioned Keys**: `allproducts_v2` format for schema changes

[Full Implementation Checklist](#implementation-checklist)