# Project Documentation Guide

<style>
  /* Documentation Page Styles - Use these exact styles for all documentation pages */
  .docs-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }
  .docs-nav {
    position: fixed;
    left: 1rem;
    top: 100px;
    width: 200px;
    background: white;
    border-right: 1px solid #e5e7eb;
    padding: 1rem;
    height: calc(100vh - 100px);
    overflow-y: auto;
  }
  .docs-content {
    padding-left: 220px;
  }
  .prose {
    color: #374151;
    line-height: 1.75;
  }
  .prose h1 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #1f2937;
  }
  .prose h2 {
    font-size: 2rem;
    font-weight: bold;
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
    color: #1f2937;
  }
  .prose h3 {
    font-size: 1.75rem;
    font-weight: bold;
    margin-top: 2rem;
    margin-bottom: 0.75rem;
    color: #1f2937;
  }
  .prose p {
    margin-bottom: 1rem;
  }
  .prose ul {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }
  .prose li {
    margin-bottom: 0.5rem;
  }
  .status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    margin-left: 0.5rem;
  }
  .status-complete {
    background-color: #dcfce7;
    color: #166534;
  }
  .status-in-progress {
    background-color: #fef3c7;
    color: #92400e;
  }
  .status-placeholder {
    background-color: #f3f4f6;
    color: #6b7280;
  }
  .back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: #3b82f6;
    color: white;
    padding: 0.75rem;
    border-radius: 50%;
    text-decoration: none;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  .breadcrumb {
    margin-bottom: 2rem;
    color: #6b7280;
    font-size: 0.875rem;
  }
  .breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
  }
  .breadcrumb a:hover {
    text-decoration: underline;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .docs-nav {
      display: none;
    }
    .docs-content {
      padding-left: 0;
    }
    .docs-container {
      padding: 1rem;
    }
  }

  /* Documentation Index Page Styles */
  .docs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
  }
  .docs-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }
  .docs-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
  .docs-card h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: #1f2937;
  }
  .docs-card p {
    margin-bottom: 1rem;
    color: #6b7280;
    font-size: 0.9rem;
  }
  .docs-card a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
  }
  .docs-card a:hover {
    text-decoration: underline;
  }
  .intro-section {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 0.75rem;
  }
</style>

<div class="docs-container">
  <nav class="docs-nav">
    <h3 class="text-lg font-semibold mb-2">Table of Contents</h3>
    <ul class="space-y-1 text-sm text-gray-600">
      <li><a href="#documentation-status-overview" class="hover:text-blue-600">Status Overview</a></li>
      <li><a href="#documentation-sections" class="hover:text-blue-600">Documentation Sections</a></li>
      <li><a href="#implementation-priority" class="hover:text-blue-600">Implementation Priority</a></li>
      <li><a href="#next-steps" class="hover:text-blue-600">Next Steps</a></li>
      <li><a href="#style-guide" class="hover:text-blue-600">Style Guide</a></li>
    </ul>
  </nav>

  <div class="docs-content prose">

This guide outlines the steps to create comprehensive documentation for the entire project. The documentation is organized into sections that align with the existing admin documentation portal at `/admin/docs`.

## Documentation Status Overview

**Existing Basic Documentation (Needs Enhancement):**
- Project Overview - Basic structure exists
- User Management - Basic CRUD operations documented
- Product Management - Basic CRUD operations documented
- Review Management - Placeholder exists
- Bug Reports - Placeholder exists
- Promotions - Placeholder exists
- API Routes - Placeholder exists
- Owner/Business Section - Placeholder exists
- User-Facing Routes - Structure exists, needs detailed content
- Core Concepts - Structure exists, needs detailed content

## Documentation Sections

### 1. Admin Section (Enhance Existing Pages)

**Current Location:** `/admin/docs/`

#### 1.1 Project Overview (`/admin/docs/project-overview`)
- [x] **Basic Overview:** ✅ Already documented
- [x] **Enhanced Content Needed:**
    - [x] Detailed architecture overview
    - [x] Technology stack deep dive
    - [x] Development workflow
    - [x] Security considerations

#### 1.2 User Management (`/admin/docs/user-management`)
- [x] **Basic CRUD Operations:** ✅ Already documented
- [x] **Advanced Features to Document:**
    - [x] User search and filtering capabilities
    - [x] User analytics and engagement metrics
    - [x] Role-based access control
    - [x] User activity tracking
    - [x] Bulk user operations

#### 1.3 Product Management (`/admin/docs/product-management`)
- [x] **Basic CRUD Operations:** ✅ Already documented
- [x] **Advanced Features to Document:**
    - [x] Product search and filtering system
    - [x] Product analytics dashboard
    - [x] Image management and upload process
    - [x] Product approval workflows
    - [x] Category management
    - [x] Bulk product operations

#### 1.4 Review Management (`/admin/docs/review-management`)
- [x] **Complete Documentation Needed:**
    - [x] Review moderation interface
    - [x] Review analytics and distribution
    - [x] Flagged content management
    - [x] Review response system
    - [x] Review verification processes

#### 1.5 Bug Reports (`/admin/docs/bug-reports`)
- [x] **Complete Documentation Needed:**
    - [x] Bug report submission process
    - [x] Status tracking and management
    - [x] Priority assignment
    - [x] Resolution workflows
    - [x] Bug analytics and reporting

#### 1.6 Promotions (`/admin/docs/promotions`)
- [x] **Complete Documentation Needed:**
    - [x] Promotion creation and management
    - [x] Campaign scheduling
    - [x] Target audience selection
    - [x] Performance tracking
    - [x] Promotion analytics

### 2. API Routes (`/admin/docs/api-routes`)

**Current Status:** Placeholder exists, needs complete documentation

- [ ] **Authentication Endpoints:**
    - [ ] User login, registration, and session management
    - [ ] Middleware documentation
    - [ ] JWT token handling
- [ ] **User API Endpoints:**
    - [ ] CRUD operations for user management
    - [ ] User profile endpoints
    - [ ] User analytics endpoints
- [ ] **Product API Endpoints:**
    - [ ] Product creation, retrieval, updates, and deletion
    - [ ] Product search and filtering endpoints
    - [ ] Product analytics endpoints
- [ ] **Review API Endpoints:**
    - [ ] Review submission, editing, and deletion
    - [ ] Review moderation endpoints
    - [ ] Review analytics endpoints
- [ ] **Business API Endpoints:**
    - [ ] Business profile management
    - [ ] Product claim endpoints
    - [ ] Business analytics endpoints

### 3. Owner/Business Section (`/admin/docs/owner-business`)

**Current Status:** Placeholder exists, needs complete documentation

- [ ] **Business Dashboard:**
    - [ ] Dashboard overview and navigation
    - [ ] Key metrics and KPIs
    - [ ] Real-time analytics display
- [ ] **Business Analytics:**
    - [ ] Comprehensive analytics dashboard
    - [ ] Charts and visualization components
    - [ ] Business insights and reporting
- [ ] **Business Product Management:**
    - [ ] Adding and editing business products
    - [ ] Product approval process
    - [ ] Inventory management
- [ ] **Promotions Management:**
    - [ ] Creating promotional campaigns
    - [ ] Campaign scheduling and targeting
    - [ ] Performance tracking
- [ ] **Business Reviews Management:**
    - [ ] Viewing customer reviews
    - [ ] Responding to reviews
    - [ ] Review analytics
- [ ] **Business Settings:**
    - [ ] Account configuration
    - [ ] Business profile setup
    - [ ] Notification preferences
- [ ] **Product/Business Claims:**
    - [ ] Claim submission process
    - [ ] Verification requirements
    - [ ] Claim status tracking

### 4. User-Facing Routes (`/admin/docs/user-facing`)

**Current Status:** Structure exists, needs detailed content creation

#### 4.1 User Profile (`/admin/docs/user-facing/profile`)
- [ ] **Create Detailed Documentation:**
    - [ ] Profile components and layout
    - [ ] Settings and preferences
    - [ ] User review history
    - [ ] Public profile view
    - [ ] Privacy controls

#### 4.2 Browsing and Search (`/admin/docs/user-facing/browsing`)
- [ ] **Create Comprehensive Search Documentation:**
    - [ ] Tag-based search functionality
    - [ ] Business name search
    - [ ] Fuzzy search capabilities
    - [ ] Advanced search filters
    - [ ] Search result navigation
    - [ ] Data flow patterns (fresh pulls vs. cached data)
    - [ ] Search performance optimization

#### 4.3 Review Submission (`/admin/docs/user-facing/reviews`)
- [ ] **Create Detailed Review Documentation:**
    - [ ] Multi-step review submission form
    - [ ] Review validation and requirements
    - [ ] Image upload functionality
    - [ ] Review editing and deletion
    - [ ] Review moderation process

#### 4.4 Home Page (`/admin/docs/user-facing/home`)
- [ ] **Create Home Page Documentation:**
    - [ ] Layout and component structure
    - [ ] Featured products section
    - [ ] Recent activity display
    - [ ] Navigation and user flows
    - [ ] SEO optimization

### 5. Core Concepts (`/admin/docs/core-concepts`)

**Current Status:** Structure exists, needs detailed content creation

#### 5.1 Component Library (`/admin/docs/core-concepts/components`)
- [ ] **Create Component Documentation:**
    - [ ] Reusable UI components catalog
    - [ ] Component props and usage examples
    - [ ] Styling guidelines
    - [ ] Component testing patterns

#### 5.2 Database Schema (`/admin/docs/core-concepts/database`)
- [ ] **Create Database Documentation:**
    - [ ] Prisma schema overview
    - [ ] Model relationships
    - [ ] Database migrations
    - [ ] Query optimization

#### 5.3 Project Structure (`/admin/docs/core-concepts/project-structure`)
- [ ] **Create Project Structure Documentation:**
    - [ ] Folder organization
    - [ ] File naming conventions
##### Admin Privilege Assignment

Admin status is assigned automatically via Clerk webhooks. When a user signs up or updates their email:

1. Clerk sends a webhook event to `/api/webhooks/clerk`
2. The handler verifies the event signature
3. For `user.created` or `user.updated` events:
   ```typescript
   if (event.type === 'user.created' || event.type === 'user.updated') {
     const email = event.data.email_addresses[0].email_address;
     const isAdmin = adminEmails.includes(email);
     // Update user record with isAdmin flag
   }
   ```
4. The email is checked against `src/admin.ts` which exports an array of admin emails:
   ```typescript
   // src/admin.ts
   export const adminEmails = [
     '<EMAIL>',
     '<EMAIL>'
   ];
   ```
5. If matched, the user's database record is updated with `isAdmin: true`

This system ensures only predefined emails gain admin privileges through automated verification.
    - [ ] Data flow architecture
    - [ ] Code organization patterns

#### 5.4 Caching Strategy (`/admin/docs/core-concepts/caching`)
- [ ] **Create Comprehensive Caching Documentation:**
    - [ ] Redis caching implementation
    - [ ] Cache data types and TTL configurations
    - [ ] Cache invalidation strategies
    - [ ] Permanent cache management
    - [ ] Cache warming processes
    - [ ] Performance monitoring

#### 5.5 Deployment (`/admin/docs/core-concepts/deployment`)
- [ ] **Create Deployment Documentation:**
    - [ ] Environment setup
    - [ ] Build process
    - [ ] Environment variables
    - [ ] Production deployment
    - [ ] Monitoring and logging

## Implementation Priority

### High Priority (Complete Missing Documentation)
1. **Core Concepts Sub-pages** - Create all missing sub-pages with detailed content
2. **User-Facing Routes Sub-pages** - Create all missing sub-pages with detailed content
3. **API Routes** - Complete comprehensive API documentation
4. **Owner/Business Section** - Complete business feature documentation

### Medium Priority (Enhance Existing Documentation)
1. **User Management** - Add advanced features documentation
2. **Product Management** - Add advanced features documentation
3. **Project Overview** - Enhance with architectural details

### Low Priority (Complete Basic Documentation)
1. **Review Management** - Complete basic documentation
2. **Bug Reports** - Complete basic documentation
3. **Promotions** - Complete basic documentation

## Next Steps

1. **Create Missing Sub-directories and Pages:**
   - Create sub-directories under `/admin/docs/core-concepts/`
   - Create sub-directories under `/admin/docs/user-facing/`
   - Create individual page.tsx files for each sub-section

2. **Content Development:**
   - Start with high-priority sections
   - Use existing component implementations as reference
   - Include code examples and screenshots where applicable

3. **Documentation Maintenance:**
   - Establish update procedures
   - Create templates for consistent formatting
   - Set up review processes for accuracy

## Style Guide <span id="style-guide" class="text-2xl font-bold"></span>

### Visual Hierarchy
- Use a 5-column typographic scale (h1: 2.5rem, h2: 2rem, h3: 1.75rem)
- Section spacing: 3rem between major sections, 1.5rem between subsections
- Color palette:
  - Primary text: `#1f2937`
  - Secondary text: `#4b5563`
  - Accent: `#3b82f6`

### Navigation Requirements
1. All documentation pages must include:
   - Table of Contents with anchor links
   - "Back to Top" button in bottom-right corner
   - Breadcrumb navigation showing section hierarchy

### Component Standards
- Status badges use predefined color classes:
  ```html
  <span class="status-badge status-complete">Complete</span>
  <span class="status-badge status-in-progress">In Progress</span>
  ```
- Code blocks use prism.js theme with line numbers
- Interactive elements use neutral colors with hover states

### Responsive Rules
- Mobile (< 768px): Hide navigation sidebar, show hamburger menu
- Tablet (768-1024px): Reduce padding, stack content sections
- Desktop (>1024px): Fixed sidebar with scrolling content

## Documentation Writing Guidelines

### Documentation Writing Guidelines

**Target Audience:** Documentation should be accessible to developers of all skill levels, from beginners to experienced professionals.

**Documentation Format:** All documentation pages should be simple HTML-like pages without state management or fancy animations. Focus on clean, readable content with basic styling.

#### 1. **Clear and Simple Language**
- Use plain English and avoid unnecessary jargon
- Define technical terms when first introduced
- Write in active voice when possible
- Keep sentences concise and focused

#### 2. **Logical Structure and Navigation**
- Start each section with a brief overview
- Use consistent heading hierarchy (H1, H2, H3)
- Include a table of contents for longer documents
- Provide clear navigation between related sections
- Use bullet points and numbered lists for step-by-step processes

#### 3. **Practical Examples and Code Samples**
- Include working code examples for all features
- Provide before/after comparisons when relevant
- Use realistic data in examples
- Comment code samples to explain key concepts
- Include expected outputs and results

#### 4. **Visual Aids and Screenshots**
- Add screenshots for UI-related documentation
- Use diagrams to explain complex workflows
- Include flowcharts for decision processes
- Highlight important UI elements with annotations

#### 5. **Consistent Formatting**
- Use consistent code block formatting with language specification
- Apply consistent styling for file paths, function names, and variables
- Use callout boxes for important notes, warnings, and tips
- Maintain consistent spacing and indentation
- Keep pages simple with static content - no complex state management or animations
- Focus on clean, readable HTML structure with basic CSS styling

#### 6. **Error Handling and Troubleshooting**
- Document common errors and their solutions
- Include troubleshooting sections for complex features
- Provide debugging tips and best practices
- Link to relevant error logs or diagnostic tools

#### 7. **Accessibility and Inclusivity**
- Use descriptive link text (avoid "click here")
- Provide alternative text for images and diagrams
- Ensure good color contrast in visual elements
- Use inclusive language and examples

#### 8. **Regular Updates and Maintenance**
- Keep documentation synchronized with code changes
- Add version information and last updated dates
- Remove or update outdated information promptly
- Gather feedback from users and incorporate improvements

### Documentation Templates

#### Standard Documentation Page Template:
```jsx
import React from 'react';

const YourDocumentationPage = () => {
    return (
        <div id="top" className="max-w-6xl mx-auto p-8">
            <nav className="fixed left-4 top-24 w-48 bg-white border-r border-gray-200 p-4 h-[calc(100vh-6rem)] overflow-y-auto hidden lg:block">
                <h3 className="text-lg font-semibold mb-4 text-gray-900">Table of Contents</h3>
                <ul className="space-y-2 text-sm">
                    <li><a href="#section-1" className="text-blue-600 hover:text-blue-800 hover:underline">Section 1</a></li>
                    <li><a href="#section-2" className="text-blue-600 hover:text-blue-800 hover:underline">Section 2</a></li>
                    <li><a href="#section-3" className="text-blue-600 hover:text-blue-800 hover:underline">Section 3</a></li>
                    {/* Add more navigation items as needed */}
                </ul>
            </nav>

            <div className="lg:pl-56 text-gray-700 leading-relaxed">
                <div className="mb-8 text-sm text-gray-500">
                    <a href="/admin/docs" className="text-blue-600 hover:underline">Documentation</a> / <span>Your Page Title</span>
                </div>

                <h1 className="text-4xl font-bold mb-6 text-gray-900">
                    Your Page Title
                    <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">Complete</span>
                </h1>
                <p className="mb-4">Brief description of what this documentation covers.</p>

                <h2 id="section-1" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Section 1</h2>
                <p className="mb-4">Content for section 1...</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Subsection 1.1</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Item 1</strong> - Description</li>
                    <li><strong>Item 2</strong> - Description</li>
                </ul>

                <h2 id="section-2" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Section 2</h2>
                <p className="mb-4">Content for section 2...</p>

                {/* Add more sections as needed */}
            </div>

            <a
                href="#top"
                className="fixed bottom-8 right-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
            >
                ↑
            </a>
        </div>
    );
};

export default YourDocumentationPage;
```

#### Documentation Index Page Template:
```jsx
import React from 'react';

const DocumentationIndexPage = () => {
    return (
        <div className="max-w-4xl mx-auto p-8">
            <div className="text-center mb-12 p-8 bg-gradient-to-br from-slate-50 to-slate-200 rounded-xl">
                <h1 className="text-4xl font-bold mb-6 text-gray-900">Documentation Title</h1>
                <p className="text-lg text-gray-600">Brief description of the documentation section.</p>
            </div>

            <section className="mb-12">
                <h2 className="text-3xl font-bold mb-4 pb-2 border-b border-gray-200 text-gray-900">Section Title</h2>
                <p className="text-lg mb-6 text-gray-600">Description of this section.</p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">
                            Page Title
                            <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Complete</span>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">Description of what this page covers.</p>
                        <a href="/path/to/page" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">View Documentation →</a>
                    </div>

                    {/* Add more cards as needed */}
                </div>
            </section>
        </div>
    );
};

export default DocumentationIndexPage;
```

**Implementation Notes:**
- Use Tailwind CSS classes for all styling (compatible with Next.js 13+ App Router Server Components)
- Create each documentation page as a simple React component that returns static JSX content
- Avoid useState, useEffect, onClick handlers, or complex interactions - keep pages as Server Components
- Do NOT use styled-jsx as it requires Client Components
- Do NOT use onClick handlers as they require Client Components
- Focus on clean, readable content with Tailwind CSS styling
- Always include the table of contents navigation for longer pages
- Use appropriate status badges to indicate completion status
- Fixed sidebar navigation should be hidden on mobile (lg:block hidden)
- Use semantic HTML with proper heading hierarchy for accessibility
- For back-to-top functionality, use href="#top" instead of onClick handlers
- Add id="top" to the main container for back-to-top links to work

#### API Documentation Template:
```markdown
# API Endpoint Name

## Endpoint Details
- **Method:** GET/POST/PUT/DELETE
- **URL:** `/api/endpoint`
- **Authentication:** Required/Optional

## Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| param1    | string | Yes | Description |

## Request Example
```json
{
  "example": "request"
}
```

## Response Example
```json
{
  "example": "response"
}
```

## Error Codes
- 400: Bad Request - Description
- 401: Unauthorized - Description
```