generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Product {
  id               String                   @id @default(uuid())
  address          String?
  createdDate      DateTime                 @default(now())
  description      String
  display_image    String
  images           String[]
  videos           String[]
  links            String[]
  name             String                   @unique
  tags             String[]
  openingHrs       String?
  closingHrs       String?
  telephone        String?
  website          String[]
  rating           Int                      @default(3)
  hasOwner         Boolean?
  ownerId          String?
  createdById      String
  isDeleted        Boolean                  @default(false)
  email            String?
  businessId       String?
  featuredPosition Int?
  rating1Star      Int                      @default(0)
  rating2Stars     Int                      @default(0)
  rating3Stars     Int                      @default(0)
  rating4Stars     Int                      @default(0)
  rating5Stars     Int                      @default(0)
  updatedAt        DateTime?
  descriptionLastUpdatedAt DateTime? // Tracks description updates
  imagesLastUpdatedAt      DateTime? // Tracks image updates
  viewCount        Int                      @default(0)
  openingDays      String[]                 @default([])
  latitude         Float?
  longitude        Float?
  business         Business?                @relation(fields: [businessId], references: [id])
  createdBy        User                     @relation(fields: [createdById], references: [id])
  analytics        ProductAnalytics?
  claims           ProductClaim?
  viewEvents       ProductViewEvent[]
  reviews          Review[]
  userInteractions UserProductInteraction[]
  promotions       Promotion[]

  @@index([isDeleted, rating])
  @@index([viewCount])
  @@index([featuredPosition])
  @@index([businessId])
}

model VoteCount {
  id             String @id @default(uuid())
  reviewId       String @unique
  helpfulVotes   Int    @default(0)
  unhelpfulVotes Int    @default(0)
  review         Review @relation(fields: [reviewId], references: [id])
}

model Review {
  id                String            @id @default(uuid())
  body              String
  createdDate       DateTime          @default(now())
  helpfulVotes      Int               @default(0)
  unhelpfulVotes    Int               @default(0)
  rating            Int
  title             String
  productId         String
  userId            String
  isVerified        Boolean?
  verifiedBy        String?
  isPublic          Boolean           @default(true)
  images            String[]
  videos            String[]
  links             String[]
  createdBy         String?
  isDeleted         Boolean?          @default(false)
  verifiedAt        DateTime?
  ownerRespondedAt  DateTime?
  comments          Comment[]
  moderationHistory ModerationEvent[]
  product           Product           @relation(fields: [productId], references: [id])
  user              User              @relation(fields: [userId], references: [id])
  voteCount         VoteCount?
  likedBy           User[]            @relation("ReviewLike")
  reports           ReviewReport[]

  @@unique([title, userId, productId], name: "unique_review")
  @@index([isDeleted, isVerified])
  @@index([rating])
  @@index([createdDate])
  @@index([productId])
  @@index([userId])
}

model Comment {
  id          String        @id @default(uuid())
  body        String
  createdDate DateTime      @default(now())
  reviewId    String
  userId      String
  isDeleted   Boolean?      @default(false)
  parentId    String?
  downvotes   Int           @default(0)
  upvotes     Int           @default(0)
  parent      Comment?      @relation("CommentReplies", fields: [parentId], references: [id])
  replies     Comment[]     @relation("CommentReplies")
  review      Review        @relation(fields: [reviewId], references: [id])
  user        User          @relation(fields: [userId], references: [id])
  votes       CommentVote[]
}

model CommentVote {
  id          String   @id @default(uuid())
  commentId   String
  userId      String
  voteType    String   @default("UP")
  createdAt   DateTime @default(now())
  clerkUserId String
  comment     Comment  @relation(fields: [commentId], references: [id])
  user        User     @relation(fields: [userId], references: [id])

  @@unique([commentId, clerkUserId])
  @@index([commentId])
  @@index([userId])
  @@index([clerkUserId])
}

model User {
  id                  String                   @id @default(uuid())
  userName            String                   @unique
  avatar              String?
  createdDate         DateTime                 @default(now())
  email               String                   @unique
  firstName           String
  lastName            String
  clerkUserId         String                   @unique
  isDeleted           Boolean?                 @default(false)
  bio                 String?
  lastLoginAt         DateTime?
  loginCount          Int                      @default(0)
  role                String                   @default("USER")
  status              String                   @default("ACTIVE")
  suspendedReason     String?
  suspendedUntil      DateTime?
  adminActions        AdminAction[]
  bugReports          BugReport[]
  resolvedBugs        BugReport[]              @relation("BugReportResolver")
  businesses          Business[]
  comments            Comment[]
  commentVotes        CommentVote[]
  moderationEvents    ModerationEvent[]
  product             Product[]
  reviewedClaims      ProductClaim[]           @relation("ClaimReviewer")
  productClaims       ProductClaim[]
  productViews        ProductViewEvent[]
  reviews             Review[]
  productInteractions UserProductInteraction[]
  likedReviews        Review[]                 @relation("ReviewLike")
  submittedReports    ReviewReport[]           @relation("ReportSubmitter")
  resolvedReports     ReviewReport[]           @relation("ReportResolver")
  createdPromotions   Promotion[]

  @@index([role, status])
  @@index([isDeleted, status])
  @@index([lastLoginAt])
}

model Business {
  id                 String    @id @unique @default(cuid())
  ownerId            String
  subscriptionStatus String
  subscriptionExpiry DateTime?
  createdDate        DateTime? @default(now())
  isVerified         Boolean?  @default(false)
  ownerName          String?
  owner              User      @relation(fields: [ownerId], references: [id])
  products           Product[]
  promotions         Promotion[]
  analytics  BusinessAnalytics? // Add relation to the new model
}

model BusinessAnalytics {
    id                            String    @id @default(uuid())
    businessId                    String    @unique
    averageReviewResponseTime     Float?    // In hours
    negativeReviewResponseRate    Float?    // Percentage of negative reviews responded to
    productContentFreshnessScore  Float?    // A score from 0 to 100
    lastCalculated                DateTime  @default(now())
    business                      Business  @relation(fields: [businessId], references: [id])

    @@index([businessId])
  }

model AdminAction {
  id          String     @id @default(uuid())
  adminId     String
  actionType  String
  targetId    String
  targetType  String
  description String
  createdAt   DateTime   @default(now())
  bugReportId String?
  admin       User       @relation(fields: [adminId], references: [id])
  BugReport   BugReport? @relation(fields: [bugReportId], references: [id])

  @@index([adminId])
  @@index([targetId])
  @@index([targetType])
  @@index([createdAt])
}

model ModerationEvent {
  id        String   @id @default(uuid())
  reviewId  String
  adminId   String
  action    String
  reason    String?
  createdAt DateTime @default(now())
  admin     User     @relation(fields: [adminId], references: [id])
  review    Review   @relation(fields: [reviewId], references: [id])

  @@index([reviewId])
  @@index([adminId])
  @@index([createdAt])
  @@index([action])
}

model ProductViewEvent {
  id          String   @id @default(uuid())
  productId   String
  userId      String?
  sessionId   String
  timestamp   DateTime @default(now())
  duration    Int?
  source      String?
  deviceType  String?
  isNewUser   Boolean  @default(false)
  isThrottled Boolean  @default(false)
  product     Product  @relation(fields: [productId], references: [id])
  user        User?    @relation(fields: [userId], references: [id])

  @@index([productId])
  @@index([userId])
  @@index([sessionId])
  @@index([timestamp])
}

model ProductAnalytics {
  id                  String   @id @default(uuid())
  productId           String   @unique
  totalViews          Int      @default(0)
  uniqueVisitors      Int      @default(0)
  averageViewDuration Float    @default(0)
  peakHours           Json
  weekdayStats        Json
  lastUpdated         DateTime @default(now())
  product             Product  @relation(fields: [productId], references: [id])

  @@index([productId])
  @@index([totalViews])
}

model UserProductInteraction {
  id               String   @id @default(uuid())
  userId           String
  productId        String
  lastViewed       DateTime @default(now())
  viewCount        Int      @default(0)
  hasReviewed      Boolean  @default(false)
  hasLiked         Boolean  @default(false)
  averageTimeSpent Float    @default(0)
  product          Product  @relation(fields: [productId], references: [id])
  user             User     @relation(fields: [userId], references: [id])

  @@unique([userId, productId])
  @@index([userId])
  @@index([productId])
  @@index([lastViewed])
}

model ProductClaim {
  id              String    @id @default(uuid())
  productId       String    @unique
  userId          String
  status          String    @default("PENDING")
  contactInfo     String
  additionalInfo  String
  images          String[]
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  reviewedAt      DateTime?
  reviewedBy      String?
  rejectionReason String?
  product         Product   @relation(fields: [productId], references: [id])
  reviewer        User?     @relation("ClaimReviewer", fields: [reviewedBy], references: [id])
  user            User      @relation(fields: [userId], references: [id])

  @@index([status])
  @@index([userId])
  @@index([createdAt])
}

model BugReport {
  id               String          @id @default(uuid())
  title            String
  description      String
  browser          String?
  device           String?
  mobile_os        String?
  status           BugReportStatus @default(OPEN)
  resolved_at      DateTime?
  resolved_by      String?
  resolution_notes String?
  created_at       DateTime        @default(now())
  updated_at       DateTime        @updatedAt
  reporterId       String?
  adminActions     AdminAction[]
  reporter         User?           @relation(fields: [reporterId], references: [id])
  resolver         User?           @relation("BugReportResolver", fields: [resolved_by], references: [id])

  @@index([status])
  @@index([reporterId])
  @@index([resolved_by])
  @@index([created_at])
}

enum BugReportStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
  WONT_FIX
}

model ReviewReport {
  id         String    @id @default(uuid())
  reviewId   String
  userId     String
  reason     String
  status     String    @default("PENDING")
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  resolvedAt DateTime?
  resolvedBy String?
  notes      String?

  review   Review @relation(fields: [reviewId], references: [id])
  user     User   @relation("ReportSubmitter", fields: [userId], references: [id])
  resolver User?  @relation("ReportResolver", fields: [resolvedBy], references: [id])

  @@index([reviewId])
  @@index([userId])
  @@index([status])
}

model Promotion {
  id                  String   @id @default(uuid())
  title               String
  description         String
  startDate           DateTime
  endDate             DateTime
  discountPercentage  Float?
  discountAmount      Float?
  promotionCode       String?
  isActive            Boolean  @default(true)
  image               String?  // Cloudinary URL
  imagePublicId       String?  // For Cloudinary management
  
  // Relationships
  productId           String
  businessId          String
  createdById         String
  
  // Analytics
  viewCount           Int      @default(0)
  clickCount          Int      @default(0)
  conversionCount     Int      @default(0)
  
  // Timestamps
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  
  // Relations
  product             Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  business            Business @relation(fields: [businessId], references: [id], onDelete: Cascade)
  createdBy           User     @relation(fields: [createdById], references: [id])
  
  // Analytics tracking
  analytics           PromotionAnalytics?
  
  @@index([productId])
  @@index([businessId])
  @@index([isActive])
  @@index([startDate, endDate])
  @@index([createdAt])
  @@index([createdById])
}

model PromotionAnalytics {
  id                  String   @id @default(uuid())
  promotionId         String   @unique
  
  // Daily metrics
  dailyViews          Json     @default("{}")  // { "2024-01-01": 150, "2024-01-02": 200 }
  dailyClicks         Json     @default("{}")  // { "2024-01-01": 15, "2024-01-02": 25 }
  dailyConversions    Json     @default("{}")  // { "2024-01-01": 2, "2024-01-02": 3 }
  
  // Performance metrics
  ctr                 Float    @default(0) // Click-through rate
  conversionRate      Float    @default(0)
  averageOrderValue   Float?
  
  // Audience insights
  topReferrers        Json     @default("{}")  // { "google": 100, "facebook": 50 }
  deviceBreakdown     Json     @default("{}")  // { "mobile": 60, "desktop": 40 }
  
  lastUpdated         DateTime @default(now())
  
  promotion           Promotion @relation(fields: [promotionId], references: [id], onDelete: Cascade)
  
  @@index([promotionId])
}
