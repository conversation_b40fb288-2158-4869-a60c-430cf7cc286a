# Vaultgy Integration Guide for Next.js Merchants

This guide will walk you through integrating Vaultgy subscription products into your Next.js website, allowing you to sell access to your content through subscriptions.

## Table of Contents

1. [Account Setup](#account-setup)
2. [Product Configuration](#product-configuration)
3. [Integration Methods](#integration-methods)
   - [Client-Side Integration](#client-side-integration)
   - [Server Components Integration](#server-components-integration)
   - [Server Actions Integration](#server-actions-integration)
4. [Handling Redirects](#handling-redirects)
5. [Webhook Configuration](#webhook-configuration)
6. [Protecting Content](#protecting-content)
7. [Testing Your Integration](#testing-your-integration)
8. [Going Live](#going-live)
9. [Troubleshooting](#troubleshooting)

## Account Setup

1. **Create a Merchant Account**
   - Sign up at [merchant.vaultgy.com](https://merchant.vaultgy.com)
   - Complete your business profile
   - Verify your email address

2. **Set Up Payment Information**
   - Add your bank account for payouts
   - Configure tax settings
   - Set up your payout schedule

## Product Configuration

1. **Create Subscription Products**
   - Log in to your Vaultgy merchant dashboard
   - Navigate to "Products" > "Add New Product"
   - Enter product details (name, description, images)
   - Configure pricing and billing options
   - Set up subscription terms and conditions
   - Save and publish your product

2. **Note Your IDs**
   - Copy your Merchant ID from your dashboard
   - Note the Product ID for each product you want to offer

## Integration Methods

Next.js offers several ways to integrate Vaultgy. Choose the method that best fits your application architecture.

### Client-Side Integration

For client-side rendering, use the Script component from Next.js:

```jsx
// app/subscribe/page.jsx
'use client';

import Script from 'next/script';
import { useEffect } from 'react';

export default function SubscribePage() {
  return (
    <div className="subscription-container">
      <h1>Subscribe to Premium Access</h1>
      <div id="vaultgy-embed"></div>
      
      <Script
        src="https://embed.vaultgy.com/v1/embed.js"
        data-merchant-id="YOUR_MERCHANT_ID"
        data-product-id="PRODUCT_ID"
        data-theme="light"
        data-primary-color="#2563eb"
        data-button-text="Subscribe Now"
        strategy="afterInteractive"
      />
    </div>
  );
}
```

### Server Components Integration

For server components, you can use a direct link approach:

```jsx
// app/pricing/page.jsx
export default function PricingPage() {
  return (
    <div className="pricing-container">
      <h1>Choose Your Plan</h1>
      
      <div className="pricing-cards">
        <div className="pricing-card">
          <h2>Basic Plan</h2>
          <p>$9.99/month</p>
          <ul>
            <li>Feature 1</li>
            <li>Feature 2</li>
          </ul>
          <a 
            href={`https://vaultgy.com/checkout/BASIC_PRODUCT_ID?merchantId=YOUR_MERCHANT_ID&redirect_url=${encodeURIComponent('https://yoursite.com/success')}`}
            className="subscribe-button"
          >
            Subscribe Now
          </a>
        </div>
        
        <div className="pricing-card">
          <h2>Premium Plan</h2>
          <p>$19.99/month</p>
          <ul>
            <li>All Basic Features</li>
            <li>Premium Feature 1</li>
            <li>Premium Feature 2</li>
          </ul>
          <a 
            href={`https://vaultgy.com/checkout/PREMIUM_PRODUCT_ID?merchantId=YOUR_MERCHANT_ID&redirect_url=${encodeURIComponent('https://yoursite.com/success')}`}
            className="subscribe-button premium"
          >
            Subscribe Now
          </a>
        </div>
      </div>
    </div>
  );
}
```

### Server Actions Integration

For advanced integration, you can use Next.js Server Actions to create a custom checkout flow:

```jsx
// app/actions.ts
'use server'

import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

export async function createCheckoutSession(productId: string) {
  const response = await fetch('https://api.vaultgy.com/v1/checkout/sessions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.VAULTGY_API_KEY}`
    },
    body: JSON.stringify({
      product_id: productId,
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/cancel`
    })
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error('Failed to create checkout session');
  }
  
  // Store session ID in cookies for verification later
  cookies().set('checkout_session', data.data.session_id, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 60 * 30, // 30 minutes
    path: '/'
  });
  
  // Redirect to checkout
  redirect(data.data.url);
}
```

Then use this server action in your component:

```jsx
// app/subscribe/page.tsx
'use client';

import { createCheckoutSession } from '../actions';

export default function SubscribePage() {
  return (
    <div className="subscription-container">
      <h1>Subscribe to Premium Access</h1>
      
      <div className="subscription-options">
        <div className="subscription-option">
          <h2>Monthly Plan</h2>
          <p>$9.99/month</p>
          <form action={createCheckoutSession.bind(null, 'MONTHLY_PRODUCT_ID')}>
            <button type="submit" className="subscribe-button">Subscribe Monthly</button>
          </form>
        </div>
        
        <div className="subscription-option">
          <h2>Annual Plan</h2>
          <p>$99.99/year (Save 16%)</p>
          <form action={createCheckoutSession.bind(null, 'ANNUAL_PRODUCT_ID')}>
            <button type="submit" className="subscribe-button">Subscribe Annually</button>
          </form>
        </div>
      </div>
    </div>
  );
}
```

## Handling Redirects

Create success and cancel pages to handle redirects from Vaultgy:

```jsx
// app/success/page.tsx
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

async function verifySession(sessionId: string) {
  // Verify the session with Vaultgy API
  const response = await fetch(`https://api.vaultgy.com/v1/checkout/sessions/${sessionId}`, {
    headers: {
      'Authorization': `Bearer ${process.env.VAULTGY_API_KEY}`
    }
  });
  
  return response.json();
}

export default async function SuccessPage({
  searchParams,
}: {
  searchParams: { session_id: string };
}) {
  const sessionId = searchParams.session_id;
  const storedSessionId = cookies().get('checkout_session')?.value;
  
  // Verify the session ID matches what we stored
  if (!sessionId || sessionId !== storedSessionId) {
    redirect('/');
  }
  
  // Verify with Vaultgy
  const session = await verifySession(sessionId);
  
  if (!session.success || session.data.status !== 'completed') {
    redirect('/payment-failed');
  }
  
  // Clear the session cookie
  cookies().delete('checkout_session');
  
  return (
    <div className="success-container">
      <h1>Subscription Successful!</h1>
      <p>Thank you for subscribing to our service.</p>
      <p>Your subscription is now active and you can access all premium content.</p>
      <a href="/dashboard" className="button">Go to Dashboard</a>
    </div>
  );
}
```

## Webhook Configuration

Set up webhooks to receive real-time updates about subscription events:

1. **Create a Webhook Endpoint**

```jsx
// app/api/vaultgy-webhook/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { verifyWebhookSignature } from '@/lib/vaultgy';
import { updateUserSubscription } from '@/lib/db';

export async function POST(request: NextRequest) {
  const body = await request.text();
  const signature = request.headers.get('vaultgy-signature') || '';
  
  try {
    // Verify webhook signature
    const isValid = verifyWebhookSignature(
      body,
      signature,
      process.env.VAULTGY_WEBHOOK_SECRET!
    );
    
    if (!isValid) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }
    
    const event = JSON.parse(body);
    
    // Handle different event types
    switch (event.type) {
      case 'subscription.created':
        await updateUserSubscription(event.data.customer_id, {
          status: 'active',
          plan: event.data.product_id,
          expiresAt: new Date(event.data.current_period_end * 1000)
        });
        break;
        
      case 'subscription.updated':
        await updateUserSubscription(event.data.customer_id, {
          status: event.data.status,
          plan: event.data.product_id,
          expiresAt: new Date(event.data.current_period_end * 1000)
        });
        break;
        
      case 'subscription.cancelled':
        await updateUserSubscription(event.data.customer_id, {
          status: 'cancelled',
          expiresAt: new Date(event.data.current_period_end * 1000)
        });
        break;
        
      // Handle other event types
    }
    
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 400 }
    );
  }
}

export const config = {
  api: {
    bodyParser: false, // Don't parse the body, we need the raw body for signature verification
  },
};
```

2. **Register Your Webhook URL**
   - Go to your Vaultgy merchant dashboard
   - Navigate to "Settings" > "Webhooks"
   - Add your webhook URL (e.g., `https://yoursite.com/api/vaultgy-webhook`)
   - Select the events you want to receive
   - Save your webhook configuration

## Protecting Content

Implement middleware to protect premium content:

```jsx
// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifySubscriptionStatus } from './lib/auth';

// Paths that require subscription
const PROTECTED_PATHS = [
  '/premium',
  '/courses',
  '/downloads',
];

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;
  
  // Check if the path requires subscription
  if (PROTECTED_PATHS.some(prefix => path.startsWith(prefix))) {
    const session = request.cookies.get('session')?.value;
    
    if (!session) {
      return NextResponse.redirect(new URL('/login', request.url));
    }
    
    // Verify subscription status
    const hasActiveSubscription = await verifySubscriptionStatus(session);
    
    if (!hasActiveSubscription) {
      return NextResponse.redirect(new URL('/subscribe', request.url));
    }
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/premium/:path*',
    '/courses/:path*',
    '/downloads/:path*',
  ],
};
```

## Testing Your Integration

1. **Enable Test Mode**
   - Log in to your Vaultgy merchant dashboard
   - Enable "Test Mode" in your settings

2. **Test the Subscription Flow**
   - Use test card number `4242 4242 4242 4242` with any future expiry date and CVC
   - Complete a test purchase
   - Verify webhook delivery
   - Check redirect functionality
   - Confirm the subscription appears in your merchant dashboard

3. **Test Content Protection**
   - Verify that protected content is only accessible to subscribers
   - Test subscription expiration and renewal

## Going Live

1. **Disable Test Mode**
   - Log in to your Vaultgy merchant dashboard
   - Disable "Test Mode" in your settings

2. **Update Environment Variables**
   - Ensure all environment variables are properly set for production
   - Double-check API keys and webhook secrets

3. **Deploy Your Application**
   - Deploy your Next.js application to your production environment
   - Verify that all integration points work correctly in production

## Troubleshooting

### Common Issues

1. **Embed Not Displaying**
   - Check that your Merchant ID and Product ID are correct
   - Verify that the embed script is loading properly
   - Check browser console for errors

2. **Webhook Not Receiving Events**
   - Verify your webhook URL is correctly registered in Vaultgy
   - Check that your server is accessible from the internet
   - Ensure your webhook signature verification is working correctly

3. **Redirect Issues**
   - Verify that your redirect URLs are properly URL-encoded
   - Check that your success and cancel pages are handling parameters correctly

### Getting Help

If you encounter any issues with your Vaultgy integration:

1. **Documentation**
   - Refer to the [Vaultgy Merchant Integration Guide](https://docs.vaultgy.com/merchant-integration-guide)
   - Check the [Developer API Guide](https://docs.vaultgy.com/developer-api-guide)

2. **Support**
   - Contact Vaultgy <NAME_EMAIL>
   - Include your Merchant ID and detailed information about the issue

---

By following this guide, you should have a fully functional Vaultgy subscription integration in your Next.js application, allowing you to sell access to your premium content through subscriptions.
