# Product Analytics Implementation Guide

## Overview
This guide outlines the implementation of a comprehensive product analytics system for Review It. The system will track user interactions with products and provide valuable insights to business owners.

## Tasks

### 1. Interface Definitions [✓]
- [✓] Create `iProductViewEvent` interface for tracking individual view events
- [✓] Create `iProductAnalytics` interface for aggregated analytics
- [✓] Create `iUserProductInteraction` interface for tracking user-specific interactions
- [✓] Add analytics-related fields to existing `iProduct` interface

### 2. Cookie-Based Tracking System [✓]
- [✓] Implement sophisticated view counting with throttling
- [✓] Track unique visitors vs returning visitors
- [✓] Store user interaction history
- [✓] Implement session tracking

### 3. Analytics Data Collection [Partial]
- [✓] Track product view timestamps
- [✓] Track referral sources
- [✓] Track device types
- [✓] Track product view duration
- [✓] Track user interaction events (clicks)
- [✓] Track scroll depth
- [✓] Track time of day/week patterns

### 4. Database Schema Updates [✓]
- [✓] Add analytics tables to Prisma schema
- [✓] Create relations between models
- [✓] Add indexes for efficient querying
- [✓] Create migrations for new tables

### 5. API Endpoints [✓]
- [✓] Create endpoint for logging view events (`/api/analytics/log-view`)
- [✓] Create endpoint for retrieving analytics (`/api/analytics/get-stats`)
- [✓] Create endpoint for updating view durations (`/api/analytics/update-view`)
- [✓] Create endpoint for custom events (`/api/analytics/log-event`)
- [✓] Add business owner-specific analytics access controls


### 7. Privacy and Security [Partial]
- [✓] Secure cookie implementation
- [✓] User authorization for analytics access
- [ ] Add consent management
- [ ] Ensure GDPR compliance
- [ ] Add data retention policies

## Implementation Progress

### ✓ Step 1: Interface Definitions (Completed)
We've added the following interfaces to `src/app/util/Interfaces.tsx`:
```typescript
export interface iProductViewEvent {
  id?: string;
  productId: string;
  userId?: string;          // Optional, for authenticated users
  sessionId: string;        // For anonymous tracking
  timestamp: Date;
  duration?: number;        // Time spent viewing in seconds
  source?: string;         // Where the user came from
  deviceType?: string;     // Mobile/Desktop/Tablet
  isNewUser: boolean;
  isThrottled: boolean;    // Whether this view was counted in stats
}

export interface iProductAnalytics {
  id?: string;
  productId: string;
  totalViews: number;
  uniqueVisitors: number;
  averageViewDuration: number;
  peakHours: Record<number, number>;  // Hour -> View count
  weekdayStats: Record<string, number>; // Day -> View count
  lastUpdated: Date;
}

export interface iUserProductInteraction {
  id?: string;
  userId: string;
  productId: string;
  lastViewed: Date;
  viewCount: number;
  hasReviewed: boolean;
  hasLiked: boolean;
  averageTimeSpent: number;
}
```

### ✓ Step 2: Database Schema Updates (Completed)
Added new models to `prisma/schema.prisma`:

```prisma
model ProductViewEvent {
  id          String   @id @default(uuid())
  productId   String
  userId      String?
  sessionId   String
  timestamp   DateTime @default(now())
  duration    Int?     // Duration in seconds
  source      String?
  deviceType  String?
  isNewUser   Boolean  @default(false)
  isThrottled Boolean  @default(false)
  product     Product  @relation(fields: [productId], references: [id])
  user        User?    @relation(fields: [userId], references: [id])

  @@index([productId])
  @@index([userId])
  @@index([sessionId])
  @@index([timestamp])
}

model ProductAnalytics {
  id                String   @id @default(uuid())
  productId         String   @unique
  totalViews        Int      @default(0)
  uniqueVisitors    Int      @default(0)
  averageViewDuration Float  @default(0)
  peakHours         Json     // Store as JSON: { "hour": count }
  weekdayStats      Json     // Store as JSON: { "day": count }
  lastUpdated       DateTime @default(now())
  product           Product  @relation(fields: [productId], references: [id])

  @@index([productId])
  @@index([totalViews])
}

model UserProductInteraction {
  id              String   @id @default(uuid())
  userId          String
  productId       String
  lastViewed      DateTime @default(now())
  viewCount       Int      @default(0)
  hasReviewed     Boolean  @default(false)
  hasLiked        Boolean  @default(false)
  averageTimeSpent Float   @default(0)
  user            User     @relation(fields: [userId], references: [id])
  product         Product  @relation(fields: [productId], references: [id])

  @@unique([userId, productId])
  @@index([userId])
  @@index([productId])
  @@index([lastViewed])
}
```

### ✓ Step 3: Cookie-Based Tracking System (Completed)
Implemented in `src/app/util/analyticsUtils.ts`:

1. Session Management
   - Persistent session tracking with 30-day cookies
   - Session ID generation using UUID
   - Secure cookie storage (httpOnly, secure in production)

2. View Throttling
   - Sophisticated count throttling (every 3 visits)
   - First-visit detection
   - Tracking of both anonymous and authenticated users

### ✓ Step 4: API Endpoints (Completed)
Created multiple API endpoints:

1. `/api/analytics/log-view` - Records product view events with:
   - User identification (authenticated or anonymous)
   - Source tracking (referrer)
   - Device type detection
   - Session management

2. `/api/analytics/get-stats` - Retrieves analytics with:
   - Authorization checks
   - Date range filtering
   - Comprehensive metrics
   - View event listing

3. `/api/analytics/update-view` - Updates view duration and engagement metrics:
   - Duration tracking
   - Average duration calculation
   - User interaction updates

4. `/api/analytics/log-event` - Records custom interaction events:
   - Event type tracking
   - Metadata support
   - User and session association

### ✓ Step 5: Server-Side Implementation (Completed)
Added view tracking to product pages:

- Server-side view recording in product detail pages
- User agent and referrer detection
- Device type identification
- Automatic analytics aggregation

### ✓ Step 6: Enhanced Client-Side Tracking (Completed)

#### 6.1: Client-Side Hooks [✓]
Created client-side tracking hooks to enhance analytics capabilities:

1. `src/app/hooks/useProductAnalytics.ts`:
   - Initial view logging
   - Duration tracking
   - Scroll depth monitoring
   - Click interaction counts
   - Custom event logging

#### 6.2: Client Component Integration [✓]
Integrated the hooks into key product components:

1. Updated `src/components/product/ProductPageClient.tsx` to:
   - Initialize analytics tracking
   - Track duration, scroll, and clicks
   - Log specific interactions (gallery views, contact info)

## Current Implementation Tasks

### Step 7: Analytics Dashboard Implementation

#### 7.1: Dashboard Components
Create the following components for the analytics dashboard:

1. `src/components/analytics/AnalyticsDashboard.tsx` - Main dashboard container
2. `src/components/analytics/ViewsChart.tsx` - Time-series chart for views
3. `src/components/analytics/VisitorMetrics.tsx` - Display visitor statistics
4. `src/components/analytics/DeviceBreakdown.tsx` - Chart for device types
5. `src/components/analytics/EngagementMetrics.tsx` - Display engagement metrics

#### 7.2: Dashboard Page
Create the dashboard page:

1. `src/app/(routes)/business/analytics/page.tsx` - Server component for the analytics dashboard
2. `src/app/(routes)/business/analytics/[productId]/page.tsx` - Product-specific analytics

#### 7.3: Data Processing Utilities
Add utilities for processing and formatting analytics data:

1. `src/app/util/analyticsProcessing.ts` - Functions for data aggregation and processing

### Step 8: Privacy and Compliance

#### 8.1: Consent Management
Implement cookie consent management:

1. Create `src/components/ui/CookieConsent.tsx` - Banner for gathering consent
2. Update `src/app/layout.tsx` to include the consent banner
3. Add consent storage and checking in analytics utilities

#### 8.2: GDPR Compliance
Implement GDPR compliance features:

1. Create data export API endpoint
2. Create data deletion API endpoint
3. Add privacy policy page

#### 8.3: Data Retention
Implement data retention policies:

1. Add scheduled job for anonymizing old data
2. Add configuration for retention periods
3. Implement data archiving functionality

## Next Action Items

1. Create basic dashboard analytics components for the business owner view
2. Implement the cookie consent banner for GDPR compliance
3. Create data export and deletion endpoints for user privacy
4. Develop the data visualization components for the analytics dashboard