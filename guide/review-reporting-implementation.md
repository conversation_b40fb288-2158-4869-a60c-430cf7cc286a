# Review Reporting System Implementation Guide

## Overview
This guide outlines the implementation of a review reporting system that allows users to report inappropriate or problematic reviews. The system will integrate with the existing moderation system and provide administrators with tools to manage reported reviews.

## Prerequisites
- Existing review system
- Admin dashboard
- User authentication system
- Moderation system

## Implementation Checklist

### 1. Database Schema Updates
- [x] Add ReviewReport model to `prisma/schema.prisma`
- [x] Add necessary indexes for performance
- [x] Add relations to User and Review models
- [x] Create migration file
- [x] Apply migration

### 2. API Endpoints

#### User Endpoints
- [x] Create POST `/api/reviews/[reviewId]/report` in `src/app/api/reviews/[reviewId]/report/route.ts`
  - [x] Input validation
  - [x] Authentication check
  - [x] Rate limiting
  - [x] Duplicate report check
  - [x] Error handling
  - [x] Success response

#### Admin Endpoints
- [x] Create GET `/api/admin/reports` in `src/app/api/admin/reports/route.ts`
  - [x] Pagination
  - [x] Filtering (status, date, review)
  - [x] Sorting
  - [x] Authentication/Authorization
  - [x] Error handling

- [x] Create PUT `/api/admin/reports/[reportId]` in `src/app/api/admin/reports/[reportId]/route.ts`
  - [x] Status update
  - [x] Resolution notes
  - [x] Authentication/Authorization
  - [x] Validation
  - [x] Error handling

- [x] Create GET `/api/admin/reports/stats` in `src/app/api/admin/reports/stats/route.ts`
  - [x] Report counts by status
  - [x] Recent reports
  - [x] Authentication/Authorization
  - [x] Error handling

### 3. UI Components

#### User Components
- [x] Create ReportButton component in `src/app/components/ReportButton.tsx`
  - [x] Button styling
  - [x] Click handler
  - [x] Loading state
  - [x] Error handling

- [x] Create ReportModal component in `src/app/components/ReportModal.tsx`
  - [x] Form fields
  - [x] Validation
  - [x] Submit handler
  - [x] Loading state
  - [x] Success/Error messages
  - [x] Close handler

- [x] Add report button to ReviewCard component in `src/app/components/ReviewCard.tsx`
- [x] Add report button to ReviewBox component in `src/app/components/ReviewBox.tsx`

#### Admin Components
- [x] Create ReportsTable component in `src/app/components/admin/ReportsTable.tsx`
  - [x] Table structure
  - [x] Pagination
  - [x] Sorting
  - [x] Filtering
  - [x] Loading state
  - [x] Error handling

- [x] Create ReportDetails component in `src/app/components/admin/ReportDetails.tsx`
  - [x] Report information display
  - [x] Review preview
  - [x] Action buttons
  - [x] Status update
  - [x] Notes field
  - [x] Loading state
  - [x] Error handling

- [x] Create ReportsDashboard component in `src/app/components/admin/ReportsDashboard.tsx`
  - [x] Statistics display
  - [x] Recent reports list
  - [x] Quick actions
  - [x] Loading state
  - [x] Error handling

### 4. Admin Dashboard Integration
- [x] Add Reports section to admin navigation in `src/app/(routes)/admin/layout.tsx`
- [x] Create reports page layout in `src/app/(routes)/admin/reports/page.tsx`
- [x] Add Reports item to quickActions in `src/components/admin/quick-actions.tsx`
- [x] Update dashboard statistics in `src/app/(routes)/admin/dashboard/page.tsx`

### 5. Interface Updates
- [x] Add ReviewReport interface to `src/app/util/Interfaces.tsx`
- [x] Update iReview interface with reports relation
- [x] Add report-related types for API requests and responses

### 6. Error Handling
- [x] Implement error boundaries
- [x] Add error logging
- [x] Create error messages
- [x] Add retry mechanisms
- [x] Implement fallback UI

### 7. Notifications
- [ ] Add admin notification for new reports
- [ ] Add user notification for report status updates
- [ ] Implement notification preferences
- [ ] Add email notifications (if applicable)

> Note: Notifications implementation will be handled in a separate phase after core functionality is complete.

### 8. Security
- [x] Implement rate limiting
- [x] Add input sanitization
- [x] Implement authorization checks
- [x] Add audit logging
- [x] Implement spam prevention

### 9. Performance
- [x] Add database indexes
- [x] Implement caching
- [x] Add pagination
- [x] Optimize queries
- [x] Add loading states

## Implementation Details

### Database Schema
```prisma
model ReviewReport {
  id          String   @id @default(uuid())
  reviewId    String
  userId      String
  reason      String
  status      String   @default("PENDING") // PENDING, REVIEWED, RESOLVED
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  resolvedAt  DateTime?
  resolvedBy  String?
  notes       String?
  
  review      Review   @relation(fields: [reviewId], references: [id])
  user        User     @relation(fields: [userId], references: [id])
  resolver    User?    @relation("ReportResolver", fields: [resolvedBy], references: [id])
  
  @@index([reviewId])
  @@index([userId])
  @@index([status])
}
```

### API Routes Structure

#### User Routes
```typescript
// POST /api/reviews/[reviewId]/report
interface ReportReviewRequest {
  reason: string;
  additionalNotes?: string;
}

interface ReportReviewResponse {
  success: boolean;
  reportId: string;
  message: string;
}
```

#### Admin Routes
```typescript
// GET /api/admin/reports
interface GetReportsRequest {
  page?: number;
  limit?: number;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface GetReportsResponse {
  reports: ReviewReport[];
  total: number;
  page: number;
  totalPages: number;
}

// PUT /api/admin/reports/[reportId]
interface UpdateReportRequest {
  status: 'PENDING' | 'REVIEWED' | 'RESOLVED';
  notes?: string;
}

interface UpdateReportResponse {
  success: boolean;
  report: ReviewReport;
}
```

### Component Structure

#### ReportButton
```typescript
interface ReportButtonProps {
  reviewId: string;
  onReport: (reportId: string) => void;
}
```

#### ReportModal
```typescript
interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  reviewId: string;
  onSubmit: (data: ReportReviewRequest) => Promise<void>;
}
```

#### ReportsTable
```typescript
interface ReportsTableProps {
  reports: ReviewReport[];
  onStatusChange: (reportId: string, status: string) => Promise<void>;
  onViewDetails: (reportId: string) => void;
}
```

#### ReportDetails
```typescript
interface ReportDetailsProps {
  report: ReviewReport;
  onStatusUpdate: (status: string, notes?: string) => Promise<void>;
  onClose: () => void;
}
```

## Error Handling Strategy

### API Errors
- 400: Bad Request (invalid input)
- 401: Unauthorized (not logged in)
- 403: Forbidden (insufficient permissions)
- 404: Not Found (report/review doesn't exist)
- 429: Too Many Requests (rate limit exceeded)
- 500: Internal Server Error

### UI Error States
- Form validation errors
- Network errors
- Permission errors
- Rate limit errors
- Server errors

## Security Considerations

### Rate Limiting
- Maximum 5 reports per user per hour
- Maximum 20 reports per user per day
- IP-based rate limiting for unauthenticated requests

### Authorization
- Only authenticated users can submit reports
- Only admins can view and manage reports
- Users can only report reviews once
- Users cannot report their own reviews

### Input Validation
- Reason field: required, max 500 characters
- Notes field: optional, max 1000 characters
- Status updates: must be valid enum values
- Report ID: must be valid UUID

## Performance Considerations

### Database
- Index on frequently queried fields
- Composite indexes for common query patterns
- Pagination for large result sets
- Efficient joins with related tables

### API
- Response caching where appropriate
- Pagination for large datasets
- Efficient filtering and sorting
- Optimized database queries

### UI
- Lazy loading of components
- Pagination for large tables
- Optimistic updates
- Debounced search inputs

## Implementation Order

1. Database Schema
   - Create and apply migration
   - Update related models

2. API Endpoints
   - Implement user report endpoint
   - Implement admin endpoints
   - Add error handling

3. UI Components
   - Create ReportButton
   - Create ReportModal
   - Create admin components
   - Add to admin dashboard

4. Error Handling
   - Implement error boundaries
   - Add error logging
   - Create error messages

5. Security
   - Add rate limiting
   - Implement authorization
   - Add input validation

6. Performance
   - Add indexes
   - Implement caching
   - Optimize queries

7. Notifications
   - Add admin notifications
   - Add user notifications

8. Testing
   - Manual testing
   - Edge case testing
   - Performance testing

## Notes
- Keep the implementation modular for easy updates
- Follow existing code style and patterns
- Document any deviations from standard patterns
- Consider future scalability
- Maintain backward compatibility

## Key Files to Modify

### Database
- `prisma/schema.prisma` - Add ReviewReport model

### API Routes
- `src/app/api/reviews/[reviewId]/report/route.ts` - New file for report submission
- `src/app/api/admin/reports/route.ts` - New file for admin reports list
- `src/app/api/admin/reports/[reportId]/route.ts` - New file for report management
- `src/app/api/admin/reports/stats/route.ts` - New file for report statistics

### UI Components
- `src/app/components/ReportButton.tsx` - New file for report button component
- `src/app/components/ReportModal.tsx` - New file for report form modal
- `src/app/components/ReviewCard.tsx` - Add report functionality
- `src/app/components/ReviewBox.tsx` - Add report functionality
- `src/app/components/admin/ReportsTable.tsx` - New file for reports management table
- `src/app/components/admin/ReportDetails.tsx` - New file for report details view
- `src/app/components/admin/ReportsDashboard.tsx` - New file for reports dashboard

### Admin Dashboard
- `src/app/(routes)/admin/layout.tsx` - Add reports navigation item
- `src/app/(routes)/admin/reports/page.tsx` - New file for reports admin page
- `src/components/admin/quick-actions.tsx` - Add report management action
- `src/app/(routes)/admin/dashboard/page.tsx` - Add report statistics

### Type Definitions
- `src/app/util/Interfaces.tsx` - Add ReviewReport interface and update related types

## Integration Points

### ReviewCard Component
Add the report button to the ReviewCard component after the review stats section:

```tsx
// src/app/components/ReviewCard.tsx
<div className="mt-2 sm:mt-0">
  <ReviewStats review={review} setReview={() => { setReview(review) }} />
  <ReportButton reviewId={review.id!} onReport={(reportId) => {
    toast.success('Review reported successfully');
  }} />
</div>
```

### Admin Navigation
Add the reports navigation item to the admin layout:

```tsx
// src/app/(routes)/admin/layout.tsx
const navItems = [
  // Existing items
  {
    path: "/admin/reports",
    label: "Reports",
    icon: <Flag className="w-5 h-5" />,
  },
  // Other items
];
```

### QuickActions Component
Update the quick actions component to include reports management:

```tsx
// src/components/admin/quick-actions.tsx
const quickActions = [
  // Existing actions
  {
    title: "Report Management",
    icon: Flag,
    href: "/admin/reports",
    description: "Manage user reports on reviews",
    color: "text-red-500",
  },
  // Other actions
];
```

### Dashboard Statistics
Update the dashboard statistics to include report statistics:

```tsx
// src/app/(routes)/admin/dashboard/page.tsx
const stats = [
  // Existing stats
  {
    title: "Reports",
    value: reportCount,
    description: "Total reports received",
  },
  // Other stats
]; 