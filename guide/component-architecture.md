# Review-It Component Architecture Guide

This guide provides a detailed overview of the component architecture in the Review-It project, including component hierarchy, data flow, and best practices.

## Table of Contents

1. [Component Hierarchy](#component-hierarchy)
2. [Component Types](#component-types)
3. [Data Flow Between Components](#data-flow-between-components)
4. [State Management](#state-management)
5. [Component Props](#component-props)
6. [Component Lifecycle](#component-lifecycle)
7. [Error Handling](#error-handling)
8. [Performance Optimization](#performance-optimization)
9. [Accessibility](#accessibility)
10. [Testing Components](#testing-components)
11. [Best Practices](#best-practices)

## Component Hierarchy

The Review-It application follows a hierarchical component structure:

```
App
├── Layout
│   ├── Header
│   │   ├── Navigation
│   │   └── UserMenu
│   ├── Sidebar (conditional)
│   └── Footer
├── Page Components
│   ├── HomePage
│   ├── BrowsePage
│   │   ├── ArrangeByPanel
│   │   └── ProductCard
│   ├── ProductPage
│   │   ├── ProductShowcase
│   │   ├── ReviewList
│   │   └── ReviewForm
│   └── UserProfilePage
└── Modal Components (portals)
    ├── ImageModal
    └── ConfirmationModal
```

## Component Types

### Page Components

Page components represent entire pages in the application and are located in the `src/app/(routes)` directory. They are responsible for:

- Fetching data using React Query
- Managing page-level state
- Composing UI components
- Handling page-level events

Example: `src/app/(routes)/browse/page.tsx`

```tsx
"use client";
import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { useSearchParams, useRouter } from "next/navigation";
import { iProduct } from "@/app/util/Interfaces";
import { getProducts } from "@/app/util/serverFunctions";
import ArrangeByPanel from "@/app/components/ArrangeByPanel";
import LoadingSpinner from "@/app/components/LoadingSpinner";
import ProductCard from "@/app/components/ProductCard";
import { allProductsAtom } from "@/app/store/store";
import { iCalculatedRating } from "@/app/util/Interfaces";
import { calculateAverageReviewRating } from "@/app/util/calculateAverageReviewRating";
import { useAuth } from "@clerk/nextjs";
import { Search } from "lucide-react";

const Page = () => {
  // Component implementation
};

export default Page;
```

### UI Components

UI components are reusable building blocks that form the visual elements of the application. They are located in the `src/components/ui` directory and include:

- Button
- DropdownMenu
- Input
- Textarea
- Card
- Modal
- Toast

Example: `src/components/ui/button.tsx`

```tsx
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
```

### Feature Components

Feature components implement specific features of the application and are located in the `src/app/components` directory. They include:

- ProductCard
- ShareButton
- ReviewList
- ReviewForm
- ProductShowcase

Example: `src/app/components/ProductCard.tsx`

```tsx
"use client";
import React, { useState, useEffect } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { Eye } from "lucide-react";
import { MdEdit } from "react-icons/md";
import { Button } from "@/components/ui/button";
import { ShareButtonWrapper } from "./ShareButtonWrapper";
import { iProduct, iReview, iCalculatedRating } from "../util/Interfaces";
import { calculateAverageReviewRating } from "../util/calculateAverageReviewRating";
import { generateShareMetadata } from "../lib/shareUtils";

interface ProductCardProps {
  reviews?: iReview[] | null;
  options: {
    showLatestReview: boolean;
    size: string;
    showWriteReview: boolean;
    showClaimThisProduct: boolean;
  };
  product?: iProduct | null;
  currentUserId: string | null;
}

const ProductCard: React.FC<ProductCardProps> = ({
  reviews,
  options,
  product,
  currentUserId,
}) => {
  // Component implementation
};

export default ProductCard;
```

### Layout Components

Layout components define the structure of the application and are located in the `src/app` directory. They include:

- Layout
- Header
- Footer
- Sidebar

Example: `src/app/layout.tsx`

```tsx
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ClerkProvider } from "@clerk/nextjs";
import { Toaster } from "@/components/ui/toaster";
import { Providers } from "./providers";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Review It - Your Voice, Your Choice",
  description:
    "Review It is a platform where you can share and read reviews on anything.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={inter.className}>
          <Providers>{children}</Providers>
          <Toaster />
        </body>
      </html>
    </ClerkProvider>
  );
}
```

## Data Flow Between Components

### Parent-Child Data Flow

Data flows from parent to child components through props:

```tsx
// Parent component
const ParentComponent = () => {
  const [data, setData] = useState([]);
  
  return (
    <ChildComponent 
      data={data} 
      onDataChange={setData} 
    />
  );
};

// Child component
const ChildComponent = ({ data, onDataChange }) => {
  // Use data and call onDataChange when needed
};
```

### Global State Management

For global state, the application uses Jotai atoms:

```tsx
// Define atom in store.ts
import { atom } from 'jotai';
import { iProduct } from '../util/Interfaces';

export const allProductsAtom = atom<iProduct[]>([]);

// Use atom in components
import { useAtom } from 'jotai';
import { allProductsAtom } from '../store/store';

const Component = () => {
  const [products, setProducts] = useAtom(allProductsAtom);
  
  // Use products and setProducts
};
```

### Server-Client Data Flow

For server-client data flow, the application uses React Query:

```tsx
// Define query function in serverFunctions.ts
export async function getProducts() {
  // Fetch products from API
  return products;
}

// Use query in components
import { useQuery } from '@tanstack/react-query';
import { getProducts } from '../util/serverFunctions';

const Component = () => {
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['products'],
    queryFn: getProducts,
    refetchOnWindowFocus: false,
  });
  
  // Use data, isLoading, isError, error
};
```

## State Management

### Local State

For component-specific state, use React's useState and useReducer:

```tsx
const Component = () => {
  const [count, setCount] = useState(0);
  
  return (
    <button onClick={() => setCount(count + 1)}>
      Count: {count}
    </button>
  );
};
```

### Global State

For application-wide state, use Jotai atoms:

```tsx
// Define atom
import { atom } from 'jotai';

export const themeAtom = atom('light');

// Use atom
import { useAtom } from 'jotai';
import { themeAtom } from '../store/store';

const Component = () => {
  const [theme, setTheme] = useAtom(themeAtom);
  
  return (
    <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
      Toggle Theme
    </button>
  );
};
```

### Server State

For server state, use React Query:

```tsx
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

const Component = () => {
  const queryClient = useQueryClient();
  
  // Query data
  const { data, isLoading } = useQuery({
    queryKey: ['data'],
    queryFn: fetchData,
  });
  
  // Mutate data
  const mutation = useMutation({
    mutationFn: updateData,
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['data'] });
    },
  });
  
  return (
    <button onClick={() => mutation.mutate(newData)}>
      Update Data
    </button>
  );
};
```

## Component Props

### Prop Types

Define prop types using TypeScript interfaces:

```tsx
interface ButtonProps {
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  label,
  onClick,
  variant = 'primary',
  disabled = false,
}) => {
  // Component implementation
};
```

### Default Props

Set default values for optional props:

```tsx
interface CardProps {
  title: string;
  content: string;
  imageUrl?: string;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  title,
  content,
  imageUrl = '/default-image.jpg',
  onClick = () => {},
}) => {
  // Component implementation
};
```

### Children Props

Use children props for component composition:

```tsx
interface ContainerProps {
  children: React.ReactNode;
  className?: string;
}

const Container: React.FC<ContainerProps> = ({
  children,
  className = '',
}) => {
  return (
    <div className={`container ${className}`}>
      {children}
    </div>
  );
};
```

## Component Lifecycle

### useEffect Hook

Use the useEffect hook for side effects:

```tsx
const Component = () => {
  const [data, setData] = useState([]);
  
  useEffect(() => {
    // Fetch data on component mount
    const fetchData = async () => {
      const result = await api.getData();
      setData(result);
    };
    
    fetchData();
    
    // Cleanup function
    return () => {
      // Cleanup code
    };
  }, []); // Empty dependency array for mount/unmount only
  
  return (
    <div>
      {data.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
};
```

### useCallback Hook

Use the useCallback hook for memoized functions:

```tsx
const Component = () => {
  const [count, setCount] = useState(0);
  
  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []); // Empty dependency array for stable function
  
  return (
    <button onClick={increment}>
      Count: {count}
    </button>
  );
};
```

### useMemo Hook

Use the useMemo hook for memoized values:

```tsx
const Component = () => {
  const [items, setItems] = useState([]);
  
  const sortedItems = useMemo(() => {
    return [...items].sort((a, b) => a.name.localeCompare(b.name));
  }, [items]); // Recompute when items change
  
  return (
    <div>
      {sortedItems.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
};
```

## Error Handling

### Error Boundaries

Use error boundaries to catch and handle errors:

```tsx
import React from 'react';

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback: React.ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    console.error('Error caught by boundary:', error, errorInfo);
  }
  
  render(): React.ReactNode {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    
    return this.props.children;
  }
}

export default ErrorBoundary;
```

### Try-Catch Blocks

Use try-catch blocks for handling errors in async functions:

```tsx
const Component = () => {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await api.getData();
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      }
    };
    
    fetchData();
  }, []);
  
  if (error) {
    return <div className="error">Error: {error}</div>;
  }
  
  if (!data) {
    return <div className="loading">Loading...</div>;
  }
  
  return (
    <div>
      {/* Render data */}
    </div>
  );
};
```

## Performance Optimization

### Memoization

Use React.memo for memoizing components:

```tsx
const ExpensiveComponent = React.memo(({ data }) => {
  // Component implementation
});
```

### Code Splitting

Use dynamic imports for code splitting:

```tsx
import dynamic from 'next/dynamic';

const DynamicComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <p>Loading...</p>,
});
```

### Virtualization

Use virtualization for rendering large lists:

```tsx
import { useVirtualizer } from '@tanstack/react-virtual';

const VirtualList = ({ items }) => {
  const parentRef = React.useRef(null);
  
  const rowVirtualizer = useVirtualizer({
    count: items.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 50,
  });
  
  return (
    <div ref={parentRef} style={{ height: '400px', overflow: 'auto' }}>
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualRow) => (
          <div
            key={virtualRow.index}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualRow.size}px`,
              transform: `translateY(${virtualRow.start}px)`,
            }}
          >
            {items[virtualRow.index].name}
          </div>
        ))}
      </div>
    </div>
  );
};
```

## Accessibility

### Semantic HTML

Use semantic HTML elements:

```tsx
const Component = () => {
  return (
    <main>
      <header>
        <h1>Page Title</h1>
        <nav>
          <ul>
            <li><a href="/">Home</a></li>
            <li><a href="/about">About</a></li>
          </ul>
        </nav>
      </header>
      <section>
        <h2>Section Title</h2>
        <p>Content</p>
      </section>
      <footer>
        <p>Footer content</p>
      </footer>
    </main>
  );
};
```

### ARIA Attributes

Use ARIA attributes for accessibility:

```tsx
const Component = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div>
      <button
        aria-expanded={isOpen}
        aria-controls="content"
        onClick={() => setIsOpen(!isOpen)}
      >
        Toggle
      </button>
      <div id="content" aria-hidden={!isOpen}>
        {isOpen && <p>Content</p>}
      </div>
    </div>
  );
};
```

### Keyboard Navigation

Ensure keyboard navigation works:

```tsx
const Component = () => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      // Handle action
    }
  };
  
  return (
    <div
      tabIndex={0}
      role="button"
      onKeyDown={handleKeyDown}
      onClick={() => {/* Handle action */}}
    >
      Click me
    </div>
  );
};
```

## Testing Components

### Unit Testing

Use Jest and React Testing Library for unit testing:

```tsx
// Component
const Button = ({ label, onClick }) => (
  <button onClick={onClick}>{label}</button>
);

// Test
import { render, screen, fireEvent } from '@testing-library/react';

test('button calls onClick when clicked', () => {
  const handleClick = jest.fn();
  render(<Button label="Click me" onClick={handleClick} />);
  
  const button = screen.getByText('Click me');
  fireEvent.click(button);
  
  expect(handleClick).toHaveBeenCalledTimes(1);
});
```

### Integration Testing

Use Cypress for integration testing:

```tsx
// cypress/integration/button.spec.js
describe('Button', () => {
  it('should call onClick when clicked', () => {
    cy.visit('/');
    cy.get('button').contains('Click me').click();
    cy.get('.result').should('contain', 'Button clicked');
  });
});
```

## Best Practices

### Component Design

1. **Single Responsibility**: Each component should have a single responsibility.
2. **Composition over Inheritance**: Use composition to build complex components.
3. **Props Interface**: Define a clear props interface for each component.
4. **Default Props**: Provide default values for optional props.
5. **Children Props**: Use children props for component composition.
6. **Controlled vs. Uncontrolled**: Decide whether a component should be controlled or uncontrolled.

### State Management

1. **Local State**: Use local state for component-specific state.
2. **Global State**: Use global state for application-wide state.
3. **Server State**: Use React Query for server state.
4. **Derived State**: Use useMemo for derived state.

### Performance

1. **Memoization**: Use React.memo, useCallback, and useMemo for memoization.
2. **Code Splitting**: Use dynamic imports for code splitting.
3. **Virtualization**: Use virtualization for rendering large lists.
4. **Lazy Loading**: Use lazy loading for images and components.

### Accessibility

1. **Semantic HTML**: Use semantic HTML elements.
2. **ARIA Attributes**: Use ARIA attributes for accessibility.
3. **Keyboard Navigation**: Ensure keyboard navigation works.
4. **Color Contrast**: Ensure sufficient color contrast.
5. **Screen Reader Support**: Ensure screen reader support.

### Testing

1. **Unit Testing**: Write unit tests for components.
2. **Integration Testing**: Write integration tests for features.
3. **End-to-End Testing**: Write end-to-end tests for critical flows.
4. **Accessibility Testing**: Test for accessibility compliance. 