# ProductClaimForm.tsx Refactoring Guide

This guide outlines the steps to refactor the `ProductClaimForm.tsx` component into smaller, more manageable sub-components. The goal is to improve readability, maintainability, and separation of concerns without altering existing functionality or data structures like interfaces.

## Pre-requisites

- [x] Ensure the current `ProductClaimForm.tsx` is stable and working as expected.
- [x] Make sure there are no uncommitted changes in your Git repository. It's advisable to create a new branch for this refactoring task.

## Overall Strategy

We will extract logical sections of the `ProductClaimForm.tsx` into their own components. The `ProductClaimForm` will then act as a parent/container component, managing the overall state and passing data/callbacks to its children.

New components will be created in a new subdirectory: `src/app/components/claim-form/`

## Step 1: Create Subdirectory for New Components

- [x] Create a new directory: `src/app/components/claim-form/`

## Step 2: Refactor Product Search Section

The product search section is displayed when no product is currently selected (`!selectedProduct`).

### 2.1. Create `ProductSearch.tsx`

- [x] Create a new file: `src/app/components/claim-form/ProductSearch.tsx`
- [x] Define `ProductSearchProps` interface. It should include:
  - `searchQuery: string`
  - `setSearchQuery: (query: string) => void`
  - `handleSearch: () => Promise<void>`
  - `handleKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void`
  - `isSearching: boolean`
  - `error: string | null`
  - `searchResults: iProduct[]`
  - `onProductSelect: (product: iProduct) => void`
  - ~~`router: NextRouter`~~ (Removed as not needed)
- [x] Move the JSX responsible for the search input, search button, error display, and search results rendering from `ProductClaimForm.tsx` into `ProductSearch.tsx`.
  - This includes the `div` for `!selectedProduct ? (...) : (...)`.
  - Ensure all necessary imports (Lucide icons, `Input`, `Button`, `ProductCardSlim`, `iProduct`) are added to `ProductSearch.tsx`.
- [x] Adapt `ProductCardSlim` usage:
  - The `onClick` handler for selecting a product from search results should now call `props.onProductSelect(product)`.
- [x] Ensure `ProductSearch.tsx` uses its props correctly.

### 2.2. Integrate `ProductSearch` into `ProductClaimForm.tsx`

- [ ] Import `ProductSearch` into `ProductClaimForm.tsx`.
- [ ] In `ProductClaimForm.tsx`, replace the old search JSX with `<ProductSearch ... />`.
- [ ] Pass the required props from `ProductClaimForm`'s state and handlers to `ProductSearch`.
  - The `onProductSelect` prop passed to `ProductSearch` will call `setSelectedProduct(product)`, `setSearchResults([])`, `localStorage.setItem("claimProductId", product.id)`, and `setError(null)` as it did before.
- [ ] **Test**: Verify that product search functionality works exactly as before.

## Step 3: Refactor Selected Product Display & Claim Check Info

This section is displayed when a product _is_ selected (`selectedProduct`). It shows the selected product's card, a "Change Product" button, and the information about any existing claims for that product by the user.

### 3.1. Create `SelectedProductDisplay.tsx`

- [x] Create a new file: `src/app/components/claim-form/SelectedProductDisplay.tsx`
- [x] Define `SelectedProductDisplayProps`. It should include:
  - `selectedProduct: iProduct`
  - `onChangeProduct: () => void` // Handles "Change Product" button click
  - `isCheckingClaim: boolean`
  - `userHasExistingClaim: boolean`
  - `existingClaimDetails: { id: string; status: string; createdAt: string; rejectionReason?: string; } | null`
- [x] Move the JSX responsible for displaying `ProductCardSlim` for the `selectedProduct`, the "Change Product" button, the "Checking your claim status..." message, and the "Existing Claim Information" block into `SelectedProductDisplay.tsx`.
- [x] Ensure all necessary imports (`ProductCardSlim`, `Button`, `Loader2`, `AlertCircle`, `format` from `date-fns`, `iProduct`) are added.

### 3.2. Integrate `SelectedProductDisplay` into `ProductClaimForm.tsx`

- [ ] Import `SelectedProductDisplay` into `ProductClaimForm.tsx`.
- [ ] In `ProductClaimForm.tsx`, within the `selectedProduct ? (...) : (...)` block, replace the old JSX for this section with `<SelectedProductDisplay ... />`.
- [ ] Pass the required props from `ProductClaimForm`'s state.
  - The `onChangeProduct` prop will call `setSelectedProduct(null)`, `setSearchResults([])`, etc., as the current "Change Product" button does.
- [ ] **Test**: Verify that displaying the selected product and existing claim information works as before.

## Step 4: Refactor Core Claim Form Fields

This is the main form content with input fields.

### 4.1. Create `ClaimFormFields.tsx`

- [x] Create a new file: `src/app/components/claim-form/ClaimFormFields.tsx`
- [x] Define `ClaimFormFieldsProps`. This will be the most extensive props list:
  - `contactInfo: { name: string; email: string; phone: string; }`
  - `setContactInfo: (info: React.SetStateAction<{ name: string; email: string; phone: string; }>) => void`
  - `additionalInfo: string`
  - `setAdditionalInfo: (info: string) => void`
  - `uploadedImages: string[]`
  - `setUploadedImages: (images: React.SetStateAction<string[]>) => void`
  - `handleFileUpload: (files: File[]) => Promise<void>`
  - `isUploading: boolean`
  - `isDisabled: boolean` (This will be `isCheckingClaim || userHasExistingClaim` from the parent)
  - `maxImages: number` (e.g., 5)
- [x] Move the JSX for the `<fieldset>` and its contents (Contact Information, Additional Information, Supporting Images input, and image previews) into `ClaimFormFields.tsx`.
- [x] Ensure all necessary imports (`Input`, `Textarea`, `Label`, `Button`, `X` for removing images, `Loader2`, `toast`) are added.
- [x] The image input's `onChange` will call `props.handleFileUpload`.
- [x] The remove image button's `onClick` will use `props.setUploadedImages`.
- [x] The `fieldset`'s `disabled` attribute will be `props.isDisabled`.

### 4.2. Integrate `ClaimFormFields` into `ProductClaimForm.tsx`

- [ ] Import `ClaimFormFields` into `ProductClaimForm.tsx`.
- [ ] In `ProductClaimForm.tsx`, within the `<form onSubmit={handleSubmit} ...>` element, replace the old form fields JSX with `<ClaimFormFields ... />`.
- [ ] Pass the required props from `ProductClaimForm`'s state and handlers.
  - `isDisabled` will be `isCheckingClaim || userHasExistingClaim`.
- [ ] **Test**: Verify that all form fields work, images can be added/removed, and the form correctly disables based on `isCheckingClaim` or `userHasExistingClaim`.

## Step 5: Create Additional Helper Components

### 5.1. Create `SubmitButton.tsx`

- [x] Create a new file: `src/app/components/claim-form/SubmitButton.tsx`
- [x] Define `SubmitButtonProps` interface with:
  - `isSubmitting: boolean`
  - `isDisabled: boolean`
- [x] Implement a button component that shows a loading spinner when submitting
- [x] Ensure the button is disabled when `isDisabled` or `isSubmitting` is true

### 5.2. Create `AuthCheck.tsx`

- [x] Create a new file: `src/app/components/claim-form/AuthCheck.tsx`
- [x] Define `AuthCheckProps` interface with:
  - `isSignedIn: boolean`
  - `children: React.ReactNode`
- [x] Implement a component that renders children if signed in, otherwise shows a sign-in prompt
- [x] Include a button to redirect to the sign-in page

## Step 6: Create Refactored ProductClaimForm

- [x] Create a new file: `src/app/components/claim-form/RefactoredProductClaimForm.tsx`
- [x] Implement the refactored form using all the new components:
  - Managing overall state (`searchQuery`, `searchResults`, `selectedProduct`, `isSearching`, `error`, `contactInfo`, `additionalInfo`, `files`, `uploadedImages`, `isUploading`, `isCheckingClaim`, `userHasExistingClaim`, `existingClaimDetails`, `isSubmitting`, `submitSuccess`).
  - Containing effect hooks for fetching initial product, checking existing claims.
  - Defining handler functions (`handleSearch`, `handleKeyPress`, `handleFileUpload`, `handleSubmit`, `handleProductSelect`, `handleChangeProduct`).
  - Rendering the appropriate components based on state (`AuthCheck`, `ProductSearch`, `SelectedProductDisplay`, `ClaimFormFields`, `SubmitButton`).
- [x] Ensure all imports are correct and no unused imports remain.
- [x] Add comments to explain complex logic or state management.

## Step 7: Final Review and Testing

- [x] Replace the original ProductClaimForm with the refactored version
- [ ] Test the refactored form to ensure all functionality works as before
- [ ] Clean up any remaining console.log statements or commented-out code

## Summary of Refactoring

The ProductClaimForm has been successfully refactored from a large, monolithic component (755 lines) into a set of smaller, focused components:

1. **AuthCheck** (30 lines) - Handles authentication check and sign-in prompt
2. **ProductSearch** (90 lines) - Handles product search functionality
3. **SelectedProductDisplay** (80 lines) - Displays selected product and existing claim information
4. **ClaimFormFields** (170 lines) - Handles form input fields and image upload
5. **SubmitButton** (25 lines) - Handles submit button state
6. **RefactoredProductClaimForm** (420 lines) - Main container that orchestrates the components

### Benefits of Refactoring

1. **Improved Readability**: Each component has a clear, single responsibility
2. **Better Maintainability**: Changes to one aspect (e.g., search) don't require modifying the entire form
3. **Easier Testing**: Components can be tested in isolation
4. **Reusability**: Components like ProductSearch could be reused in other parts of the application
5. **Reduced Complexity**: The main component is now focused on orchestration rather than implementation details

### Considerations for Future Improvements

- **State Management**: Consider using React Context or a state management library if the form grows more complex
- **Form Validation**: Add more robust form validation using a library like Zod or Yup
- **Error Handling**: Implement more comprehensive error handling and recovery, ensuring error states are correctly passed to relevant components
- **Accessibility**: Ensure all components meet accessibility standards
- **Prop Drilling**: If props need to go many levels deep, consider using Context API for more global state management
- **Styling**: Ensure that extracting components doesn't break any styling (Tailwind CSS should handle this well)

This detailed guide should help us perform the refactoring systematically. We can check off tasks as we complete them.
