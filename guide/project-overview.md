# Review-It Project Overview Guide

This guide provides a comprehensive overview of the Review-It project, including its architecture, routes, key components, and development guidelines.

## Table of Contents

1. [Project Architecture](#project-architecture)
2. [Application Routes](#application-routes)
3. [Key Components](#key-components)
4. [Data Flow](#data-flow)
5. [Authentication & Authorization](#authentication--authorization)
6. [State Management](#state-management)
7. [Styling & UI Components](#styling--ui-components)
8. [API Integration](#api-integration)
9. [Development Guidelines](#development-guidelines)
10. [Deployment & Environment](#deployment--environment)
11. [Testing & Quality Assurance](#testing--quality-assurance)
12. [Performance Optimization](#performance-optimization)
13. [SEO & Metadata](#seo--metadata)
14. [Internationalization](#internationalization)
15. [Accessibility](#accessibility)

## Project Architecture

Review-It is built using Next.js 14 with the App Router, TypeScript, and Tailwind CSS. The project follows a modular architecture with clear separation of concerns.

### Directory Structure

```
src/
├── app/                  # Next.js App Router
│   ├── (routes)/         # Main application routes
│   ├── api/              # API routes
│   ├── components/       # Shared components
│   ├── lib/              # Utility functions and libraries
│   ├── store/            # State management
│   ├── util/             # TypeScript interfaces and utilities
│   └── hooks/            # Custom React hooks
├── components/           # UI components
├── lib/                  # Shared libraries
├── middleware/           # Middleware functions
└── hooks/                # Global hooks
```

### Key Technologies

- **Next.js 14**: React framework with App Router
- **TypeScript**: For type safety and better developer experience
- **Tailwind CSS**: For styling
- **Clerk**: For authentication
- **Prisma**: For database access
- **React Query**: For data fetching and caching
- **Jotai**: For state management
- **Framer Motion**: For animations

## Application Routes

The application uses Next.js App Router with route groups for organization.

### Main Routes

- **Home**: `/` - Landing page
- **Browse**: `/browse` - Browse products and services
- **Product**: `/product/[id]` - Product details page
- **Reviews**: `/reviews` - Reviews listing page
- **Create Review**: `/cr` - Create a new review
- **User Profile**: `/userprofile` - User profile page
- **Owner Admin**: `/owner-admin` - Business owner dashboard
- **Admin**: `/admin` - Site administrator dashboard
- **Edit Product**: `/editproduct` - Edit product details
- **My Businesses**: `/mybusinesses` - User's businesses
- **Submit**: `/submit` - Submit a new product
- **Pricing**: `/pricing` - Pricing information
- **Notifications**: `/notifications` - User notifications
- **Install**: `/install` - Installation guide
- **Bug Report**: `/bugreport` - Report a bug
- **About**: `/about` - About page
- **FAQ**: `/faq` - Frequently asked questions
- **Privacy**: `/privacy` - Privacy policy
- **Terms of Service**: `/tos` - Terms of service

### Authentication Routes

- **Sign In**: `https://accounts.reviewit.gy/sign-in` - Sign in page
- **Sign Up**: `https://accounts.reviewit.gy/sign-up` - Sign up page
- **Sign Out**: `/sign-out` - Sign out page

### Internationalization

- **French Routes**: `/fr/*` - French language routes

## Key Components

### ProductCard

The `ProductCard` component is a central piece of the application, displaying product information with reviews.

**Location**: `src/app/components/ProductCard.tsx`

**Key Features**:
- Displays product information (name, description, images)
- Shows review ratings and statistics
- Provides actions (edit, view profile, write review, share)
- Handles product ownership verification
- Responsive design for different screen sizes

**Props**:
```typescript
interface ProductCardProps {
  reviews?: iReview[] | null;
  options: {
    showLatestReview: boolean;
    size: string;
    showWriteReview: boolean;
    showClaimThisProduct: boolean;
  };
  product?: iProduct | null;
  currentUserId: string | null;
}
```

### ShareButton

The `ShareButton` component provides social sharing functionality.

**Location**: `src/app/components/ShareButton.tsx`

**Key Features**:
- Dropdown menu with sharing options
- Supports multiple platforms (Twitter, Facebook, WhatsApp, LinkedIn, Reddit, Email)
- Copy link functionality
- Customizable appearance

**Props**:
```typescript
interface ShareButtonProps {
  title: string;
  text: string;
  url: string;
  imageUrl?: string;
  className?: string;
}
```

### DropdownMenu

The `DropdownMenu` component provides a customizable dropdown menu.

**Location**: `src/components/ui/dropdown-menu.tsx`

**Key Features**:
- Accessible dropdown menu
- Customizable appearance
- Support for sub-menus
- Keyboard navigation

## Data Flow

### Data Fetching

The application uses React Query for data fetching and caching:

```typescript
// Example from browse/page.tsx
const { data, isLoading, isError, error } = useQuery({
  queryKey: ["products"],
  queryFn: getProducts,
  refetchOnWindowFocus: false,
}) as any;
```

### State Management

Jotai is used for global state management:

```typescript
// Example from browse/page.tsx
const [_, setCurrentProduct] = useAtom(allProductsAtom);
```

## Authentication & Authorization

Clerk is used for authentication:

```typescript
// Example from browse/page.tsx
const { userId } = useAuth();
```

### Authorization Flow

1. User signs in with Clerk
2. User ID is stored in the application state
3. Authorization checks are performed based on user ID
4. Protected routes and actions are guarded

## Styling & UI Components

### Tailwind CSS

The application uses Tailwind CSS for styling:

```tsx
// Example from ProductCard.tsx
<div className="flex-1 sm:flex-initial">
  <Link href={`/product/${currentProduct?.id}`} className="w-full">
    <Button
      variant="outline"
      size="sm"
      className="w-full flex items-center justify-center gap-1"
    >
      <Eye size={16} />
      Product Profile
    </Button>
  </Link>
</div>
```

### UI Components

The application uses a set of reusable UI components:

- Button
- DropdownMenu
- Input
- Textarea
- Card
- Modal
- Toast

## API Integration

### Server Actions

Next.js Server Actions are used for API integration:

```typescript
// Example from actions.ts
'use server'

export async function createCheckoutSession(productId: string) {
  // API call implementation
}
```

### API Routes

API routes are used for webhooks and other server-side functionality:

```typescript
// Example from api/vaultgy-webhook/route.ts
export async function POST(request: NextRequest) {
  // Webhook handling implementation
}
```

## Development Guidelines

### Code Standards

1. Use TypeScript for all new components
2. Follow existing project structure
3. Implement responsive design
4. Use existing styling patterns
5. Add proper error handling
6. Include loading states where necessary
7. Add appropriate comments
8. Follow existing naming conventions

### Component Guidelines

1. Create reusable components when possible
2. Use proper TypeScript interfaces
3. Implement error boundaries where needed
4. Follow accessibility best practices
5. Optimize performance
6. Add proper testing

### TypeScript Interfaces

Key interfaces are defined in `src/app/util/Interfaces.tsx`:

- `iProduct`: Product data structure
- `iReview`: Review data structure
- `iUser`: User data structure
- `iBusiness`: Business data structure
- `iComment`: Comment data structure
- `iNotification`: Notification data structure

## Deployment & Environment

### Environment Variables

The application uses environment variables for configuration:

```
NEXT_PUBLIC_APP_URL=https://reviewit.gy
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key
```

### Deployment

The application can be deployed to various platforms:

- Vercel (recommended)
- Netlify
- AWS
- Google Cloud Platform

## Testing & Quality Assurance

### Testing Tools

- Jest for unit testing
- React Testing Library for component testing
- Cypress for end-to-end testing

### Quality Assurance

- ESLint for code linting
- Prettier for code formatting
- Husky for pre-commit hooks
- GitHub Actions for CI/CD

## Performance Optimization

### Optimization Techniques

- Image optimization with Next.js Image component
- Code splitting with dynamic imports
- Server-side rendering for SEO
- Client-side caching with React Query
- Lazy loading of components

## SEO & Metadata

### Metadata Generation

The application uses Next.js metadata API for SEO:

```typescript
// Example from componentMetadata.ts
export function generateComponentMetadata({
  title,
  description = "Review It - Your Voice, Your Choice. A website where you can share and read reviews on anything.",
  imageUrl,
  url,
  type = 'website',
  rating,
  reviewCount,
}: ComponentMetadataProps): Partial<Metadata> {
  // Metadata generation implementation
}
```

### Social Sharing

The application supports social sharing with OpenGraph and Twitter card metadata:

```typescript
// Example from socialSharing.ts
export function getSocialShareUrl(options: SocialShareOptions): string {
  // Social sharing URL generation implementation
}
```

## Internationalization

The application supports multiple languages:

- English (default)
- French (`/fr/*` routes)

## Accessibility

The application follows accessibility best practices:

- Semantic HTML
- ARIA attributes
- Keyboard navigation
- Color contrast
- Screen reader support

## Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Clerk Documentation](https://clerk.com/docs)
- [React Query Documentation](https://tanstack.com/query/latest)
- [Jotai Documentation](https://jotai.org/docs)
- [Framer Motion Documentation](https://www.framer.com/motion) 