# Product Claiming System Implementation Guide

## Implementation Status

### ✅ Completed Features

- ✅ Database schema updated with ProductClaim model
- ✅ TypeScript interfaces added for ProductClaim
- ✅ Cloudinary integration for claim images
- ✅ Image upload API endpoint for claims
- ✅ Product claim submission API endpoint
- ✅ Admin review API endpoint for approving/rejecting claims
- ✅ Admin claims review panel UI
- ✅ ClaimStatus component for displaying claim status to users
- ✅ Admin claims page for managing claims
- ✅ Admin permission checking API

### 🚧 Next Steps

- [ ] User dashboard integration (showing user's claims)
- [ ] Email notifications for claim status changes (skip this)
- [ ] User's claims history page

## Database Schema

The ProductClaim model has been added to the Prisma schema:

```prisma
model ProductClaim {
  id              String    @id @default(uuid())
  productId       String
  userId          String
  status          String    @default("PENDING") // PENDING, APPROVED, REJECTED
  contactInfo     String
  additionalInfo  String
  images          String[]  // URLs to uploaded images in Cloudinary
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  reviewedAt      DateTime?
  reviewedBy      String?
  rejectionReason String?
  
  product         Product   @relation(fields: [productId], references: [id])
  user            User      @relation(fields: [userId], references: [id])
  reviewer        User?     @relation("ClaimReviewer", fields: [reviewedBy], references: [id])

  @@unique([productId]) // One active claim per product
  @@index([status])
  @@index([userId])
  @@index([createdAt])
}
```

The Product and User models have been updated with relations to ProductClaim.

## TypeScript Interfaces

The ProductClaim interface has been added to support the new model:

```typescript
export interface iProductClaim {
  id?: string;
  productId: string;
  userId: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  contactInfo: string;
  additionalInfo: string;
  images: string[];
  createdAt?: Date;
  updatedAt?: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  rejectionReason?: string;
  product?: iProduct;
  user?: iUser;
  reviewer?: iUser;
}
```

## API Endpoints Implemented

### 1. Image Upload API

**Path:** `src/app/api/upload/claim-images/route.ts`

Functionality:
- Uploads images for product claims to Cloudinary
- Stores images in a dedicated "claims_images" folder
- Validates file types (only images allowed)
- Includes size validation (max 5MB)

### 2. Claim Submission API

**Path:** `src/app/api/update/claim-product/route.ts`

Functionality:
- Creates a ProductClaim record with pending status
- Validates required fields (product, contact info, additional info, images)
- Performs checks to prevent duplicate claims or claiming already-owned products

### 3. Admin Review API

**Path:** `src/app/api/admin/review-claim/route.ts`

Functionality:
- Allows admins to approve or reject claims
- When approving: updates product ownership, creates business record
- When rejecting: records rejection reason
- Logs admin actions for auditing

### 4. Admin Claims List API

**Path:** `src/app/api/admin/claims/route.ts`

Functionality:
- Fetches claims with filtering and pagination
- Includes related product and user information
- Secure access for admin users only

### 5. Admin Permission Check API

**Path:** `src/app/api/admin/check-permission/route.ts`

Functionality:
- Verifies if the current user has admin permissions
- Used to protect admin routes and components

## UI Components Implemented

### 1. ClaimStatus Component

**Path:** `src/app/components/ClaimStatus.tsx`

Functionality:
- Displays claim status (pending, approved, rejected)
- Shows claim details and supporting images
- Provides appropriate feedback based on status

### 2. Admin Review Panel

**Path:** `src/app/components/admin/ClaimReviewPanel.tsx`

Functionality:
- Tabbed interface for pending, approved, and rejected claims
- Detailed claim review card with product and user information
- Forms for approving or rejecting claims with reason

### 3. Admin Claims Page

**Path:** `src/app/admin/claims/page.tsx`

Functionality:
- Protected admin-only page
- Lists all claims with filters
- Integration with ClaimReviewPanel component

## Error Handling Strategy

The implemented API endpoints include comprehensive error handling:

```typescript
try {
  // API logic
} catch (error) {
  console.error("Error in endpoint:", error);
  const e = error as Error;
  return NextResponse.json({
    success: false,
    status: 500,
    message: e.message || 'An unexpected error occurred',
    errorCode: 'ERROR_CODE', // For client-side handling
  });
}
```

All endpoints return standardized error responses with:
- Error codes for client-side handling
- Human-readable error messages
- Appropriate HTTP status codes

## Implementation Plan for Remaining Tasks

### 1. User Dashboard Integration

**Steps:**
1. Create an API endpoint to fetch a user's claims
   - Path: `src/app/api/user/claims/route.ts`
   - Functionality: Fetch claims for the authenticated user with filtering options

2. Add a section to the user dashboard
   - Path: `src/app/dashboard/page.tsx` (or relevant dashboard component)
   - Add a new tab or section for "My Claims"
   - Integrate the ClaimStatus component to display each claim

3. Create a user claims component
   - Path: `src/app/components/UserClaims.tsx`
   - Display a list of the user's claims with status indicators
   - Allow filtering by status (pending, approved, rejected)

### 2. Email Notifications- lets skip email notifications for now

**Steps:**
1. Set up an email service integration
   - Options: SendGrid, Resend, or similar
   - Create email templates for different notification types

2. Create an email notification service
   - Path: `src/app/util/emailService.ts`
   - Functions for sending different types of notifications

3. Integrate email notifications with claim status changes
   - Update the admin review API to trigger emails
   - Send notifications to users when their claims are approved or rejected

### 3. User's Claims History Page

**Steps:**
1. Create a dedicated claims history page
   - Path: `src/app/claims/page.tsx`
   - Display all claims made by the user with detailed information

2. Add filtering and sorting options
   - Allow filtering by status, date, etc.
   - Implement sorting by various fields

3. Create a detailed claim view component
   - Path: `src/app/components/ClaimDetail.tsx`
   - Show comprehensive information about a specific claim
   - Include all supporting images and review feedback

## Cloudinary Configuration

The image upload functionality uses a dedicated function for claim images:

```typescript
export async function uploadClaimImageToCloudinary(data: any) {
  return await c.uploader.upload(data, {
    resource_type: "image",
    folder: "claims_images",
  });
}
```

## Security Considerations

1. All API endpoints include authentication checks using Clerk
2. Admin endpoints have additional role-based verification
3. Image uploads include validation for file type and size
4. The ProductClaim model has a unique constraint on productId to prevent multiple claims
5. Database queries use transactions for data consistency when approving claims

## Performance Considerations

For handling the image uploads:

1. Client-side image compression before upload is recommended
2. The API validates file size to prevent large uploads
3. Cloudinary is used for efficient image storage and delivery

## Rollback Plan

If issues arise with the new claiming system:
1. The database migration can be reversed with `prisma migrate down`
2. The API endpoints can be disabled or removed
3. The UI components can be hidden from the interface

## Future Enhancements

1. Email notifications for claim status changes
2. Bulk claim review for admins
3. Claim history tracking
4. Analytics for claim processing times
5. Integration with business verification process
