# Product Location and Mobile UI Improvements Guide

This guide outlines the implementation of location features for products and improvements to the mobile UI for product cards.

## Table of Contents
1. [Mobile UI Improvements](#mobile-ui-improvements)
2. [Product Location Feature](#product-location-feature)
3. [Similar Products Feature](#similar-products-feature)

## Mobile UI Improvements

### Current Issue
The product cards on the browse page mobile view for unclaimed businesses can have up to 4 buttons in a row:
- Claim this business
- View product
- Share
- Write review

This layout is not mobile-friendly as the buttons are too small and cramped on smaller screens.

### Solution
Implement a more mobile-friendly button layout:

1. **Primary Action Button**
   - Keep the most important action (likely "Claim This Business" for unclaimed businesses) as a full-width primary button at the top
   - This ensures the most important action is immediately visible and accessible

2. **Secondary Actions in a Grid**
   - Arrange the remaining buttons in a 2x2 grid below the primary button
   - Each button would take up 50% of the width, making them large enough for comfortable tapping
   - Use icon-only buttons with clear labels underneath

3. **More Actions Menu**
   - Add a "More Actions" dropdown menu for additional functionality
   - Include options like:
     - Report Product
     - View All Reviews
     - Contact Business (if available)
     - View on Map (if location data is available)

### Implementation Steps

1. Modify the `ProductCard` component to use a responsive layout:
   ```tsx
   // In ProductCard.tsx
   <div className="flex flex-col sm:flex-row gap-2">
     {/* Primary action button */}
     {options.showClaimThisProduct && canClaimProduct && !amITheOwner && (
       <div className="w-full sm:w-auto">
         <ClaimProductComponent product={currentProduct} />
       </div>
     )}
     
     {/* Secondary actions grid */}
     <div className="grid grid-cols-2 gap-2 w-full sm:w-auto">
       <Link href={`/product/${currentProduct?.id}`} className="w-full">
         <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
           <Eye size={16} />
           <span className="hidden sm:inline">View</span>
         </Button>
       </Link>
       
       {options.showWriteReview && (
         <Link href={`/cr/?id=${currentProduct?.id}&rating=3`} className="w-full">
           <Button variant="default" size="sm" className="w-full flex items-center justify-center gap-1 bg-black hover:bg-gray-800 text-white">
             <MdEdit size={16} />
             <span className="hidden sm:inline">Review</span>
           </Button>
         </Link>
       )}
       
       <div className="w-full">
         <ShareButtonWrapper metadata={metadata} className="w-full" />
       </div>
       
       <DropdownMenu>
         <DropdownMenuTrigger asChild>
           <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
             <MoreHorizontal size={16} />
             <span className="hidden sm:inline">More</span>
           </Button>
         </DropdownMenuTrigger>
         <DropdownMenuContent align="end" className="w-48">
           <DropdownMenuItem>
             <Link href={`/reviews?id=${currentProduct?.id}`} className="flex items-center gap-2 w-full">
               <MessageSquare size={16} />
               View All Reviews
             </Link>
           </DropdownMenuItem>
           {currentProduct?.location && (
             <DropdownMenuItem>
               <Link href={`/location?id=${currentProduct?.id}`} className="flex items-center gap-2 w-full">
                 <MapPin size={16} />
                 View on Map
               </Link>
             </DropdownMenuItem>
           )}
           <DropdownMenuItem>
             <Button variant="ghost" className="flex items-center gap-2 w-full text-red-600 hover:text-red-700">
               <MdReport size={16} />
               Report Product
             </Button>
           </DropdownMenuItem>
         </DropdownMenuContent>
       </DropdownMenu>
     </div>
   </div>
   ```

2. Add necessary imports:
   ```tsx
   import { MoreHorizontal, MapPin, MessageSquare } from 'lucide-react';
   import {
     DropdownMenu,
     DropdownMenuContent,
     DropdownMenuItem,
     DropdownMenuTrigger,
   } from "@/components/ui/dropdown-menu";
   ```

## Product Location Feature

### Overview
Add the ability for users to specify a location for their product/business when creating it, and provide a dedicated location page to view the product on a map.

### Implementation Steps

1. **Update Prisma Schema**
   Add location fields to the Product model:
   ```prisma
   model Product {
     // existing fields
     latitude  Float?
     longitude Float?
     address   String?
     // other fields
   }
   ```

2. **Install Required Packages**
   ```bash
   pnpm add @react-google-maps/api
   ```

3. **Create Location Picker Component**
   Create a new component for selecting a location on a map:
   ```tsx
   // src/app/components/LocationPicker.tsx
   "use client";
   import { useState, useCallback } from 'react';
   import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api';

   interface LocationPickerProps {
     onLocationSelect: (location: { lat: number; lng: number; address: string }) => void;
     initialLocation?: { lat: number; lng: number };
   }

   const LocationPicker: React.FC<LocationPickerProps> = ({ onLocationSelect, initialLocation }) => {
     const [marker, setMarker] = useState(initialLocation || { lat: 0, lng: 0 });
     const [address, setAddress] = useState('');

     const handleMapClick = useCallback((e: google.maps.MapMouseEvent) => {
       if (e.latLng) {
         const newLocation = {
           lat: e.latLng.lat(),
           lng: e.latLng.lng(),
         };
         setMarker(newLocation);
         
         // Reverse geocoding to get address
         const geocoder = new google.maps.Geocoder();
         geocoder.geocode({ location: newLocation }, (results, status) => {
           if (status === 'OK' && results && results[0]) {
             const formattedAddress = results[0].formatted_address;
             setAddress(formattedAddress);
             onLocationSelect({ ...newLocation, address: formattedAddress });
           }
         });
       }
     }, [onLocationSelect]);

     return (
       <div className="w-full h-[400px] rounded-lg overflow-hidden border border-gray-200">
         <LoadScript googleMapsApiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}>
           <GoogleMap
             mapContainerStyle={{ width: '100%', height: '100%' }}
             center={marker}
             zoom={13}
             onClick={handleMapClick}
           >
             <Marker position={marker} />
           </GoogleMap>
         </LoadScript>
         {address && (
           <div className="p-2 bg-white border-t border-gray-200">
             <p className="text-sm text-gray-600">{address}</p>
           </div>
         )}
       </div>
     );
   };

   export default LocationPicker;
   ```

4. **Create Location Page**
   Create a dedicated page for viewing a product's location:
   ```tsx
   // src/app/(routes)/location/page.tsx
   "use client";
   import { useSearchParams } from 'next/navigation';
   import { useQuery } from '@tanstack/react-query';
   import { getProduct } from '@/app/util/serverFunctions';
   import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api';
   import LoadingSpinner from '@/app/components/LoadingSpinner';
   import Link from 'next/link';
   import { ArrowLeft } from 'lucide-react';

   const LocationPage = () => {
     const searchParams = useSearchParams();
     const productId = searchParams.get('id');

     const { data: product, isLoading, isError } = useQuery({
       queryKey: ['product', productId],
       queryFn: () => getProduct(productId!),
       enabled: !!productId,
     });

     if (isLoading) return <LoadingSpinner />;
     if (isError) return <div>Error loading product</div>;
     if (!product || !product.latitude || !product.longitude) {
       return (
         <div className="container mx-auto p-6">
           <div className="bg-white rounded-lg shadow p-6 text-center">
             <h1 className="text-xl font-bold mb-4">Location Not Available</h1>
             <p className="mb-4">This product doesn't have location information.</p>
             <Link href={`/product/${productId}`} className="inline-flex items-center text-blue-600 hover:text-blue-800">
               <ArrowLeft className="w-4 h-4 mr-2" />
               Back to Product
             </Link>
           </div>
         </div>
       );
     }

     const location = {
       lat: product.latitude,
       lng: product.longitude,
     };

     return (
       <div className="container mx-auto p-6">
         <div className="mb-4">
           <Link href={`/product/${productId}`} className="inline-flex items-center text-blue-600 hover:text-blue-800">
             <ArrowLeft className="w-4 h-4 mr-2" />
             Back to Product
           </Link>
         </div>
         <div className="bg-white rounded-lg shadow overflow-hidden">
           <div className="p-4 border-b border-gray-200">
             <h1 className="text-xl font-bold">{product.name}</h1>
             {product.address && (
               <p className="text-gray-600 mt-1">{product.address}</p>
             )}
           </div>
           <div className="h-[500px]">
             <LoadScript googleMapsApiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}>
               <GoogleMap
                 mapContainerStyle={{ width: '100%', height: '100%' }}
                 center={location}
                 zoom={15}
               >
                 <Marker position={location} />
               </GoogleMap>
             </LoadScript>
           </div>
         </div>
       </div>
     );
   };

   export default LocationPage;
   ```

5. **Update Product Creation/Edit Forms**
   Add the location picker to product creation and edit forms:
   ```tsx
   // In your product form component
   const [location, setLocation] = useState<{ lat: number; lng: number; address: string } | null>(null);

   // In your form JSX
   <div className="mb-4">
     <label className="block text-sm font-medium text-gray-700 mb-2">
       Location (Optional)
     </label>
     <LocationPicker 
       onLocationSelect={setLocation} 
       initialLocation={product?.latitude && product?.longitude ? 
         { lat: product.latitude, lng: product.longitude } : undefined} 
     />
   </div>

   // In your form submission handler
   const formData = {
     // other fields
     latitude: location?.lat,
     longitude: location?.lng,
     address: location?.address,
   };
   ```

6. **Update API Routes**
   Modify your API routes to handle the new location fields:
   ```ts
   // In your product creation/update API route
   const { latitude, longitude, address, ...otherFields } = req.body;
   
   const product = await prisma.product.create({
     data: {
       ...otherFields,
       latitude: latitude ? parseFloat(latitude) : null,
       longitude: longitude ? parseFloat(longitude) : null,
       address: address || null,
     },
   });
   ```

7. **Environment Variables**
   Add the Google Maps API key to your environment variables:
   ```
   NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
   ```

## Similar Products Feature

### Overview
Add a "View Similar Products" option that takes users to the browse page with relevant tags pre-selected.

### Implementation Steps

1. **Add Similar Products Link to More Actions Menu**
   Update the dropdown menu in the ProductCard component to include a link to similar products:
   ```tsx
   <DropdownMenuItem>
     <Link 
       href={`/browse?tags=${encodeURIComponent(currentProduct?.tags.slice(0, 2).join(','))}`} 
       className="flex items-center gap-2 w-full"
     >
       <Search size={16} />
       View Similar Products
     </Link>
   </DropdownMenuItem>
   ```

2. **No Additional Changes Needed**
   The browse page already supports filtering by tags via URL parameters, so no additional changes are required to the browse page functionality.

## Conclusion

These improvements will enhance the mobile user experience and add valuable location features to your product listings. The implementation is modular and can be done incrementally, starting with the mobile UI improvements and then adding the location features.

Remember to:
1. Test thoroughly on various mobile devices and screen sizes
2. Ensure the Google Maps API key is properly secured
3. Consider adding loading states and error handling for the map components
4. Update your documentation to reflect these new features 