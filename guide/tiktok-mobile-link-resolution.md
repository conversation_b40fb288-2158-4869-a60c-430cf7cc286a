# TikTok Mobile Link Resolution Implementation Guide

## Overview
This guide outlines the implementation of automatic resolution for TikTok mobile links (`vm.tiktok.com`) to their expanded desktop format (`tiktok.com`). This will improve user experience by eliminating the need for manual conversion of mobile TikTok links.

## Current Implementation
Currently, when a user pastes a mobile TikTok link (`vm.tiktok.com`), the system shows an error message asking users to manually open the link in a browser and use the expanded URL. This is handled in `src/app/components/VideoEmbed.tsx`.

## Implementation Plan

### 1. Create URL Resolution Utility
- [ ] Create new file: `src/app/util/urlResolvers.ts`
- [ ] Implement `resolveTikTokMobileLink` function
- [ ] Add error handling and type safety

### 2. Modify VideoEmbed Component
- [ ] Update `getTikTokEmbedCode` function in `src/app/components/VideoEmbed.tsx`
- [ ] Integrate URL resolution
- [ ] Add loading state for resolution process
- [ ] Update error handling

## Detailed Implementation Steps

### 1. URL Resolution Utility

Create `src/app/util/urlResolvers.ts`:

```typescript
import { z } from 'zod';

// Schema for validating TikTok URLs
const tiktokUrlSchema = z.string().url().refine(
  (url) => url.includes('tiktok.com'),
  'URL must be a TikTok link'
);

/**
 * Resolves a TikTok mobile link (vm.tiktok.com) to its expanded desktop format
 * @param url The TikTok URL to resolve
 * @returns Promise resolving to the expanded URL
 * @throws Error if resolution fails
 */
export async function resolveTikTokMobileLink(url: string): Promise<string> {
  try {
    // Validate URL format
    tiktokUrlSchema.parse(url);

    // If it's already a desktop URL, return as is
    if (!url.includes('vm.tiktok.com')) {
      return url;
    }

    // Make HEAD request to get the final URL after redirects
    const response = await fetch(url, {
      method: 'HEAD',
      redirect: 'follow',
    });

    if (!response.ok) {
      throw new Error(`Failed to resolve TikTok URL: ${response.statusText}`);
    }

    return response.url;
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error('Invalid TikTok URL format');
    }
    throw error;
  }
}
```

### 2. Update VideoEmbed Component

Modify `src/app/components/VideoEmbed.tsx`:

```typescript
// Add import
import { resolveTikTokMobileLink } from '../util/urlResolvers';

// Add loading state
const [isResolving, setIsResolving] = useState(false);

// Update getTikTokEmbedCode function
const getTikTokEmbedCode = useCallback(async (url: string): Promise<{ embedCode: string, thumbnailUrl: string }> => {
  try {
    if (url.includes('vm.tiktok.com')) {
      setIsResolving(true);
      try {
        const resolvedUrl = await resolveTikTokMobileLink(url);
        const response = await fetch(`https://www.tiktok.com/oembed?url=${encodeURIComponent(resolvedUrl)}`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        return { embedCode: data.html, thumbnailUrl: data.thumbnail_url };
      } catch (error) {
        setError('Failed to resolve TikTok mobile link. Please try using the desktop version of the link.');
        return { embedCode: '', thumbnailUrl: '' };
      } finally {
        setIsResolving(false);
      }
    }

    // Existing code for desktop URLs...
    const response = await fetch(`https://www.tiktok.com/oembed?url=${encodeURIComponent(url)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return { embedCode: data.html, thumbnailUrl: data.thumbnail_url };
  } catch (error) {
    throw new Error('Failed to fetch TikTok embed code');
  }
}, []);
```

### 3. Add Loading State UI

Update the UI in `VideoEmbed.tsx` to show loading state:

```typescript
{isResolving && (
  <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
    <div className="text-white">Resolving TikTok link...</div>
  </div>
)}
```

## Security Considerations

1. **Rate Limiting**
   - Consider implementing rate limiting for URL resolution requests
   - Cache resolved URLs to prevent repeated requests

2. **URL Validation**
   - Strict validation of input URLs
   - Sanitization of resolved URLs
   - Prevention of malicious redirects

3. **Error Handling**
   - Graceful degradation
   - User-friendly error messages
   - Logging for debugging

## Performance Considerations

1. **Caching**
   - Implement URL resolution caching
   - Cache embed codes and thumbnails
   - Set appropriate cache expiration

2. **Loading States**
   - Show loading indicators
   - Prevent multiple simultaneous requests
   - Handle timeouts gracefully

## Dependencies

- `zod` for URL validation
- Existing `fetch` API
- React state management

## Related Files

- `src/app/components/VideoEmbed.tsx`
- `src/app/util/urlResolvers.ts` (new)
- `src/app/components/review/ReviewMediaSection.tsx`

## Notes

- The implementation uses HEAD requests to minimize data transfer
- Error messages are user-friendly and actionable
- Loading states provide clear feedback
- The solution is scalable and maintainable 