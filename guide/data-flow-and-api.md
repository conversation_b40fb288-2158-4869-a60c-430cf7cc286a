# Review-It Data Flow and API Integration Guide

This guide provides a detailed overview of the data flow and API integration in the Review-It project, including data fetching, state management, and API endpoints.

## Table of Contents

1. [Data Flow Overview](#data-flow-overview)
2. [Data Fetching](#data-fetching)
3. [State Management](#state-management)
4. [API Integration](#api-integration)
5. [Server Actions](#server-actions)
6. [Error Handling](#error-handling)
7. [Caching Strategies](#caching-strategies)
8. [Optimistic Updates](#optimistic-updates)
9. [Real-time Updates](#real-time-updates)
10. [Data Validation](#data-validation)
11. [Best Practices](#best-practices)

## Data Flow Overview

The Review-It application follows a unidirectional data flow:

1. **Data Source**: API endpoints, server actions, or local state
2. **Data Fetching**: React Query for server data, <PERSON><PERSON> for global state
3. **State Management**: Jotai atoms for global state, React hooks for local state
4. **UI Rendering**: Components render based on state
5. **User Interactions**: User actions trigger state updates
6. **State Updates**: State updates trigger UI re-renders

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Data Source│────▶│Data Fetching│────▶│   State     │────▶│     UI      │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
       ▲                   ▲                   ▲                   ▲
       │                   │                   │                   │
       └───────────────────┴───────────────────┴───────────────────┘
                      User Interactions
```

## Data Fetching

### React Query

The application uses React Query for data fetching and caching:

```tsx
// Example from browse/page.tsx
import { useQuery } from "@tanstack/react-query";
import { getProducts } from "@/app/util/serverFunctions";

const BrowsePage = () => {
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
    refetchOnWindowFocus: false,
  }) as any;

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (isError) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div>
      {data.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
};
```

### Query Keys

Query keys are used to identify and cache data:

```tsx
// Simple query key
const { data } = useQuery({
  queryKey: ["products"],
  queryFn: getProducts,
});

// Query key with parameters
const { data } = useQuery({
  queryKey: ["product", productId],
  queryFn: () => getProduct(productId),
  enabled: !!productId,
});

// Query key with filters
const { data } = useQuery({
  queryKey: ["products", { category, sortBy }],
  queryFn: () => getProducts({ category, sortBy }),
});
```

### Query Functions

Query functions are defined in `src/app/util/serverFunctions.ts`:

```tsx
// Example from serverFunctions.ts
export async function getProducts() {
  const response = await fetch("/api/products");
  if (!response.ok) {
    throw new Error("Failed to fetch products");
  }
  return response.json();
}

export async function getProduct(id: string) {
  const response = await fetch(`/api/products/${id}`);
  if (!response.ok) {
    throw new Error("Failed to fetch product");
  }
  return response.json();
}
```

### Mutations

Mutations are used for data updates:

```tsx
// Example from product/edit/page.tsx
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateProduct } from "@/app/util/serverFunctions";

const EditProductPage = ({ productId }) => {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: updateProduct,
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["product", productId] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const handleSubmit = (data) => {
    mutation.mutate({ id: productId, ...data });
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  );
};
```

## State Management

### Jotai Atoms

Jotai atoms are used for global state management:

```tsx
// Example from store.ts
import { atom } from "jotai";
import { iProduct } from "@/app/util/Interfaces";

export const allProductsAtom = atom<iProduct[]>([]);
export const selectedProductAtom = atom<iProduct | null>(null);
export const userAtom = atom<any | null>(null);
```

### Using Atoms

Atoms are used in components:

```tsx
// Example from browse/page.tsx
import { useAtom } from "jotai";
import { allProductsAtom } from "@/app/store/store";

const BrowsePage = () => {
  const [products, setProducts] = useAtom(allProductsAtom);

  useEffect(() => {
    // Update atom when data is fetched
    if (data) {
      setProducts(data);
    }
  }, [data, setProducts]);

  return (
    <div>
      {products.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
};
```

### Derived Atoms

Derived atoms are used for computed values:

```tsx
// Example from store.ts
import { atom } from "jotai";
import { iProduct } from "@/app/util/Interfaces";

export const allProductsAtom = atom<iProduct[]>([]);

// Derived atom for filtered products
export const filteredProductsAtom = atom((get) => {
  const products = get(allProductsAtom);
  const filter = get(filterAtom);
  
  return products.filter((product) => 
    product.name.toLowerCase().includes(filter.toLowerCase())
  );
});
```

## API Integration

### API Routes

API routes are defined in the `src/app/api` directory:

```tsx
// Example from api/products/route.ts
import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const products = await prisma.product.findMany();
    return NextResponse.json(products);
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to fetch products" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const product = await prisma.product.create({
      data: body,
    });
    return NextResponse.json(product);
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to create product" },
      { status: 500 }
    );
  }
}
```

### API Endpoints

The application uses the following API endpoints:

- `GET /api/products`: Get all products
- `GET /api/products/:id`: Get a product by ID
- `POST /api/products`: Create a new product
- `PUT /api/products/:id`: Update a product
- `DELETE /api/products/:id`: Delete a product
- `GET /api/reviews`: Get all reviews
- `GET /api/reviews/:id`: Get a review by ID
- `POST /api/reviews`: Create a new review
- `PUT /api/reviews/:id`: Update a review
- `DELETE /api/reviews/:id`: Delete a review
- `GET /api/users`: Get all users
- `GET /api/users/:id`: Get a user by ID
- `PUT /api/users/:id`: Update a user
- `GET /api/businesses`: Get all businesses
- `GET /api/businesses/:id`: Get a business by ID
- `POST /api/businesses`: Create a new business
- `PUT /api/businesses/:id`: Update a business
- `DELETE /api/businesses/:id`: Delete a business

### API Response Format

API responses follow a consistent format:

```json
{
  "data": [...],
  "meta": {
    "total": 100,
    "page": 1,
    "limit": 10
  }
}
```

## Server Actions

### Server Actions

Server actions are used for form submissions and data mutations:

```tsx
// Example from actions.ts
'use server'

import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";

export async function createProduct(formData: FormData) {
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  
  try {
    const product = await prisma.product.create({
      data: {
        name,
        description,
      },
    });
    
    revalidatePath("/products");
    return { success: true, data: product };
  } catch (error) {
    return { success: false, error: "Failed to create product" };
  }
}
```

### Using Server Actions

Server actions are used in components:

```tsx
// Example from product/create/page.tsx
'use client'

import { createProduct } from "@/app/actions";

const CreateProductPage = () => {
  return (
    <form action={createProduct}>
      <input type="text" name="name" placeholder="Product Name" />
      <textarea name="description" placeholder="Product Description" />
      <button type="submit">Create Product</button>
    </form>
  );
};
```

### Form Validation

Form validation is handled using Zod:

```tsx
// Example from actions.ts
'use server'

import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";

const productSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
});

export async function createProduct(formData: FormData) {
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  
  try {
    const validatedData = productSchema.parse({ name, description });
    
    const product = await prisma.product.create({
      data: validatedData,
    });
    
    revalidatePath("/products");
    return { success: true, data: product };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.errors };
    }
    return { success: false, error: "Failed to create product" };
  }
}
```

## Error Handling

### API Error Handling

API routes handle errors consistently:

```tsx
// Example from api/products/route.ts
import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const products = await prisma.product.findMany();
    return NextResponse.json({ data: products });
  } catch (error) {
    console.error("API Error:", error);
    return NextResponse.json(
      { error: "Failed to fetch products" },
      { status: 500 }
    );
  }
}
```

### Client Error Handling

Client components handle errors using React Query:

```tsx
// Example from browse/page.tsx
import { useQuery } from "@tanstack/react-query";
import { getProducts } from "@/app/util/serverFunctions";

const BrowsePage = () => {
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
    refetchOnWindowFocus: false,
  });

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (isError) {
    return (
      <div className="error-container">
        <h2>Error Loading Products</h2>
        <p>{error.message}</p>
        <button onClick={() => refetch()}>Try Again</button>
      </div>
    );
  }

  return (
    <div>
      {data.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
};
```

### Form Error Handling

Form errors are handled using React Hook Form:

```tsx
// Example from product/create/page.tsx
'use client'

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { createProduct } from "@/app/actions";

const productSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
});

type ProductFormData = z.infer<typeof productSchema>;

const CreateProductPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
  });

  const onSubmit = async (data: ProductFormData) => {
    const result = await createProduct(data);
    if (!result.success) {
      // Handle errors
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input {...register("name")} placeholder="Product Name" />
      {errors.name && <p className="error">{errors.name.message}</p>}
      
      <textarea {...register("description")} placeholder="Product Description" />
      {errors.description && <p className="error">{errors.description.message}</p>}
      
      <button type="submit">Create Product</button>
    </form>
  );
};
```

## Caching Strategies

### React Query Caching

React Query provides built-in caching:

```tsx
// Example from browse/page.tsx
import { useQuery } from "@tanstack/react-query";
import { getProducts } from "@/app/util/serverFunctions";

const BrowsePage = () => {
  const { data, isLoading } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });

  // Component implementation
};
```

### Cache Invalidation

Cache invalidation is handled using React Query:

```tsx
// Example from product/edit/page.tsx
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateProduct } from "@/app/util/serverFunctions";

const EditProductPage = ({ productId }) => {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: updateProduct,
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["product", productId] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  // Component implementation
};
```

### Prefetching

Prefetching is used to improve user experience:

```tsx
// Example from browse/page.tsx
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getProducts, getProduct } from "@/app/util/serverFunctions";

const BrowsePage = () => {
  const queryClient = useQueryClient();
  const { data } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
  });

  const prefetchProduct = (productId: string) => {
    queryClient.prefetchQuery({
      queryKey: ["product", productId],
      queryFn: () => getProduct(productId),
    });
  };

  return (
    <div>
      {data.map((product) => (
        <div
          key={product.id}
          onMouseEnter={() => prefetchProduct(product.id)}
        >
          <ProductCard product={product} />
        </div>
      ))}
    </div>
  );
};
```

## Optimistic Updates

### Optimistic Updates with React Query

Optimistic updates are used to improve user experience:

```tsx
// Example from product/edit/page.tsx
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateProduct } from "@/app/util/serverFunctions";

const EditProductPage = ({ productId }) => {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: updateProduct,
    onMutate: async (newProduct) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["product", productId] });
      
      // Snapshot the previous value
      const previousProduct = queryClient.getQueryData(["product", productId]);
      
      // Optimistically update to the new value
      queryClient.setQueryData(["product", productId], newProduct);
      
      // Return context with the snapshotted value
      return { previousProduct };
    },
    onError: (err, newProduct, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(["product", productId], context.previousProduct);
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ["product", productId] });
    },
  });

  // Component implementation
};
```

## Real-time Updates

### WebSockets

WebSockets are used for real-time updates:

```tsx
// Example from hooks/useWebSocket.ts
import { useEffect, useState } from "react";

export function useWebSocket(url: string) {
  const [data, setData] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const socket = new WebSocket(url);

    socket.onopen = () => {
      setIsConnected(true);
    };

    socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setData(data);
    };

    socket.onerror = (error) => {
      setError(error);
    };

    socket.onclose = () => {
      setIsConnected(false);
    };

    return () => {
      socket.close();
    };
  }, [url]);

  return { data, isConnected, error };
}
```

### Server-Sent Events

Server-Sent Events are used for one-way real-time updates:

```tsx
// Example from hooks/useEventSource.ts
import { useEffect, useState } from "react";

export function useEventSource(url: string) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    const eventSource = new EventSource(url);

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setData(data);
    };

    eventSource.onerror = (error) => {
      setError(error);
      eventSource.close();
    };

    return () => {
      eventSource.close();
    };
  }, [url]);

  return { data, error };
}
```

## Data Validation

### Zod Schema Validation

Zod is used for data validation:

```tsx
// Example from util/validation.ts
import { z } from "zod";

export const productSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  price: z.number().min(0, "Price must be a positive number"),
  category: z.string().min(1, "Category is required"),
  tags: z.array(z.string()).optional(),
});

export const reviewSchema = z.object({
  rating: z.number().min(1, "Rating must be at least 1").max(5, "Rating must be at most 5"),
  title: z.string().min(3, "Title must be at least 3 characters"),
  content: z.string().min(10, "Content must be at least 10 characters"),
  productId: z.string().min(1, "Product ID is required"),
});

export const userSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  bio: z.string().optional(),
});
```

### Form Validation with React Hook Form

React Hook Form is used for form validation:

```tsx
// Example from product/create/page.tsx
'use client'

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { productSchema } from "@/app/util/validation";
import { createProduct } from "@/app/actions";

type ProductFormData = z.infer<typeof productSchema>;

const CreateProductPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
  });

  const onSubmit = async (data: ProductFormData) => {
    const result = await createProduct(data);
    if (!result.success) {
      // Handle errors
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div>
        <label htmlFor="name">Name</label>
        <input id="name" {...register("name")} />
        {errors.name && <p className="error">{errors.name.message}</p>}
      </div>
      
      <div>
        <label htmlFor="description">Description</label>
        <textarea id="description" {...register("description")} />
        {errors.description && <p className="error">{errors.description.message}</p>}
      </div>
      
      <div>
        <label htmlFor="price">Price</label>
        <input id="price" type="number" {...register("price", { valueAsNumber: true })} />
        {errors.price && <p className="error">{errors.price.message}</p>}
      </div>
      
      <div>
        <label htmlFor="category">Category</label>
        <select id="category" {...register("category")}>
          <option value="">Select a category</option>
          <option value="electronics">Electronics</option>
          <option value="clothing">Clothing</option>
          <option value="books">Books</option>
        </select>
        {errors.category && <p className="error">{errors.category.message}</p>}
      </div>
      
      <button type="submit" disabled={isSubmitting}>
        {isSubmitting ? "Creating..." : "Create Product"}
      </button>
    </form>
  );
};
```

## Best Practices

### Data Fetching

1. **Use React Query**: Use React Query for data fetching and caching.
2. **Consistent Query Keys**: Use consistent query keys for caching.
3. **Error Handling**: Handle errors consistently.
4. **Loading States**: Show loading states during data fetching.
5. **Prefetching**: Use prefetching to improve user experience.

### State Management

1. **Local State**: Use local state for component-specific state.
2. **Global State**: Use global state for application-wide state.
3. **Server State**: Use React Query for server state.
4. **Derived State**: Use derived atoms for computed values.

### API Integration

1. **Consistent API Format**: Use a consistent API response format.
2. **Error Handling**: Handle API errors consistently.
3. **Validation**: Validate API responses.
4. **Caching**: Cache API responses appropriately.

### Server Actions

1. **Form Validation**: Validate form data before submission.
2. **Error Handling**: Handle errors consistently.
3. **Revalidation**: Revalidate data after mutations.
4. **Optimistic Updates**: Use optimistic updates for better UX.

### Performance

1. **Caching**: Cache data appropriately.
2. **Prefetching**: Use prefetching to improve user experience.
3. **Pagination**: Use pagination for large data sets.
4. **Virtualization**: Use virtualization for large lists.

### Security

1. **Input Validation**: Validate all user inputs.
2. **Authentication**: Authenticate all API requests.
3. **Authorization**: Authorize all API requests.
4. **CSRF Protection**: Protect against CSRF attacks.
5. **XSS Protection**: Protect against XSS attacks. 