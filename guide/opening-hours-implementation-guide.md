# Opening Hours Implementation Guide

## Overview

This guide outlines the steps to add days of the week to the opening hours of a product in the Review It application. This enhancement will allow businesses to specify which days they are open, providing more accurate information to users.

## Current Implementation

Currently, the product model has:
- `openingHrs`: String (nullable) - Time when the business opens
- `closingHrs`: String (nullable) - Time when the business closes

These fields are used in:
- `NewProductForm.tsx` - For creating new products
- `EditProductForm.tsx` - For editing existing products
- `ProductCard.tsx` - For displaying product information

## Implementation Plan

### 1. Database Schema Update

- [ ] Add a new field to the Product model in `prisma/schema.prisma`:
  ```prisma
  model Product {
    // ... existing fields
    openingDays String[] @default([]) // Array of days the business is open
    // ... existing fields
  }
  ```

### 2. Interface Update

- [ ] Update the `iProduct` interface in `src/app/util/Interfaces.tsx`:
  ```typescript
  export interface iProduct {
    // ... existing fields
    openingDays?: string[]; // Array of days the business is open
    // ... existing fields
  }
  ```

### 3. Form Components Update

#### NewProductForm.tsx

- [ ] Add a new section for selecting opening days
- [ ] Update the initial product state to include an empty openingDays array
- [ ] Add handlers for managing the opening days selection

#### EditProductForm.tsx

- [ ] Add a new section for selecting opening days
- [ ] Update the form to display and edit existing opening days

### 4. API Updates

#### Create Product API

- [ ] Update `src/app/api/create/product/route.ts` to handle the new openingDays field

#### Update Product API

- [ ] Update `src/app/api/update/product/[productId]/route.ts` to handle the new openingDays field
- [ ] Add openingDays to the allowedFields array in the convertToPrismaProductUpdateInput function

### 5. Display Updates

#### ProductCard.tsx

- [ ] Update the display of opening hours to include the days of the week

### 6. Migration Strategy

Since we're adding a new optional field, we don't need to migrate existing data. The field will default to an empty array for existing products.

## Detailed Implementation Steps

### 1. Database Schema Update

1. Update the Prisma schema:
   ```prisma
   model Product {
     // ... existing fields
     openingDays String[] @default([]) // Array of days the business is open
     // ... existing fields
   }
   ```

2. Run the Prisma migration:
   ```bash
   npx prisma migrate dev --name add_opening_days
   ```

### 2. Interface Update

1. Update the `iProduct` interface in `src/app/util/Interfaces.tsx`:
   ```typescript
   export interface iProduct {
     // ... existing fields
     openingDays?: string[]; // Array of days the business is open
     // ... existing fields
   }
   ```

### 3. Form Components Update

#### NewProductForm.tsx

1. Update the initial product state:
   ```typescript
   const initialProduct: iProduct = {
     // ... existing fields
     openingDays: [],
     // ... existing fields
   };
   ```

2. Add a new section for selecting opening days:
   ```tsx
   <div>
     <Label>Opening Days</Label>
     <div className="flex flex-wrap gap-2 mt-2">
       {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
         <Button
           key={day}
           type="button"
           variant={product.openingDays?.includes(day) ? "default" : "outline"}
           size="sm"
           onClick={() => {
             if (product.openingDays?.includes(day)) {
               setProduct({
                 ...product,
                 openingDays: product.openingDays.filter(d => d !== day)
               });
             } else {
               setProduct({
                 ...product,
                 openingDays: [...(product.openingDays || []), day]
               });
             }
           }}
         >
           {day}
         </Button>
       ))}
     </div>
   </div>
   ```

#### EditProductForm.tsx

1. Add a similar section for selecting opening days as in NewProductForm.tsx

### 4. API Updates

#### Create Product API

1. Update `src/app/api/create/product/route.ts`:
   ```typescript
   const createdProduct: iProduct = await prisma.product.create({
     data: {
       // ... existing fields
       openingDays: product.openingDays || [],
       // ... existing fields
     },
   });
   ```

#### Update Product API

1. Update `src/app/api/update/product/[productId]/route.ts`:
   ```typescript
   function convertToPrismaProductUpdateInput(
     partialProduct: Partial<iProduct>,
   ): Prisma.ProductUpdateInput {
     const allowedFields: (keyof iProduct)[] = [
       // ... existing fields
       "openingDays",
       // ... existing fields
     ];

     // ... existing code

     for (const field of allowedFields) {
       if (field in partialProduct) {
         switch (field) {
           // ... existing cases
           case "openingDays":
             updateInput[field] = partialProduct[field] as string[];
             break;
           // ... existing cases
         }
       }
     }

     return updateInput;
   }
   ```

### 5. Display Updates

#### ProductCard.tsx

1. Update the display of opening hours:
   ```tsx
   {currentProduct?.openingHrs && currentProduct?.closingHrs && (
     <p className="text-sm text-gray-600 flex items-center">
       <MdAccessTime className="shrink-0 mr-1" />
       {currentProduct.openingDays && currentProduct.openingDays.length > 0 
         ? `${currentProduct.openingDays.join(', ')}: ${currentProduct.openingHrs} - ${currentProduct.closingHrs}`
         : `${currentProduct.openingHrs} - ${currentProduct.closingHrs}`}
     </p>
   )}
   ```

## Testing

1. Create a new product with opening days
2. Edit an existing product to add opening days
3. Verify that the opening days are displayed correctly in the product card
4. Test the API endpoints to ensure they handle the new field correctly

## Conclusion

This implementation adds the ability to specify which days a business is open, providing more accurate information to users. The changes are backward compatible with existing data, as the new field is optional and defaults to an empty array. 