## Review-It Development Guide

### Current Tasks

#### 1. Product Showcase Section
- [x] Create new ProductShowcase component
  - [x] Design component layout
  - [x] Handle images display
  - [x] Handle links display
  - [x] Handle websites display
  - [x] Add conditional rendering
  - [x] Add image modal/lightbox feature
  - [x] Improve mobile responsiveness
- [x] Integrate ProductShowcase into Reviews page
  - [x] Add component below ProductCard
  - [x] Pass required props

### Completed Tasks
- Created ProductShowcase component with responsive design
- Integrated ProductShowcase into Reviews page
- Implemented conditional rendering based on content availability
- Added proper TypeScript interfaces and types
- Followed existing project structure and styling patterns
- Added image modal/lightbox feature with:
  - Full-screen image view
  - Click outside to close
  - Escape key to close
  - Accessible close button
  - Smooth transitions
- Mobile improvements:
  - Optimized spacing and padding for small screens
  - Responsive typography
  - Better image grid layout on mobile
  - Improved link display with text wrapping
  - Enhanced touch targets
  - Prevent body scroll when modal is open
  - Optimized modal layout for mobile viewing

### Code Standards
1. Use TypeScript for all new components
2. Follow existing project structure
3. Implement responsive design
4. Use existing styling patterns
5. Add proper error handling
6. Include loading states where necessary
7. Add appropriate comments
8. Follow existing naming conventions

### Component Guidelines
1. Create reusable components when possible
2. Use proper TypeScript interfaces
3. Implement error boundaries where needed
4. Follow accessibility best practices
5. Optimize performance
6. Add proper testing

### TypeScript Interfaces
1. All interfaces are defined in `src/app/util/Interfaces.tsx`
2. Key interfaces include:
   - `iProduct`: Product data structure
   - `iReview`: Review data structure
   - `iUser`: User data structure
   - `iBusiness`: Business data structure
   - `iComment`: Comment data structure
   - `iNotification`: Notification data structure
3. When creating new components:
   - Import interfaces from `@/app/util/Interfaces`
   - Use existing interfaces when possible
   - Extend existing interfaces if needed
   - Document any new interfaces added
4. Interface usage guidelines:
   - Always use TypeScript interfaces for props
   - Include proper type checking
   - Use optional properties (?) when appropriate
   - Document complex interface relationships
   - Keep interfaces focused and single-purpose 