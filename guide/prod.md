# Product Detail Page Implementation Guide

## Overview

This guide outlines the implementation of a comprehensive product detail page that displays all available information from the `iProduct` interface, including images, ratings, reviews, and more.

## Page Structure

We'll organize the page into these sections:

1. **Hero Section** - Product name, main image, and key details
2. **Gallery** - All product images and videos
3. **Details Section** - Complete product information
4. **Rating & Statistics** - Visual representation of ratings
5. **Reviews Section** - Recent reviews with pagination
6. **Related Information** - Business info, tags, etc.
7. **Related Products** - Products with similar tags

## Implementation Checklist

### 1. Hero Section

- [x] Create responsive hero layout with main image and product name
- [x] Add rating summary with star display
- [x] Include quick action buttons (share, save, etc.)
- [x] Display verification badge if product has owner
- [x] Add view count display
- [x] Implement lazy loading for the main image

### 2. Gallery Section

- [x] Implement image carousel/grid for all product images
- [x] Add video player for product videos
- [x] Create lightbox for full-screen image viewing
- [x] Add thumbnails for quick navigation
- [x] Implement lazy loading for gallery images
- [x] Add image alt text for accessibility

### 3. Details Section

- [x] Format and display product description
- [x] Create contact information card (phone, email, website)
- [x] Add business hours display (opening/closing times)
- [x] Include address with optional map integration
- [x] Display all links with proper formatting
- [x] Add schema.org markup for product details

### 4. Rating & Statistics

- [x] Create star rating distribution chart
- [x] Add rating breakdown by star level (5★, 4★, etc.)
- [x] Display total review count and average rating
- [x] Add view count statistic
- [x] Implement interactive rating visualization

### 5. Reviews Section

- [x] Implement recent reviews display (3-5 reviews)
- [x] Add "See all reviews" link
- [x] Include review sorting options
- [x] Show review highlights (most helpful)
- [x] Implement pagination for reviews
- [x] Add review filtering options
- [x] Display review images in a gallery format

### 6. Related Information

- [x] Display business information if available
- [x] Show tags with filtering capability
- [x] Add "Created by" information
- [x] Include creation and update dates
- [x] Add related products section if available

### 7. SEO & Performance

- [x] Implement proper meta tags
- [x] Add structured data (schema.org)
- [x] Optimize images with next/image
- [x] Implement code splitting for large components
- [x] Add Open Graph and Twitter card meta tags
- [x] Ensure proper canonical URLs

### 8. Accessibility

- [x] Add proper ARIA attributes
- [x] Ensure keyboard navigation
- [x] Add proper focus management
- [x] Ensure sufficient color contrast
- [x] Add skip links for screen readers

## New Components Implementation

### Related Products Component

The RelatedProducts component intelligently displays products that are related to the current one based on shared tags:

```tsx
// Related products based on shared tags
<section className="bg-white rounded-xl shadow-sm p-6 mb-6">
    <h2 className="text-xl font-semibold mb-4">Related Products</h2>
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {product.relatedProducts?.map((relatedProduct) => (
            <Link
                key={relatedProduct.id}
                href={`/product/${relatedProduct.id}`}
                className="block group"
            >
                <div className="flex items-start gap-3 p-3 rounded-lg transition-all hover:bg-gray-50 border border-transparent hover:border-gray-200">
                    <div className="relative w-16 h-16 sm:w-20 sm:h-20 rounded-md overflow-hidden flex-shrink-0">
                        <Image
                            src={relatedProduct.display_image}
                            alt={relatedProduct.name}
                            fill
                            className="object-cover"
                            loading="lazy"
                            sizes="(max-width: 768px) 64px, 80px"
                        />
                    </div>
                    <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm sm:text-base line-clamp-1 group-hover:text-blue-600 transition-colors">
                            {relatedProduct.name}
                        </h3>
                        <p className="text-gray-500 text-xs sm:text-sm line-clamp-2 mt-1">
                            {relatedProduct.description}
                        </p>
                        <div className="flex items-center gap-1 mt-2">
                            <Star className="h-3 w-3 text-yellow-400" />
                            <span className="text-xs sm:text-sm font-medium">
                                {relatedProduct.rating.toFixed(1)}
                            </span>
                            <span className="text-xs text-gray-500">
                                ({relatedProduct._count?.reviews} reviews)
                            </span>
                        </div>
                    </div>
                </div>
            </Link>
        ))}
    </div>
</section>
```

### Review Filtering and Sorting

The ReviewFilter component provides comprehensive filtering and sorting capabilities:

```tsx
<ReviewFilter
    onSortChange={setSort}
    onRatingFilterChange={setRatingFilter}
    onHasImagesChange={setHasImages}
    onHasVideosChange={setHasVideos}
    selectedSort={sort}
    selectedRating={ratingFilter}
    hasImages={hasImages}
    hasVideos={hasVideos}
    totalReviews={filteredReviews.length}
/>
```

Key features:
- Sort by newest, oldest, highest rating, lowest rating, and most helpful
- Filter by star rating (5★, 4★, 3★, 2★, 1★)
- Filter to show only reviews with images or videos
- Mobile-friendly interface with sliding panel for filters
- Clear filters option

### Review Pagination

The ReviewPagination component handles navigation through review pages:

```tsx
<ReviewPagination
    currentPage={currentPage}
    totalPages={totalPages}
    onPageChange={handlePageChange}
/>
```

Features:
- Next/previous navigation buttons
- Page number buttons with current page highlighted
- Smart ellipsis display for many pages
- Accessibility support with ARIA attributes
- Responsive design for all screen sizes

### Dedicated Reviews Page

A dedicated reviews page at `/reviews/[productId]` that provides:

- Full list of reviews with filtering and sorting
- Rating summary and distribution
- Product information sidebar
- "Write a Review" call-to-action
- Navigation back to product page

## Data Fetching Enhancement

The `getProductById` function now includes related products based on shared tags:

```typescript
// Fetch related products based on tags
let relatedProducts: any[] = [];
if (product.tags && product.tags.length > 0) {
    relatedProducts = await prisma.product.findMany({
        where: {
            id: { not: id },  // Exclude the current product
            tags: { hasSome: product.tags },  // Find products with at least one matching tag
            isDeleted: false
        },
        take: 4, // Limit to 4 related products
        include: {
            _count: {
                select: {
                    reviews: true,
                },
            },
        },
        orderBy: {
            createdDate: 'desc',
        }
    });
}

return {
    ...product,
    rating: averageRating,
    relatedProducts: relatedProducts,
};
```

## Error Handling

All components follow robust error handling practices:

1. **Null Checking**: All components check if properties exist before using them
   ```tsx
   {product.telephone && <div>Phone: {product.telephone}</div>}
   ```

2. **Default Values**: Empty fallbacks for missing data
   ```tsx
   <span>{product._count?.reviews || 0} reviews</span>
   ```

3. **Optional Chaining**: Safe access for nested properties
   ```tsx
   {review.user?.firstName} {review.user?.lastName}
   ```

4. **Conditional Rendering**: Components don't render if required data is missing
   ```tsx
   if (!product.relatedProducts || product.relatedProducts.length === 0) {
       return null;
   }
   ```

5. **Empty States**: User-friendly messaging when no data matches criteria
   ```tsx
   <div className="text-center py-8 text-gray-500">
       <p>No reviews match your filter criteria.</p>
       <Button 
           variant="outline" 
           className="mt-4"
           onClick={handleResetFilters}
       >
           Clear Filters
       </Button>
   </div>
   ```

## Next Steps

Future enhancements to consider:

1. Implement review submission functionality
2. Add user interaction tracking and analytics
3. Implement A/B testing for key features
4. Add more sophisticated related product algorithms
5. Enhance mobile experience with gestures for gallery navigation
6. Implement review helpfulness voting
7. Add review comment functionality
8. Implement product search with autocomplete

## Interface Usage

All components should use the interfaces defined in `src/app/util/Interfaces.tsx`. The key interfaces for the product detail page are:

1. **iProduct**: The main product interface containing all product data
   ```typescript
   interface iProduct {
     id?: string;
     address?: string | null;
     createdDate: Date;
     description: string;
     display_image: string;
     images: string[];
     videos: string[];
     links: string[];
     name: string;
     tags: string[];
     openingHrs?: string | null;
     closingHrs?: string | null;
     telephone?: string | null;
     website: string[];
     rating: number;
     hasOwner?: boolean | null;
     ownerId?: string | null;
     createdById: string;
     isDeleted: boolean;
     email?: string | null;
     businessId?: string | null;
     updatedAt?: Date | null;
     viewCount?: number;
     rating5Stars?: number;
     rating4Stars?: number;
     rating3Stars?: number;
     rating2Stars?: number;
     rating1Star?: number;
     featuredPosition?: number | null;
     business?: iBusiness | null;
     createdBy?: iUser | null;
     reviews?: iReview[];
     _count?: {
       reviews?: number;
     };
   }
   ```

2. **iReview**: The review interface for displaying product reviews
   ```typescript
   interface iReview {
     id?: string;
     body: string;
     createdDate?: Date;
     helpfulVotes?: number;
     unhelpfulVotes?: number;
     rating: number;
     title: string;
     productId: string;
     userId: string;
     isVerified?: boolean;
     verifiedBy?: string;
     isPublic: boolean;
     images: string[];
     videos: string[];
     links: string[];
     createdBy?: string;
     isDeleted?: boolean;
     verifiedAt?: Date;
     moderationHistory?: iModerationEvent[];
     product?: iProduct | null;
     user?: iUser | null;
     comments: iComment[];
     voteCount?: iVoteCount | null;
     likedBy: iUser[];
   }
   ```

3. **iBusiness**: The business interface for displaying business information
   ```typescript
   interface iBusiness {
     id: string;
     owner: iUser;
     ownerId: string;
     businessDescription: string | null;
     subscriptionStatus: string;
     subscriptionExpiry: Date | null;
     products: iProduct[];
     createdDate: Date | null;
     isVerified: boolean | null;
     ownerName?: string | null;
   }
   ```

4. **iUser**: The user interface for displaying user information
   ```typescript
   interface iUser {
     id: string;
     bio?: string | null;
     userName: string;
     avatar: string | null;
     createdDate: Date;
     email: string;
     firstName: string;
     lastName: string;
     clerkUserId: string;
     isDeleted: boolean | null;
     role?: 'USER' | 'ADMIN';
     status?: 'ACTIVE' | 'SUSPENDED' | 'BANNED';
     lastLoginAt?: Date;
     loginCount?: number;
     suspendedUntil?: Date;
     suspendedReason?: string;
     reviews?: iReview[];
     product?: iProduct[];
     createdBy?: iUser | null;
     createdById?: string;
     comments?: iComment[];
     likedReviews?: iReview[];
     businesses?: iBusiness[];
     _count?: {
       reviews?: number;
       comments?: number;
       product?: number;
     };
     adminActions?: iAdminAction[];
     moderationEvents?: iModerationEvent[];
     commentVotes?: iCommentVote[];
   }
   ```

When creating components, always import these interfaces from `@/app/util/Interfaces.tsx` and use them for proper type checking.
