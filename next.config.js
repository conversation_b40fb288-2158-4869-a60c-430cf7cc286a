const withPWA = require("@ducanh2912/next-pwa").default({
  dest: "public",
  runtimeCaching: [
    {
      urlPattern: /\/offline/,
      handler: "NetworkOnly",
    },
    {
      // Exclude metadata and OG image routes from caching
      urlPattern: /^https?:\/\/[^/]+\/(?:api\/og|api\/metadata|fr)/,
      handler: "NetworkOnly",
    },
    {
      // Exclude top-reviewers API from caching
      urlPattern: /\/api\/top-reviewers/,
      handler: "NetworkOnly",
    },
    {
      urlPattern: /^https?.*/,
      handler: "NetworkFirst",
      options: {
        cacheName: "offlineFallback",
        expiration: {
          maxEntries: 200,
        },
        cacheableResponse: {
          statuses: [0, 200],
        },
      },
    },
  ],
});

module.exports = withPWA({
  reactStrictMode: false,
  images: {
    remotePatterns: [
      { hostname: "i5.walmartimages.com", protocol: "https" },
      { hostname: "newsroom.gy", protocol: "https" },
      { hostname: "img.clerk.com", protocol: "https" },
      { hostname: "res.cloudinary.com", protocol: "https" },
      { hostname: "cloudflare-ipfs.com", protocol: "https" },
      { hostname: "loremflickr.com", protocol: "https" },
      { hostname: "placehold.co", protocol: "https" },
      { hostname: "images.clerk.dev", protocol: "https" },
      { hostname: "images.unsplash.com", protocol: "https" },
      { hostname: "picsum.photos", protocol: "https" },
      { hostname: "img.youtube.com", protocol: "https" },
      // TikTok CDN domains
      { hostname: "p16-sign-va.tiktokcdn.com", protocol: "https" },
      { hostname: "p16-sign-sg.tiktokcdn.com", protocol: "https" },
      { hostname: "p77-sign-va.tiktokcdn.com", protocol: "https" },
      { hostname: "p77-sign-sg.tiktokcdn.com", protocol: "https" },
      { hostname: "p16-sign-useast2a.tiktokcdn.com", protocol: "https" },
    ],
  },
  async redirects() {
    return [
      {
        source: '/',
        destination: '/',
        has: [
          {
            type: 'query',
            key: '_rsc',
          }
        ],
        permanent: true,
      },
      {
        source: '/:path*',
        destination: '/:path*',
        has: [
          {
            type: 'query',
            key: '_rsc',
          }
        ],
        permanent: true,
      }
    ]
  },
  async headers() {
    return [
      {
        // Add noindex for API routes
        source: '/api/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow',
          },
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          }
        ],
      },
      {
        // Specific headers for review pages to ensure proper metadata handling
        source: '/fr',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'index, follow, max-image-preview:large, max-snippet:-1',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          }
        ],
      },
      {
        // Only set index headers for specific public routes
        // This allows robots.ts to handle the rest
        source: '/(browse|reviews|category|pricing|submit|install)/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'index, follow, max-image-preview:large, max-snippet:-1',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          }
        ],
      },
      {
        // Set index headers for the homepage
        source: '/',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'index, follow, max-image-preview:large, max-snippet:-1',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          }
        ],
      },
      {
        // Add CORS headers for all routes
        source: '/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: 'https://www.reviewit.gy, https://reviewit.gy, http://localhost:3000',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ];
  },
  // headers: [
  //   {
  //     source: '/fr',
  //     headers: [
  //       {
  //         key: 'Cache-Control',
  //         value: 'no-cache',
  //       },
  //     ],
  //   },
  // ],
});
