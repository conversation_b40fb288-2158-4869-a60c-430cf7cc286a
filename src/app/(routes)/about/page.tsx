import React from 'react';
import Link from 'next/link';
import { Shield, Users, Star, HelpCircle } from 'lucide-react';

export const metadata = {
    title: 'About Review It',
    description: 'Learn about Review It, our mission, and how we help users find trustworthy product information.',
};

export default function AboutPage() {
    return (
        <div className="container mx-auto px-4 py-8 max-w-4xl">
            <h1 className="text-3xl font-bold mb-8">About Review It</h1>

            <div className="prose prose-lg">
                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Our Mission</h2>
                    <p>
                        At Review It, we&apos;re on a mission to provide users with reliable, trustworthy information
                        about products and businesses. We believe that informed consumers make better decisions,
                        and we&apos;re committed to creating a platform where users can find authentic reviews and
                        accurate product information.
                    </p>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">What We Do</h2>
                    <p>
                        Review It is a comprehensive platform that:
                    </p>
                    <ul className="list-disc pl-6 mt-2">
                        <li>Aggregates and verifies product reviews from multiple sources</li>
                        <li>Provides detailed product information and specifications</li>
                        <li>Connects users with businesses and product manufacturers</li>
                        <li>Offers a platform for businesses to engage with their customers</li>
                        <li>Ensures the authenticity and reliability of information through our verification process</li>
                    </ul>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Our Values</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
                            <div className="flex items-center mb-3">
                                <Shield className="h-6 w-6 text-blue-600 mr-2" />
                                <h3 className="text-xl font-medium">Trust & Transparency</h3>
                            </div>
                            <p>
                                We believe in building trust through transparency. Our verification process
                                ensures that the information on our platform is accurate and reliable.
                            </p>
                        </div>

                        <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
                            <div className="flex items-center mb-3">
                                <Users className="h-6 w-6 text-blue-600 mr-2" />
                                <h3 className="text-xl font-medium">Community</h3>
                            </div>
                            <p>
                                We foster a community where users can share their experiences and businesses
                                can engage with their customers in meaningful ways.
                            </p>
                        </div>

                        <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
                            <div className="flex items-center mb-3">
                                <Star className="h-6 w-6 text-blue-600 mr-2" />
                                <h3 className="text-xl font-medium">Quality</h3>
                            </div>
                            <p>
                                We&apos;re committed to maintaining high standards of quality in all aspects of
                                our platform, from the information we provide to the user experience we offer.
                            </p>
                        </div>

                        <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
                            <div className="flex items-center mb-3">
                                <HelpCircle className="h-6 w-6 text-blue-600 mr-2" />
                                <h3 className="text-xl font-medium">Support</h3>
                            </div>
                            <p>
                                We provide excellent support to both users and businesses, ensuring that
                                everyone has a positive experience on our platform.
                            </p>
                        </div>
                    </div>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Learn More</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Link href="/about/verification" className="block p-6 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                            <h3 className="text-xl font-medium mb-2">Our Verification Process</h3>
                            <p className="text-gray-600">
                                Learn about how we verify products and businesses to ensure the trustworthiness of our platform.
                            </p>
                        </Link>

                        <Link href="/faq" className="block p-6 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                            <h3 className="text-xl font-medium mb-2">Frequently Asked Questions</h3>
                            <p className="text-gray-600">
                                Find answers to common questions about Review It and how to use our platform.
                            </p>
                        </Link>
                    </div>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Join Our Community</h2>
                    <p>
                        Whether you&apos;re a user looking for reliable product information or a business owner
                        wanting to engage with your customers, Review It has something for you.
                    </p>
                    <div className="mt-6 flex flex-wrap gap-4">
                        <Link href="/submit" className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors">
                            Submit a Review
                        </Link>
                        <Link href="/claim-business" className="inline-flex items-center px-6 py-3 bg-white text-blue-600 border border-blue-600 font-medium rounded-md hover:bg-blue-50 transition-colors">
                            Claim Your Business
                        </Link>
                    </div>
                </section>
            </div>
        </div>
    );
} 