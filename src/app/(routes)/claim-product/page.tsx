import React from "react";
import { Metadata } from "next";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import ProductClaimForm from "@/app/components/ProductClaimForm";

export const metadata: Metadata = {
  title: "Claim Your Product | Review It",
  description:
    "Claim ownership of a product by providing contact information, additional details, and supporting images.",
};

export default async function ClaimProductPage() {
  const { userId } = await auth();

  if (!userId) {
    redirect("https://accounts.reviewit.gy/sign-in");
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-100 py-10 px-4 sm:px-6 lg:px-8">
      <div className="max-w-5xl mx-auto">
        <div className="mb-8 text-center sm:text-left">
          <h1 className="text-3xl font-bold text-myTheme-primary">
            Claim Your Product
          </h1>
          <p className="text-gray-600 mt-2">
            Provide information to claim ownership of a product
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-xl font-semibold mb-2">
              How to Claim a Product
            </h2>
            <ol className="list-decimal pl-5 space-y-2 text-gray-600">
              <li>Search for the product you want to claim</li>
              <li>
                Provide your contact information so administrators can reach you
              </li>
              <li>Explain why you should own this product</li>
              <li>
                Upload supporting images that prove your connection to the
                product
              </li>
              <li>Submit your claim for review</li>
            </ol>
            <p className="mt-4 text-sm text-gray-500">
              Your claim will be reviewed by our administrators. You will be
              notified of the decision through your account and email.
            </p>
          </div>

          <div className="p-6">
            <ProductClaimForm />
          </div>
        </div>

        {/* Product Not Listed Section */}
        <div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-6 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Your product not listed?
            </h3>
            <p className="text-gray-600 mb-4">
              Can&apos;t find your product in our database? Add it to our platform first.
            </p>
            <a
              href="/submit"
              className="inline-flex items-center px-6 py-3 bg-myTheme-primary text-white font-medium rounded-lg hover:bg-myTheme-primary/90 transition-colors duration-200"
            >
              Add Your Product
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
