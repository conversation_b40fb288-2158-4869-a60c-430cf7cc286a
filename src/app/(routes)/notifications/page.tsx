"use client"
import AllNotifications from '@/app/components/notification-components/AllNotifications'
import React from 'react'
import { useAtom } from "jotai";
import { ownerNotificationsAtom } from "@/app/store/store";
import { userNotificationsAtom } from "@/app/store/store";
import { Bell } from 'lucide-react';
import { useSearchParams } from 'next/navigation';

const NotificationsPage = () => {
  const [ONA] = useAtom(ownerNotificationsAtom);
  const [UNA] = useAtom(userNotificationsAtom);
  const searchParams = useSearchParams();
  const productId = searchParams.get('productId');

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <div className="max-w-7xl mx-auto px-3 py-4 sm:px-6 sm:py-8 lg:px-8">
        {/* Header Section */}
        <div className="text-center mb-4 sm:mb-8">
          <div className="flex items-center justify-center gap-2 mb-2 sm:mb-3">
            <Bell className="w-5 h-5 sm:w-6 sm:h-6 text-myTheme-primary" />
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Notifications</h1>
          </div>
          <p className="text-sm sm:text-base text-gray-600 max-w-2xl mx-auto px-4">
            Stay updated with the latest activity on your reviews and products
          </p>
        </div>

        {/* Notifications Content */}
        <div className="max-w-3xl mx-auto">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-3 sm:p-6">
            <AllNotifications UNA={UNA} ONA={ONA} productId={productId} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default NotificationsPage
