import UserProfileComponent from "@/app/components/UserProfileComponent";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { getUserWithId } from "@/app/util/serverFunctions";
import { iUser } from "@/app/util/Interfaces";
import { User, Settings, Star, MessageCircle, Sparkles } from "lucide-react";

export default async function Page() {
  const { userId } = await auth();

  if (!userId) {
    redirect("https://accounts.reviewit.gy/sign-in");
  }

  // Get user data from our database
  const userData = await getUserWithId(userId);

  if (!userData.success || !userData.data) {
    // If we can't get the user data, redirect to sign in
    redirect("https://accounts.reviewit.gy/sign-in");
  }

  const user = userData.data as iUser;

  const reviewCount = user._count?.reviews || 0;
  const reviews = user.reviews || [];
  const totalRating = reviews.reduce((acc, review) => acc + review.rating, 0);
  const averageRating = reviews.length > 0 ? totalRating / reviews.length : 1;

  // Generate random gradients
  const gradientColors = [
    'from-rose-50 via-white to-pink-50',
    'from-blue-50 via-white to-indigo-50', 
    'from-green-50 via-white to-emerald-50',
    'from-purple-50 via-white to-violet-50',
    'from-orange-50 via-white to-amber-50',
    'from-cyan-50 via-white to-teal-50',
    'from-indigo-50 via-white to-cyan-50'
  ];
  
  const animatedGradients = [
    ['from-rose-400/20 to-pink-600/20', 'from-pink-400/20 to-rose-600/20', 'from-rose-400/10 to-pink-600/10'],
    ['from-blue-400/20 to-purple-600/20', 'from-cyan-400/20 to-blue-600/20', 'from-purple-400/10 to-pink-600/10'],
    ['from-green-400/20 to-emerald-600/20', 'from-emerald-400/20 to-teal-600/20', 'from-green-400/10 to-emerald-600/10'],
    ['from-purple-400/20 to-violet-600/20', 'from-violet-400/20 to-purple-600/20', 'from-purple-400/10 to-violet-600/10'],
    ['from-orange-400/20 to-amber-600/20', 'from-amber-400/20 to-yellow-600/20', 'from-orange-400/10 to-amber-600/10'],
    ['from-cyan-400/20 to-teal-600/20', 'from-teal-400/20 to-cyan-600/20', 'from-cyan-400/10 to-teal-600/10'],
    ['from-indigo-400/20 to-blue-600/20', 'from-blue-400/20 to-indigo-600/20', 'from-indigo-400/10 to-blue-600/10']
  ];
  
  const randomIndex = Math.floor(Math.random() * gradientColors.length);
  const selectedGradient = gradientColors[randomIndex];
  const selectedAnimated = animatedGradients[randomIndex];

  return (
    <div className={`min-h-screen bg-gradient-to-br ${selectedGradient} relative`}>
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br ${selectedAnimated[0]} rounded-full blur-3xl animate-pulse`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br ${selectedAnimated[1]} rounded-full blur-3xl animate-pulse delay-1000`}></div>
        <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br ${selectedAnimated[2]} rounded-full blur-3xl animate-pulse delay-500`}></div>
      </div>

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-cyan-600/5"></div>
        <div className="relative pt-20 pb-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            {/* Welcome Message */}
            <div className="text-center mb-8">
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent mb-6 leading-tight">
                Welcome to Your Profile
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Manage your reviews, track your activity, and connect with the community
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative px-4 sm:px-6 lg:px-8 pb-12">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white/80 backdrop-blur-md rounded-3xl shadow-2xl border border-white/50 overflow-hidden transform hover:shadow-3xl transition-all duration-500">
            <UserProfileComponent userIdFromParams={user.id} />
          </div>
        </div>
      </div>
    </div>
  );
}
