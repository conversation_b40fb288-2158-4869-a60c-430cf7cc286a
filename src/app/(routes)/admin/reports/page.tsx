'use client';

import React, { useState } from 'react';
import ReportsTable from '@/app/components/admin/ReportsTable';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import ReportsDashboard from '@/app/components/admin/ReportsDashboard';
import { iReviewReport } from '@/app/util/Interfaces';

export default function ReportsPage() {
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [selectedReport, setSelectedReport] = useState<iReviewReport | null>(null);

    const handleViewDetails = (reportId: string) => {
        // TODO: Implement view details functionality
        console.log('View details for report:', reportId);
    };

    const handleStatusChange = async (reportId: string, status: string) => {
        try {
            const response = await fetch(`/api/admin/reports/${reportId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ status }),
            });

            if (!response.ok) {
                throw new Error('Failed to update report status');
            }

            // Refresh the reports list
            // You might want to implement a refresh function here
        } catch (error) {
            console.error('Error updating report status:', error);
        }
    };

    return (
        <div className="px-4 py-6 md:px-6 space-y-6">
            <div className="flex flex-col gap-2">
                <h1 className="text-2xl font-bold tracking-tight">Report Management</h1>
                <p className="text-muted-foreground">
                    Review and manage user reports on inappropriate or problematic content.
                </p>
            </div>

            <Tabs defaultValue="all">
                <TabsList>
                    <TabsTrigger value="all">All Reports</TabsTrigger>
                    <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="mt-6">
                    <ReportsTable
                        onViewDetails={handleViewDetails}
                        onStatusChange={handleStatusChange}
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={setCurrentPage}
                    />
                </TabsContent>

                <TabsContent value="dashboard" className="mt-6">
                    <ReportsDashboard />
                </TabsContent>
            </Tabs>
        </div>
    );
} 