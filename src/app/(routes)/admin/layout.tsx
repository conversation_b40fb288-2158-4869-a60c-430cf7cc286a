"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  Package2,
  BarChart3,
  Users,
  FileText,
  ChevronLeft,
  Menu,
  Database,
  X,
  Bug,
  Flag,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { useAuth } from "@clerk/nextjs";

import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const { userId, isLoaded } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isCheckingAdmin, setIsCheckingAdmin] = useState(true);
  const [isDocsExpanded, setIsDocsExpanded] = useState(false);

  // Check admin permissions
  useEffect(() => {
    if (isLoaded && !userId) {
      router.push("https://accounts.reviewit.gy/sign-in");
      return;
    }

    const checkAdminPermission = async () => {
      try {
        setIsCheckingAdmin(true);
        const response = await fetch('/api/admin/check-permission');
        const data = await response.json();

        if (!data.success || !data.isAdmin) {
          router.push('/');
          return;
        }

        setIsAdmin(true);
      } catch (error) {
        console.error('Error checking admin permission:', error);
        router.push('/');
      } finally {
        setIsCheckingAdmin(false);
      }
    };

    if (isLoaded && userId) {
      checkAdminPermission();
    }
  }, [isLoaded, userId, router]);

  // Auto-expand docs section if we're on a docs page
  useEffect(() => {
    if (pathname.startsWith('/admin/docs')) {
      setIsDocsExpanded(true);
    }
  }, [pathname]);

  // Show loading while checking admin status
  if (!isLoaded || isCheckingAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Verifying admin access...</p>
        </div>
      </div>
    );
  }

  // Don't render admin interface if not admin
  if (!isAdmin) {
    return null;
  }

  // Get the current page title based on the pathname
  const getPageTitle = () => {
    if (pathname === "/admin") return "Dashboard";
    if (pathname === "/admin/reviews") return "Reviews";
    if (pathname === "/admin/users") return "Users";
    if (pathname === "/admin/products") return "Products";
    if (pathname === "/admin/claims") return "Product Claims";
    if (pathname === "/admin/words") return "Word Management";
    return "Admin";
  };

  // Check if a nav item is active
  const isActive = (path: string) => {
    if (path === "/admin" && pathname === "/admin") return true;
    if (path !== "/admin" && pathname.startsWith(path)) return true;
    return false;
  };

  // Navigation items
  const navItems = [
    {
      path: "/admin",
      label: "Dashboard",
      icon: <BarChart3 className="w-5 h-5" />,
    },
    {
      path: "/admin/reviews",
      label: "Reviews",
      icon: <FileText className="w-5 h-5" />,
    },
    {
      path: "/admin/users",
      label: "Users",
      icon: <Users className="w-5 h-5" />,
    },
    {
      path: "/admin/products",
      label: "Products",
      icon: <Package2 className="w-5 h-5" />,
    },
    {
      path: "/admin/claims",
      label: "Product Claims",
      icon: <FileText className="w-5 h-5" />,
    },
    {
      path: "/admin/reports",
      label: "Reports",
      icon: <Flag className="w-5 h-5" />,
    },
    {
      path: "/admin/words",
      label: "Word Management",
      icon: <Database className="w-5 h-5" />,
    },
    {
      path: "/admin/bug-reports",
      label: "Bug Reports",
      icon: <Bug className="w-5 h-5" />,
    },
    {
      path: "/admin/docs",
      label: "Documentation",
      icon: <FileText className="w-5 h-5" />,
      expandable: true,
      subItems: [
        {
          path: "/admin/docs/project-overview",
          label: "Project Overview",
        },
        {
          path: "/admin/docs/user-management",
          label: "User Management",
        },
        {
          path: "/admin/docs/product-management",
          label: "Product Management",
        },
        {
          path: "/admin/docs/review-management",
          label: "Review Management",
        },
        {
          path: "/admin/docs/bug-reports",
          label: "Bug Reports",
        },
        {
          path: "/admin/docs/promotions",
          label: "Promotions",
        },
        {
          path: "/admin/docs/api-routes",
          label: "API Routes",
        },
        {
          path: "/admin/docs/core-concepts",
          label: "Core Concepts",
        },
        {
          path: "/admin/docs/user-facing",
          label: "User-Facing Routes",
        },
        {
          path: "/admin/docs/owner-business",
          label: "Owner/Business Section",
        },
      ],
    },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="sticky top-0 z-10 flex items-center justify-between h-16 px-4 md:px-6 border-b bg-background">
        <div className="flex items-center gap-4">
          <Link href="/" className="flex items-center gap-2 text-sm">
            <ChevronLeft className="w-4 h-4" />
            <span>Back to Site</span>
          </Link>
          <div className="h-6 border-l mx-2"></div>
          <Link href="/admin" className="flex items-center gap-2 font-semibold">
            <Package2 className="w-5 h-5" />
            <span className="hidden sm:inline">Review It Admin</span>
            <span className="sm:hidden">Admin</span>
          </Link>
        </div>

        {/* Mobile menu trigger */}
        <div className="md:hidden">
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-64 p-0 bg-white">
              <div className="p-4 border-b bg-gray-50">
                <Link
                  href="/admin"
                  className="flex items-center gap-2 font-semibold"
                >
                  <Package2 className="w-5 h-5" />
                  <span>Review It Admin</span>
                </Link>
              </div>
              <nav className="p-4 space-y-2">
                {navItems.map((item) => {
                  const active = isActive(item.path);
                  const hasSubItems = item.subItems && item.subItems.length > 0;

                  return (
                    <div key={item.path}>
                      {hasSubItems ? (
                        <div>
                          <Button
                            variant="ghost"
                            className={cn(
                              "w-full justify-start relative",
                              active &&
                              "font-medium text-primary bg-primary/10 hover:bg-primary/15"
                            )}
                            onClick={() => setIsDocsExpanded(!isDocsExpanded)}
                          >
                            {React.cloneElement(item.icon, {
                              className: cn("w-5 h-5 mr-3", active && "text-primary"),
                            })}
                            {item.label}
                            {isDocsExpanded ? (
                              <ChevronDown className="w-4 h-4 ml-auto" />
                            ) : (
                              <ChevronRight className="w-4 h-4 ml-auto" />
                            )}
                            {active && (
                              <span className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-md" />
                            )}
                          </Button>
                          {isDocsExpanded && (
                            <div className="ml-6 mt-1 space-y-1">
                              {item.subItems.map((subItem) => {
                                const subActive = isActive(subItem.path);
                                return (
                                  <Button
                                    key={subItem.path}
                                    variant="ghost"
                                    size="sm"
                                    className={cn(
                                      "w-full justify-start relative text-sm",
                                      subActive &&
                                      "font-medium text-primary bg-primary/10 hover:bg-primary/15"
                                    )}
                                    onClick={() => setIsMobileMenuOpen(false)}
                                    asChild
                                  >
                                    <Link href={subItem.path}>
                                      {subItem.label}
                                      {subActive && (
                                        <span className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-md" />
                                      )}
                                    </Link>
                                  </Button>
                                );
                              })}
                            </div>
                          )}
                        </div>
                      ) : (
                        <Button
                          variant="ghost"
                          className={cn(
                            "w-full justify-start relative",
                            active &&
                            "font-medium text-primary bg-primary/10 hover:bg-primary/15"
                          )}
                          onClick={() => setIsMobileMenuOpen(false)}
                          asChild
                        >
                          <Link href={item.path}>
                            {React.cloneElement(item.icon, {
                              className: cn(
                                "w-5 h-5 mr-3",
                                active && "text-primary"
                              ),
                            })}
                            {item.label}
                            {active && (
                              <span className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-md" />
                            )}
                          </Link>
                        </Button>
                      )}
                    </div>
                  );
                })}
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </header>

      <div className="flex flex-1">
        {/* Sidebar */}
        <aside className="hidden w-64 p-4 border-r md:block bg-gray-50">
          <nav className="space-y-2">
            {navItems.map((item) => {
              const active = isActive(item.path);
              const isDocsItem = item.path === "/admin/docs";
              const hasSubItems = item.subItems && item.subItems.length > 0;

              return (
                <div key={item.path}>
                  {hasSubItems ? (
                    <div>
                      <Button
                        variant="ghost"
                        className={cn(
                          "w-full justify-start relative",
                          active &&
                          "font-medium text-primary bg-primary/10 hover:bg-primary/15"
                        )}
                        onClick={() => setIsDocsExpanded(!isDocsExpanded)}
                      >
                        {React.cloneElement(item.icon, {
                          className: cn("w-5 h-5 mr-3", active && "text-primary"),
                        })}
                        {item.label}
                        {isDocsExpanded ? (
                          <ChevronDown className="w-4 h-4 ml-auto" />
                        ) : (
                          <ChevronRight className="w-4 h-4 ml-auto" />
                        )}
                        {active && (
                          <span className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-md" />
                        )}
                      </Button>
                      {isDocsExpanded && (
                        <div className="ml-6 mt-1 space-y-1">
                          {item.subItems.map((subItem) => {
                            const subActive = isActive(subItem.path);
                            return (
                              <Button
                                key={subItem.path}
                                variant="ghost"
                                size="sm"
                                className={cn(
                                  "w-full justify-start relative text-sm",
                                  subActive &&
                                  "font-medium text-primary bg-primary/10 hover:bg-primary/15"
                                )}
                                asChild
                              >
                                <Link href={subItem.path}>
                                  {subItem.label}
                                  {subActive && (
                                    <span className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-md" />
                                  )}
                                </Link>
                              </Button>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Button
                      variant="ghost"
                      className={cn(
                        "w-full justify-start relative",
                        active &&
                        "font-medium text-primary bg-primary/10 hover:bg-primary/15"
                      )}
                      asChild
                    >
                      <Link href={item.path}>
                        {React.cloneElement(item.icon, {
                          className: cn("w-5 h-5 mr-3", active && "text-primary"),
                        })}
                        {item.label}
                        {active && (
                          <span className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-md" />
                        )}
                      </Link>
                    </Button>
                  )}
                </div>
              );
            })}
          </nav>
        </aside>

        {/* Main content */}
        <main className="flex-1 md:p-8 pb-6">
          <div className="flex flex-col gap-6">
            {/* Page header */}
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="p-2">
                <h1 className="text-2xl font-bold">{getPageTitle()}</h1>
                <p className="text-muted-foreground">
                  {getPageTitle() === "Dashboard" &&
                    "Overview of your admin panel"}
                  {getPageTitle() === "Reviews" && "Manage review submissions"}
                  {getPageTitle() === "Users" && "Manage user accounts"}
                  {getPageTitle() === "Products" &&
                    "Manage products and listings"}
                  {getPageTitle() === "Product Claims" &&
                    "Review and manage product ownership claims"}
                </p>
              </div>
            </div>

            {/* Page content */}
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
