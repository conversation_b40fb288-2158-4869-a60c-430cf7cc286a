import React from 'react';

const ProjectOverviewPage = () => {
    return (
        <div>
            <h1>Project Overview</h1>
            <p>This document provides a high-level overview of the Review-it project.</p>
            
            <h2>Project Goal</h2>
            <p>The main goal of the project is to create a platform where users can review products and services.</p>
            
            <h2>Key Features</h2>
            <ul>
                <li>User authentication and management</li>
                <li>Product and service listings</li>
                <li>Review submission and management</li>
                <li>Admin dashboard for managing the platform</li>
            </ul>

            <h2>Technology Stack</h2>
            <ul>
                <li>Next.js - React framework for server-rendered applications</li>
                <li>Prisma - ORM for database access</li>
                <li>PostgreSQL - Database</li>
                <li>Tailwind CSS - CSS framework</li>
                <li>Clerk - User authentication</li>
            </ul>

            <h2>Architecture</h2>
            <p>The project follows a monolithic architecture with a server-rendered frontend using Next.js. The backend is tightly coupled with the frontend, and the database is accessed through the Prisma ORM. The application is designed to be deployed as a single unit.</p>

            <h2>Development Workflow</h2>
            <p>The development workflow is based on Gitflow. Developers create feature branches from the `develop` branch. Once a feature is complete, a pull request is created to merge it into the `develop` branch. The `main` branch is used for production releases.</p>

            <h2>Security Considerations</h2>
            <p>Security is a top priority for the project. We use Clerk for user authentication, which provides a secure and reliable way to manage users. All sensitive data is encrypted at rest and in transit. We also follow best practices for web security, such as using HTTPS and protecting against common vulnerabilities like XSS and CSRF.</p>
        </div>
    );
};

export default ProjectOverviewPage;