import React from 'react';

const ProjectOverviewPage = () => {
    return (
        <div id="top" className="max-w-4xl mx-auto">
            <div className="text-gray-700 leading-relaxed">
                <div className="mb-8 text-sm text-gray-500">
                    <a href="/admin/docs" className="text-blue-600 hover:underline">Documentation</a> / <span>Project Overview</span>
                </div>

                <h1 className="text-4xl font-bold mb-6 text-gray-900">
                    Project Overview
                    <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">Complete</span>
                </h1>
                <p className="mb-4">This document provides a comprehensive overview of the Review-it project, including its goals, architecture, and development practices.</p>

                <h2 id="project-goal" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Project Goal</h2>
                <p className="mb-4">The main goal of the project is to create a comprehensive platform where users can review products and services, helping others make informed decisions while providing businesses with valuable feedback.</p>

                <h2 id="key-features" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Key Features</h2>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>User authentication and management</strong> - Secure user registration and profile management</li>
                    <li><strong>Product and service listings</strong> - Comprehensive database of reviewable items</li>
                    <li><strong>Review submission and management</strong> - User-friendly review creation and editing</li>
                    <li><strong>Admin dashboard</strong> - Complete platform management tools</li>
                    <li><strong>Business owner portal</strong> - Dedicated area for business management</li>
                    <li><strong>Analytics and reporting</strong> - Comprehensive insights and metrics</li>
                </ul>

                <h2 id="technology-stack" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Technology Stack</h2>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Next.js 13+</strong> - React framework with App Router for server-rendered applications</li>
                    <li><strong>TypeScript</strong> - Type-safe JavaScript development</li>
                    <li><strong>Prisma</strong> - Modern ORM for database access and management</li>
                    <li><strong>PostgreSQL</strong> - Robust relational database</li>
                    <li><strong>Tailwind CSS</strong> - Utility-first CSS framework</li>
                    <li><strong>Clerk</strong> - Complete user authentication and management</li>
                    <li><strong>Redis</strong> - Caching and session management</li>
                    <li><strong>Vercel</strong> - Deployment and hosting platform</li>
                </ul>

                <h2 id="architecture" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Architecture</h2>
                <p className="mb-4">The project follows a modern monolithic architecture with a server-rendered frontend using Next.js 13+ App Router. The backend is tightly integrated with the frontend, utilizing API routes for data operations. The database is accessed through the Prisma ORM, providing type-safe database operations and migrations.</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Key Architectural Decisions</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Server-Side Rendering (SSR)</strong> - Improved SEO and initial page load performance</li>
                    <li><strong>API Routes</strong> - RESTful endpoints for data operations</li>
                    <li><strong>Component-Based Architecture</strong> - Reusable UI components with shadcn/ui</li>
                    <li><strong>Type Safety</strong> - End-to-end TypeScript implementation</li>
                </ul>

                <h2 id="development-workflow" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Development Workflow</h2>
                <p className="mb-4">The development workflow follows Git best practices with feature branching. Developers create feature branches from the main development branch, implement changes, and create pull requests for code review before merging.</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Branch Strategy</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>main/prod</strong> - Production-ready code</li>
                    <li><strong>develop</strong> - Integration branch for features</li>
                    <li><strong>feature/*</strong> - Individual feature development</li>
                    <li><strong>hotfix/*</strong> - Critical production fixes</li>
                </ul>

                <h2 id="security-considerations" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Security Considerations</h2>
                <p className="mb-4">The application implements comprehensive security measures to protect user data and prevent unauthorized access:</p>

                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Authentication</strong> - Clerk-based secure user authentication</li>
                    <li><strong>Authorization</strong> - Role-based access control (RBAC)</li>
                    <li><strong>Input Validation</strong> - Server-side validation for all user inputs</li>
                    <li><strong>Data Protection</strong> - Encrypted data transmission and storage</li>
                    <li><strong>Security Headers</strong> - Proper HTTP security headers implementation</li>
                    <li><strong>Regular Audits</strong> - Ongoing security assessments and updates</li>
                </ul>
            </div>

            <a
                href="#top"
                className="fixed bottom-8 right-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
            >
                ↑
            </a>
        </div>
    );
};

export default ProjectOverviewPage;