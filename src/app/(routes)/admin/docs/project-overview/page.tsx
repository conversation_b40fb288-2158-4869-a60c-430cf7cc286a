import React from 'react';

const ProjectOverviewPage = () => {
    return (
        <>
            <style jsx>{`
                .docs-container {
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 2rem;
                }
                .docs-nav {
                    position: fixed;
                    left: 1rem;
                    top: 100px;
                    width: 200px;
                    background: white;
                    border-right: 1px solid #e5e7eb;
                    padding: 1rem;
                    height: calc(100vh - 100px);
                    overflow-y: auto;
                }
                .docs-content {
                    padding-left: 220px;
                }
                .prose {
                    color: #374151;
                    line-height: 1.75;
                }
                .prose h1 {
                    font-size: 2.5rem;
                    font-weight: bold;
                    margin-bottom: 1.5rem;
                    color: #1f2937;
                }
                .prose h2 {
                    font-size: 2rem;
                    font-weight: bold;
                    margin-top: 2.5rem;
                    margin-bottom: 1rem;
                    border-bottom: 1px solid #e5e7eb;
                    padding-bottom: 0.5rem;
                    color: #1f2937;
                }
                .prose h3 {
                    font-size: 1.75rem;
                    font-weight: bold;
                    margin-top: 2rem;
                    margin-bottom: 0.75rem;
                    color: #1f2937;
                }
                .prose p {
                    margin-bottom: 1rem;
                }
                .prose ul {
                    margin-bottom: 1rem;
                    padding-left: 1.5rem;
                }
                .prose li {
                    margin-bottom: 0.5rem;
                }
                .status-badge {
                    display: inline-block;
                    padding: 0.25rem 0.75rem;
                    border-radius: 9999px;
                    font-size: 0.875rem;
                    margin-left: 0.5rem;
                }
                .status-complete {
                    background-color: #dcfce7;
                    color: #166534;
                }
                .back-to-top {
                    position: fixed;
                    bottom: 2rem;
                    right: 2rem;
                    background: #3b82f6;
                    color: white;
                    padding: 0.75rem;
                    border-radius: 50%;
                    text-decoration: none;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                }
                .breadcrumb {
                    margin-bottom: 2rem;
                    color: #6b7280;
                    font-size: 0.875rem;
                }
                .breadcrumb a {
                    color: #3b82f6;
                    text-decoration: none;
                }
                .breadcrumb a:hover {
                    text-decoration: underline;
                }
                @media (max-width: 768px) {
                    .docs-nav {
                        display: none;
                    }
                    .docs-content {
                        padding-left: 0;
                    }
                    .docs-container {
                        padding: 1rem;
                    }
                }
            `}</style>

            <div className="docs-container">
                <nav className="docs-nav">
                    <h3 className="text-lg font-semibold mb-4 text-gray-900">Table of Contents</h3>
                    <ul className="space-y-2 text-sm">
                        <li><a href="#project-goal" className="text-blue-600 hover:text-blue-800 hover:underline">Project Goal</a></li>
                        <li><a href="#key-features" className="text-blue-600 hover:text-blue-800 hover:underline">Key Features</a></li>
                        <li><a href="#technology-stack" className="text-blue-600 hover:text-blue-800 hover:underline">Technology Stack</a></li>
                        <li><a href="#architecture" className="text-blue-600 hover:text-blue-800 hover:underline">Architecture</a></li>
                        <li><a href="#development-workflow" className="text-blue-600 hover:text-blue-800 hover:underline">Development Workflow</a></li>
                        <li><a href="#security-considerations" className="text-blue-600 hover:text-blue-800 hover:underline">Security Considerations</a></li>
                    </ul>
                </nav>

                <div className="docs-content prose">
                    <div className="breadcrumb">
                        <a href="/admin/docs">Documentation</a> / <span>Project Overview</span>
                    </div>

                    <h1>Project Overview <span className="status-badge status-complete">Complete</span></h1>
                    <p>This document provides a comprehensive overview of the Review-it project, including its goals, architecture, and development practices.</p>

                    <h2 id="project-goal">Project Goal</h2>
                    <p>The main goal of the project is to create a comprehensive platform where users can review products and services, helping others make informed decisions while providing businesses with valuable feedback.</p>

                    <h2 id="key-features">Key Features</h2>
                    <ul>
                        <li><strong>User authentication and management</strong> - Secure user registration and profile management</li>
                        <li><strong>Product and service listings</strong> - Comprehensive database of reviewable items</li>
                        <li><strong>Review submission and management</strong> - User-friendly review creation and editing</li>
                        <li><strong>Admin dashboard</strong> - Complete platform management tools</li>
                        <li><strong>Business owner portal</strong> - Dedicated area for business management</li>
                        <li><strong>Analytics and reporting</strong> - Comprehensive insights and metrics</li>
                    </ul>

                    <h2 id="technology-stack">Technology Stack</h2>
                    <ul>
                        <li><strong>Next.js 13+</strong> - React framework with App Router for server-rendered applications</li>
                        <li><strong>TypeScript</strong> - Type-safe JavaScript development</li>
                        <li><strong>Prisma</strong> - Modern ORM for database access and management</li>
                        <li><strong>PostgreSQL</strong> - Robust relational database</li>
                        <li><strong>Tailwind CSS</strong> - Utility-first CSS framework</li>
                        <li><strong>Clerk</strong> - Complete user authentication and management</li>
                        <li><strong>Redis</strong> - Caching and session management</li>
                        <li><strong>Vercel</strong> - Deployment and hosting platform</li>
                    </ul>

                    <h2 id="architecture">Architecture</h2>
                    <p>The project follows a modern monolithic architecture with a server-rendered frontend using Next.js 13+ App Router. The backend is tightly integrated with the frontend, utilizing API routes for data operations. The database is accessed through the Prisma ORM, providing type-safe database operations and migrations.</p>

                    <h3>Key Architectural Decisions</h3>
                    <ul>
                        <li><strong>Server-Side Rendering (SSR)</strong> - Improved SEO and initial page load performance</li>
                        <li><strong>API Routes</strong> - RESTful endpoints for data operations</li>
                        <li><strong>Component-Based Architecture</strong> - Reusable UI components with shadcn/ui</li>
                        <li><strong>Type Safety</strong> - End-to-end TypeScript implementation</li>
                    </ul>

                    <h2 id="development-workflow">Development Workflow</h2>
                    <p>The development workflow follows Git best practices with feature branching. Developers create feature branches from the main development branch, implement changes, and create pull requests for code review before merging.</p>

                    <h3>Branch Strategy</h3>
                    <ul>
                        <li><strong>main/prod</strong> - Production-ready code</li>
                        <li><strong>develop</strong> - Integration branch for features</li>
                        <li><strong>feature/*</strong> - Individual feature development</li>
                        <li><strong>hotfix/*</strong> - Critical production fixes</li>
                    </ul>

                    <h2 id="security-considerations">Security Considerations</h2>
                    <p>The application implements comprehensive security measures to protect user data and prevent unauthorized access:</p>

                    <ul>
                        <li><strong>Authentication</strong> - Clerk-based secure user authentication</li>
                        <li><strong>Authorization</strong> - Role-based access control (RBAC)</li>
                        <li><strong>Input Validation</strong> - Server-side validation for all user inputs</li>
                        <li><strong>Data Protection</strong> - Encrypted data transmission and storage</li>
                        <li><strong>Security Headers</strong> - Proper HTTP security headers implementation</li>
                        <li><strong>Regular Audits</strong> - Ongoing security assessments and updates</li>
                    </ul>
                </div>

                <a href="#" className="back-to-top" onClick={(e) => { e.preventDefault(); window.scrollTo({top: 0, behavior: 'smooth'}); }}>
                    ↑
                </a>
            </div>
        </>
    );
};

export default ProjectOverviewPage;