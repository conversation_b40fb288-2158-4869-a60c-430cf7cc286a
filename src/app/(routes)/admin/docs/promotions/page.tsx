import React from 'react';

const PromotionsPage = () => {
    return (
        <div>
            <h1>Promotions</h1>
            <p>This guide explains how to manage promotions in the admin dashboard.</p>

            <h2>Viewing Promotions</h2>
            <p>To see a list of all promotions, navigate to the &quot;Promotions&quot; tab in the admin dashboard. You will see a table with all promotions, along with their details such as the name, discount, and status.</p>

            <h2>Creating Promotions</h2>
            <p>To create a new promotion, click on the &quot;New Promotion&quot; button. A form will appear where you can enter the promotion&apos;s details, such as the name, description, and discount. Once you are done, click &quot;Create&quot; to add the promotion to the system.</p>

            <h2>Editing Promotions</h2>
            <p>To edit a promotion&apos;s details, click on the &quot;Edit&quot; button next to the promotion you want to modify. A form will appear with the promotion&apos;s current information. Make the necessary changes and click &quot;Save&quot; to update the promotion.</p>

            <h2>Deleting Promotions</h2>
            <p>To delete a promotion, click on the &quot;Delete&quot; button next to the promotion you want to remove. A confirmation dialog will appear. Click &quot;Confirm&quot; to permanently delete the promotion from the system.</p>

            <h2>Campaign Scheduling</h2>
            <p>The system allows you to schedule promotions to run at a specific time. You can set a start and end date for each promotion, and the promotion will automatically be activated and deactivated at the specified times.</p>

            <h2>Target Audience Selection</h2>
            <p>The system allows you to target promotions to specific groups of users. You can target promotions to users based on their location, purchase history, or other criteria.</p>

            <h2>Performance Tracking</h2>
            <p>The system tracks the performance of each promotion. You can view metrics such as the number of times a promotion has been used, the total discount amount, and the total revenue generated by the promotion.</p>

            <h2>Promotion Analytics</h2>
            <p>The promotion analytics section provides insights into the effectiveness of your promotions. You can view metrics such as the conversion rate, the average order value, and the return on investment for each promotion.</p>
        </div>
    );
};

export default PromotionsPage;