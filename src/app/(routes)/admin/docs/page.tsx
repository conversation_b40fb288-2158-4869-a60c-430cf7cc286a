import React from 'react';

const AdminDocsPage = () => {
    return (
        <>
            <style jsx>{`
                .docs-container {
                    max-width: 1000px;
                    margin: 0 auto;
                    padding: 2rem;
                }
                .prose {
                    color: #374151;
                    line-height: 1.75;
                }
                .prose h1 {
                    font-size: 2.5rem;
                    font-weight: bold;
                    margin-bottom: 1.5rem;
                    color: #1f2937;
                    text-align: center;
                }
                .prose h2 {
                    font-size: 2rem;
                    font-weight: bold;
                    margin-top: 2.5rem;
                    margin-bottom: 1rem;
                    border-bottom: 1px solid #e5e7eb;
                    padding-bottom: 0.5rem;
                    color: #1f2937;
                }
                .prose h3 {
                    font-size: 1.5rem;
                    font-weight: bold;
                    margin-top: 2rem;
                    margin-bottom: 0.75rem;
                    color: #1f2937;
                }
                .prose p {
                    margin-bottom: 1rem;
                    font-size: 1.1rem;
                }
                .docs-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 1.5rem;
                    margin-top: 2rem;
                }
                .docs-card {
                    background: white;
                    border: 1px solid #e5e7eb;
                    border-radius: 0.5rem;
                    padding: 1.5rem;
                    transition: all 0.2s ease;
                    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
                }
                .docs-card:hover {
                    border-color: #3b82f6;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    transform: translateY(-1px);
                }
                .docs-card h3 {
                    margin-top: 0;
                    margin-bottom: 0.5rem;
                    color: #1f2937;
                }
                .docs-card p {
                    margin-bottom: 1rem;
                    color: #6b7280;
                    font-size: 0.9rem;
                }
                .docs-card a {
                    color: #3b82f6;
                    text-decoration: none;
                    font-weight: 500;
                }
                .docs-card a:hover {
                    text-decoration: underline;
                }
                .status-badge {
                    display: inline-block;
                    padding: 0.25rem 0.75rem;
                    border-radius: 9999px;
                    font-size: 0.75rem;
                    margin-left: 0.5rem;
                    font-weight: 500;
                }
                .status-complete {
                    background-color: #dcfce7;
                    color: #166534;
                }
                .status-in-progress {
                    background-color: #fef3c7;
                    color: #92400e;
                }
                .status-placeholder {
                    background-color: #f3f4f6;
                    color: #6b7280;
                }
                .intro-section {
                    text-align: center;
                    margin-bottom: 3rem;
                    padding: 2rem;
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    border-radius: 0.75rem;
                }
                @media (max-width: 768px) {
                    .docs-container {
                        padding: 1rem;
                    }
                    .docs-grid {
                        grid-template-columns: 1fr;
                    }
                }
            `}</style>

            <div className="docs-container prose">
                <div className="intro-section">
                    <h1>Review-it Documentation</h1>
                    <p>Comprehensive documentation for the Review-it platform, covering all aspects from basic operations to advanced features and development practices.</p>
                </div>

                <h2>Admin Section Documentation</h2>
                <p>Core administrative features and platform management tools.</p>

                <div className="docs-grid">
                    <div className="docs-card">
                        <h3>Project Overview <span className="status-badge status-complete">Complete</span></h3>
                        <p>High-level overview of the Review-it project, including goals, architecture, technology stack, and development practices.</p>
                        <a href="/admin/docs/project-overview">View Documentation →</a>
                    </div>

                    <div className="docs-card">
                        <h3>User Management <span className="status-badge status-complete">Complete</span></h3>
                        <p>Comprehensive guide for managing users, including CRUD operations, analytics, role-based access control, and bulk operations.</p>
                        <a href="/admin/docs/user-management">View Documentation →</a>
                    </div>

                    <div className="docs-card">
                        <h3>Product Management <span className="status-badge status-complete">Complete</span></h3>
                        <p>Complete product management system covering listings, categories, approval workflows, analytics, and bulk operations.</p>
                        <a href="/admin/docs/product-management">View Documentation →</a>
                    </div>

                    <div className="docs-card">
                        <h3>Review Management <span className="status-badge status-complete">Complete</span></h3>
                        <p>Advanced review moderation system with analytics, flagged content management, response system, and verification processes.</p>
                        <a href="/admin/docs/review-management">View Documentation →</a>
                    </div>

                    <div className="docs-card">
                        <h3>Bug Reports <span className="status-badge status-placeholder">Placeholder</span></h3>
                        <p>Bug report submission, tracking, and resolution workflows for maintaining platform quality.</p>
                        <a href="/admin/docs/bug-reports">View Documentation →</a>
                    </div>

                    <div className="docs-card">
                        <h3>Promotions <span className="status-badge status-placeholder">Placeholder</span></h3>
                        <p>Promotion creation, campaign management, scheduling, and performance tracking tools.</p>
                        <a href="/admin/docs/promotions">View Documentation →</a>
                    </div>
                </div>

                <h2>Technical Documentation</h2>
                <p>API documentation, core concepts, and technical implementation details.</p>

                <div className="docs-grid">
                    <div className="docs-card">
                        <h3>API Routes <span className="status-badge status-placeholder">Placeholder</span></h3>
                        <p>Complete API documentation covering authentication, user, product, review, and business endpoints.</p>
                        <a href="/admin/docs/api-routes">View Documentation →</a>
                    </div>

                    <div className="docs-card">
                        <h3>Core Concepts <span className="status-badge status-placeholder">Needs Content</span></h3>
                        <p>Fundamental concepts including component library, database schema, project structure, caching, and deployment.</p>
                        <a href="/admin/docs/core-concepts">View Documentation →</a>
                    </div>
                </div>

                <h2>User-Facing Features</h2>
                <p>Documentation for public-facing features and user experience components.</p>

                <div className="docs-grid">
                    <div className="docs-card">
                        <h3>User-Facing Routes <span className="status-badge status-placeholder">Needs Content</span></h3>
                        <p>User profile management, browsing and search functionality, review submission, and home page features.</p>
                        <a href="/admin/docs/user-facing">View Documentation →</a>
                    </div>

                    <div className="docs-card">
                        <h3>Owner/Business Section <span className="status-badge status-placeholder">Placeholder</span></h3>
                        <p>Business dashboard, analytics, product management, promotions, and business settings for product owners.</p>
                        <a href="/admin/docs/owner-business">View Documentation →</a>
                    </div>
                </div>

                <h2>Getting Started</h2>
                <p>New to the platform? Start with these essential documents:</p>

                <div className="docs-grid">
                    <div className="docs-card">
                        <h3>🚀 Quick Start</h3>
                        <p>Begin with the Project Overview to understand the platform architecture and key features.</p>
                        <a href="/admin/docs/project-overview">Start Here →</a>
                    </div>

                    <div className="docs-card">
                        <h3>👥 User Management</h3>
                        <p>Learn how to manage users, roles, and permissions in the admin dashboard.</p>
                        <a href="/admin/docs/user-management">Learn More →</a>
                    </div>

                    <div className="docs-card">
                        <h3>📦 Product Management</h3>
                        <p>Understand how to manage products, categories, and approval workflows.</p>
                        <a href="/admin/docs/product-management">Explore →</a>
                    </div>
                </div>
            </div>
        </>
    );
};

export default AdminDocsPage;