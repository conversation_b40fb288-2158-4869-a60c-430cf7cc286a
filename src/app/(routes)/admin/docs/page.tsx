import React from 'react';

const AdminDocsPage = () => {
    return (
        <div className="max-w-4xl mx-auto p-8">
            <div className="text-center mb-12 p-8 bg-gradient-to-br from-slate-50 to-slate-200 rounded-xl">
                <h1 className="text-4xl font-bold mb-6 text-gray-900">Review-it Documentation</h1>
                <p className="text-lg text-gray-600">Comprehensive documentation for the Review-it platform, covering all aspects from basic operations to advanced features and development practices.</p>
            </div>

            <section className="mb-12">
                <h2 className="text-3xl font-bold mb-4 pb-2 border-b border-gray-200 text-gray-900">Admin Section Documentation</h2>
                <p className="text-lg mb-6 text-gray-600">Core administrative features and platform management tools.</p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">
                            Project Overview
                            <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Complete</span>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">High-level overview of the Review-it project, including goals, architecture, technology stack, and development practices.</p>
                        <a href="/admin/docs/project-overview" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">View Documentation →</a>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">
                            User Management
                            <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Complete</span>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">Comprehensive guide for managing users, including CRUD operations, analytics, role-based access control, and bulk operations.</p>
                        <a href="/admin/docs/user-management" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">View Documentation →</a>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">
                            Product Management
                            <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Complete</span>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">Complete product management system covering listings, categories, approval workflows, analytics, and bulk operations.</p>
                        <a href="/admin/docs/product-management" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">View Documentation →</a>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">
                            Review Management
                            <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Complete</span>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">Advanced review moderation system with analytics, flagged content management, response system, and verification processes.</p>
                        <a href="/admin/docs/review-management" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">View Documentation →</a>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">
                            Bug Reports
                            <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">Placeholder</span>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">Bug report submission, tracking, and resolution workflows for maintaining platform quality.</p>
                        <a href="/admin/docs/bug-reports" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">View Documentation →</a>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">
                            Promotions
                            <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">Placeholder</span>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">Promotion creation, campaign management, scheduling, and performance tracking tools.</p>
                        <a href="/admin/docs/promotions" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">View Documentation →</a>
                    </div>
                </div>
            </section>

            <section className="mb-12">
                <h2 className="text-3xl font-bold mb-4 pb-2 border-b border-gray-200 text-gray-900">Technical Documentation</h2>
                <p className="text-lg mb-6 text-gray-600">API documentation, core concepts, and technical implementation details.</p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">
                            API Routes
                            <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">Placeholder</span>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">Complete API documentation covering authentication, user, product, review, and business endpoints.</p>
                        <a href="/admin/docs/api-routes" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">View Documentation →</a>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">
                            Core Concepts
                            <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Needs Content</span>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">Fundamental concepts including component library, database schema, project structure, caching, and deployment.</p>
                        <a href="/admin/docs/core-concepts" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">View Documentation →</a>
                    </div>
                </div>
            </section>

            <section className="mb-12">
                <h2 className="text-3xl font-bold mb-4 pb-2 border-b border-gray-200 text-gray-900">User-Facing Features</h2>
                <p className="text-lg mb-6 text-gray-600">Documentation for public-facing features and user experience components.</p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">
                            User-Facing Routes
                            <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Needs Content</span>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">User profile management, browsing and search functionality, review submission, and home page features.</p>
                        <a href="/admin/docs/user-facing" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">View Documentation →</a>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">
                            Owner/Business Section
                            <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">Placeholder</span>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">Business dashboard, analytics, product management, promotions, and business settings for product owners.</p>
                        <a href="/admin/docs/owner-business" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">View Documentation →</a>
                    </div>
                </div>
            </section>

            <section className="mb-12">
                <h2 className="text-3xl font-bold mb-4 pb-2 border-b border-gray-200 text-gray-900">Getting Started</h2>
                <p className="text-lg mb-6 text-gray-600">New to the platform? Start with these essential documents:</p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">🚀 Quick Start</h3>
                        <p className="text-gray-600 text-sm mb-4">Begin with the Project Overview to understand the platform architecture and key features.</p>
                        <a href="/admin/docs/project-overview" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">Start Here →</a>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">👥 User Management</h3>
                        <p className="text-gray-600 text-sm mb-4">Learn how to manage users, roles, and permissions in the admin dashboard.</p>
                        <a href="/admin/docs/user-management" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">Learn More →</a>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">📦 Product Management</h3>
                        <p className="text-gray-600 text-sm mb-4">Understand how to manage products, categories, and approval workflows.</p>
                        <a href="/admin/docs/product-management" className="text-blue-600 hover:text-blue-800 font-medium hover:underline">Explore →</a>
                    </div>
                </div>
            </section>
        </div>
    );
};

export default AdminDocsPage;