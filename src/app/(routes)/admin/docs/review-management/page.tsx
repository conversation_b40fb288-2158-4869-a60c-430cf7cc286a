import React from 'react';

const ReviewManagementPage = () => {
    return (
        <div className="max-w-6xl mx-auto p-8">
            <nav className="fixed left-4 top-24 w-48 bg-white border-r border-gray-200 p-4 h-[calc(100vh-6rem)] overflow-y-auto hidden lg:block">
                <h3 className="text-lg font-semibold mb-4 text-gray-900">Table of Contents</h3>
                <ul className="space-y-2 text-sm">
                    <li><a href="#viewing-reviews" className="text-blue-600 hover:text-blue-800 hover:underline">Viewing Reviews</a></li>
                    <li><a href="#moderation-actions" className="text-blue-600 hover:text-blue-800 hover:underline">Moderation Actions</a></li>
                    <li><a href="#moderation-interface" className="text-blue-600 hover:text-blue-800 hover:underline">Moderation Interface</a></li>
                    <li><a href="#review-analytics" className="text-blue-600 hover:text-blue-800 hover:underline">Review Analytics</a></li>
                    <li><a href="#flagged-content" className="text-blue-600 hover:text-blue-800 hover:underline">Flagged Content</a></li>
                    <li><a href="#response-system" className="text-blue-600 hover:text-blue-800 hover:underline">Response System</a></li>
                    <li><a href="#verification-processes" className="text-blue-600 hover:text-blue-800 hover:underline">Verification Processes</a></li>
                </ul>
            </nav>

            <div className="lg:pl-56 text-gray-700 leading-relaxed">
                <div className="mb-8 text-sm text-gray-500">
                    <a href="/admin/docs" className="text-blue-600 hover:underline">Documentation</a> / <span>Review Management</span>
                </div>

                <h1 className="text-4xl font-bold mb-6 text-gray-900">
                    Review Management
                    <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">Complete</span>
                </h1>
                <h2 id="viewing-reviews" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Viewing Reviews</h2>
                <p className="mb-4">To see a list of all reviews, navigate to the "Review Management" tab in the admin dashboard. The review table displays comprehensive information about each review:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Review Content</strong> - Title and text content of the review</li>
                    <li><strong>Rating</strong> - Star rating given by the reviewer</li>
                    <li><strong>Product</strong> - Associated product or service being reviewed</li>
                    <li><strong>Reviewer</strong> - User who submitted the review</li>
                    <li><strong>Status</strong> - Published, pending, flagged, or rejected</li>
                    <li><strong>Submission Date</strong> - When the review was originally submitted</li>
                    <li><strong>Last Modified</strong> - Most recent update or moderation action</li>
                    <li><strong>Engagement</strong> - Helpful votes and user interactions</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Review Display Options</h3>
                <ul className="mb-4 pl-6 space-y-2">
                        <li><strong>List View</strong> - Compact table format for quick scanning</li>
                        <li><strong>Card View</strong> - Detailed cards showing full review content</li>
                        <li><strong>Timeline View</strong> - Chronological display of review submissions</li>
                        <li><strong>Custom Views</strong> - Personalized layouts for different workflows</li>
                    </ul>

                    <h2 id="moderation-actions">Moderation Actions</h2>
                    <p>The review management system provides several moderation actions to maintain content quality and platform standards:</p>

                    <h3>Approval and Rejection</h3>
                    <ul>
                        <li><strong>Approve Review</strong> - Make the review visible to all users</li>
                        <li><strong>Reject Review</strong> - Remove from public view with reason</li>
                        <li><strong>Conditional Approval</strong> - Approve with minor edits or warnings</li>
                        <li><strong>Request Revision</strong> - Ask reviewer to modify content before approval</li>
                    </ul>

                    <h3>Content Management</h3>
                    <ul>
                        <li><strong>Edit Review</strong> - Make administrative edits to review content</li>
                        <li><strong>Add Moderator Note</strong> - Internal notes for other administrators</li>
                        <li><strong>Flag for Review</strong> - Mark for additional scrutiny</li>
                        <li><strong>Archive Review</strong> - Remove from active display while preserving data</li>
                    </ul>

                    <h3>Deletion and Removal</h3>
                    <ul>
                        <li><strong>Soft Delete</strong> - Hide review but maintain in database</li>
                        <li><strong>Hard Delete</strong> - Permanently remove review and all associated data</li>
                        <li><strong>Bulk Deletion</strong> - Remove multiple reviews simultaneously</li>
                        <li><strong>Automated Removal</strong> - System-triggered removal based on rules</li>
                    </ul>

                    <h2 id="moderation-interface">Review Moderation Interface</h2>
                    <p>The review moderation interface provides a centralized location for managing all reviews with powerful tools and workflows:</p>

                    <h3>Moderation Queue</h3>
                    <ul>
                        <li><strong>Pending Reviews</strong> - New submissions awaiting approval</li>
                        <li><strong>Flagged Reviews</strong> - Content reported by users or system</li>
                        <li><strong>Priority Queue</strong> - High-priority reviews requiring immediate attention</li>
                        <li><strong>Escalated Reviews</strong> - Complex cases requiring senior moderator review</li>
                    </ul>

                    <h3>Filtering and Search</h3>
                    <ul>
                        <li><strong>Status Filters</strong> - Filter by approval status and moderation state</li>
                        <li><strong>Content Filters</strong> - Search by keywords, rating, or content type</li>
                        <li><strong>User Filters</strong> - Filter by reviewer or product owner</li>
                        <li><strong>Date Filters</strong> - Filter by submission or moderation dates</li>
                        <li><strong>Advanced Search</strong> - Combine multiple criteria for precise results</li>
                    </ul>

                    <h3>Workflow Tools</h3>
                    <ul>
                        <li><strong>Batch Actions</strong> - Perform actions on multiple reviews at once</li>
                        <li><strong>Quick Actions</strong> - One-click approval or rejection</li>
                        <li><strong>Assignment System</strong> - Assign reviews to specific moderators</li>
                        <li><strong>Escalation Rules</strong> - Automatic escalation for complex cases</li>
                    </ul>

                    <h2 id="review-analytics">Review Analytics</h2>
                    <p>The review analytics section provides comprehensive insights into review trends, user feedback patterns, and content quality metrics:</p>

                    <h3>Volume and Trends</h3>
                    <ul>
                        <li><strong>Review Volume</strong> - Daily, weekly, and monthly submission counts</li>
                        <li><strong>Approval Rates</strong> - Percentage of reviews approved vs. rejected</li>
                        <li><strong>Response Times</strong> - Average time from submission to moderation</li>
                        <li><strong>Seasonal Patterns</strong> - Review submission trends over time</li>
                    </ul>

                    <h3>Quality Metrics</h3>
                    <ul>
                        <li><strong>Rating Distribution</strong> - Breakdown of star ratings across reviews</li>
                        <li><strong>Content Quality</strong> - Length, detail, and helpfulness scores</li>
                        <li><strong>Authenticity Indicators</strong> - Metrics suggesting genuine vs. fake reviews</li>
                        <li><strong>User Engagement</strong> - Helpful votes and user interactions</li>
                    </ul>

                    <h3>Moderation Analytics</h3>
                    <ul>
                        <li><strong>Moderator Performance</strong> - Individual moderator statistics and efficiency</li>
                        <li><strong>Decision Consistency</strong> - Agreement rates between different moderators</li>
                        <li><strong>Appeal Success Rates</strong> - Outcomes of moderation appeals</li>
                        <li><strong>Policy Compliance</strong> - Adherence to content guidelines and standards</li>
                    </ul>

                    <h2 id="flagged-content">Flagged Content Management</h2>
                    <p>The system provides comprehensive tools for managing user-reported content and maintaining community standards:</p>

                    <h3>Flagging System</h3>
                    <ul>
                        <li><strong>User Reports</strong> - Community-driven content flagging</li>
                        <li><strong>Automated Detection</strong> - System-based content analysis and flagging</li>
                        <li><strong>Flag Categories</strong> - Specific reasons for flagging (spam, inappropriate, etc.)</li>
                        <li><strong>Priority Levels</strong> - Urgent, high, medium, and low priority flags</li>
                    </ul>

                    <h3>Investigation Tools</h3>
                    <ul>
                        <li><strong>Flag Details</strong> - Complete information about why content was flagged</li>
                        <li><strong>Reporter Information</strong> - Details about users who reported content</li>
                        <li><strong>Content History</strong> - Previous versions and edit history</li>
                        <li><strong>Related Content</strong> - Other reviews from the same user or product</li>
                    </ul>

                    <h3>Resolution Actions</h3>
                    <ul>
                        <li><strong>Dismiss Flag</strong> - Mark flag as invalid or resolved</li>
                        <li><strong>Uphold Flag</strong> - Confirm flag and take appropriate action</li>
                        <li><strong>Partial Action</strong> - Address specific concerns while keeping content</li>
                        <li><strong>Escalate Case</strong> - Forward to senior moderators for complex decisions</li>
                    </ul>

                    <h2 id="response-system">Review Response System</h2>
                    <p>The review response system enables administrators and business owners to engage with reviewers and provide additional context:</p>

                    <h3>Response Types</h3>
                    <ul>
                        <li><strong>Business Responses</strong> - Official replies from product/service owners</li>
                        <li><strong>Administrative Responses</strong> - Platform moderator clarifications</li>
                        <li><strong>Community Responses</strong> - Verified user contributions and corrections</li>
                        <li><strong>Automated Responses</strong> - System-generated acknowledgments and updates</li>
                    </ul>

                    <h3>Response Management</h3>
                    <ul>
                        <li><strong>Response Templates</strong> - Pre-written responses for common situations</li>
                        <li><strong>Approval Workflow</strong> - Review and approve responses before publication</li>
                        <li><strong>Response Guidelines</strong> - Standards for professional and helpful responses</li>
                        <li><strong>Response Analytics</strong> - Track effectiveness and user engagement</li>
                    </ul>

                    <h3>Communication Features</h3>
                    <ul>
                        <li><strong>Direct Messaging</strong> - Private communication between parties</li>
                        <li><strong>Public Dialogue</strong> - Visible conversation threads on reviews</li>
                        <li><strong>Notification System</strong> - Alerts for new responses and updates</li>
                        <li><strong>Resolution Tracking</strong> - Monitor issue resolution and follow-up</li>
                    </ul>

                    <h2 id="verification-processes">Review Verification Processes</h2>
                    <p>The system includes comprehensive verification processes to ensure review authenticity and maintain platform integrity:</p>

                    <h3>Authenticity Verification</h3>
                    <ul>
                        <li><strong>User Verification</strong> - Confirm reviewer identity and legitimacy</li>
                        <li><strong>Purchase Verification</strong> - Verify actual product/service usage</li>
                        <li><strong>Experience Validation</strong> - Confirm genuine user experience</li>
                        <li><strong>Duplicate Detection</strong> - Identify and prevent duplicate reviews</li>
                    </ul>

                    <h3>Quality Checks</h3>
                    <ul>
                        <li><strong>Content Analysis</strong> - Automated quality and relevance scoring</li>
                        <li><strong>Language Detection</strong> - Identify inappropriate or spam content</li>
                        <li><strong>Sentiment Analysis</strong> - Analyze review tone and authenticity</li>
                        <li><strong>Pattern Recognition</strong> - Detect suspicious review patterns</li>
                    </ul>

                    <h3>Verification Rules</h3>
                    <ul>
                        <li><strong>Custom Rules</strong> - Create specific verification criteria</li>
                        <li><strong>Automated Verification</strong> - System-based verification for qualifying reviews</li>
                        <li><strong>Manual Review</strong> - Human verification for complex cases</li>
                        <li><strong>Verification Badges</strong> - Visual indicators of verified reviews</li>
                    </ul>

                    <h3>Fraud Prevention</h3>
                    <ul>
                        <li><strong>Fake Review Detection</strong> - Identify artificially generated reviews</li>
                        <li><strong>Manipulation Prevention</strong> - Prevent review farming and gaming</li>
                        <li><strong>IP Tracking</strong> - Monitor for suspicious activity patterns</li>
                        <li><strong>Account Monitoring</strong> - Track user behavior for authenticity</li>
                    </ul>
                </div>

            <a
                href="#"
                className="fixed bottom-8 right-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
                onClick={(e) => { e.preventDefault(); window.scrollTo({top: 0, behavior: 'smooth'}); }}
            >
                ↑
            </a>
        </div>
    );
};

export default ReviewManagementPage;