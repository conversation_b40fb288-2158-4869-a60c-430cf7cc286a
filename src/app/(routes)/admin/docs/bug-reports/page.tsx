import React from 'react';

const BugReportsPage = () => {
    return (
        <div>
            <h1>Bug Reports</h1>
            <p>This guide explains how to manage bug reports in the admin dashboard.</p>

            <h2>Viewing Bug Reports</h2>
            <p>To see a list of all bug reports, navigate to the &quot;Bug Reports&quot; tab in the admin dashboard. You will see a table with all bug reports, along with their details such as the title, description, and status.</p>

            <h2>Updating Bug Report Status</h2>
            <p>You can update the status of a bug report to reflect its current state (e.g., &quot;Open&quot;, &quot;In Progress&quot;, &quot;Resolved&quot;). Click on the status dropdown next to the bug report and select the new status.</p>

            <h2>Deleting Bug Reports</h2>
            <p>To delete a bug report, click on the &quot;Delete&quot; button next to the report you want to remove. A confirmation dialog will appear. Click &quot;Confirm&quot; to permanently delete the bug report from the system.</p>

            <h2>Bug Report Submission Process</h2>
            <p>Users can submit bug reports through the &quot;Bug Report&quot; form. The form includes fields for the bug title, description, and steps to reproduce. Once a bug report is submitted, it is added to the bug report queue for review.</p>

            <h2>Status Tracking and Management</h2>
            <p>The bug report dashboard allows you to track the status of all bug reports. You can view a list of all bug reports, filter them by status, and update the status of each bug report as it is being worked on.</p>

            <h2>Priority Assignment</h2>
            <p>The system allows you to assign a priority to each bug report. You can set the priority to &quot;High&quot;, &quot;Medium&quot;, or &quot;Low&quot; to indicate the urgency of the bug report.</p>

            <h2>Resolution Workflows</h2>
            <p>The system includes a resolution workflow that allows you to track the progress of each bug report. You can update the status of a bug report to &quot;In Progress&quot;, &quot;Resolved&quot;, or &quot;Closed&quot; to indicate its current state.</p>

            <h2>Bug Analytics and Reporting</h2>
            <p>The bug analytics section provides insights into bug trends and resolution times. You can view metrics such as the number of open bugs, the number of resolved bugs, and the average time to resolution.</p>
        </div>
    );
};

export default BugReportsPage;