"use client";

import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { iProduct, iReview } from "@/app/util/Interfaces";
import Image from "next/legacy/image";
import SearchBoxAndListener from "@/app/components/SearchBoxAndListener";
import Link from "next/link";
import PaginatedProductCarousel from "../../components/PaginatedProductCarousel";
import Quote from "@/app/components/Quote";
import { useQuery } from "@tanstack/react-query";
import { getLatestReviews } from "@/app/util/serverFunctions";
import { TopReviewsSkeleton } from "@/app/components/skeletons";

const WriteReviewPage = () => {
  const [products] = useAtom(allProductsStore);
  const [filteredProducts, setFilteredProducts] = useState<iProduct[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const router = useRouter();

  // Fetch recent reviews
  const { data: recentReviews } = useQuery({
    queryKey: ["recentReviews"],
    queryFn: async () => {
      const response = await getLatestReviews("latest");
      if (!response.success) {
        throw new Error(response.error || "Failed to fetch recent reviews");
      }
      return response.data || [];
    },
  });

  useEffect(() => {
    if (products) {
      setFilteredProducts(products);
    }
  }, [products]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (products) {
      const results = products.filter(product =>
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.description.toLowerCase().includes(query.toLowerCase()) ||
        (product.tags && product.tags.some(tag =>
          tag.toLowerCase().includes(query.toLowerCase())
        ))
      );
      setFilteredProducts(results);
    }
  };

  const handleSelectProduct = (productId: string) => {
    router.push(`/cr?id=${productId}`);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Write a Review
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Share your experience, help others make the right choice
        </p>
        
        {/* Recent Reviews Section */}
        {recentReviews === undefined ? (
          <div className="mb-12 hidden md:block">
            <TopReviewsSkeleton />
          </div>
        ) : recentReviews && recentReviews.length > 0 && (
          <div className="mb-12 hidden md:block">
            <h2 className="text-2xl font-semibold mb-6 text-gray-800">What others are saying</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
              {recentReviews.slice(0, 3).map((review: iReview) => (
               <Quote
                 key={review.id}
                 userName={review.user?.userName || "Anonymous"}
                 userImage={review.user?.avatar || undefined}
                 quoteText={review.body}
               />
             ))}
            </div>
            <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
              <p className="text-lg font-medium text-gray-700 mb-2">Your turn to share!</p>
              <p className="text-gray-600">Join thousands of satisfied customers and help others make informed decisions.</p>
            </div>
          </div>
        )}
      </div>

      {/* Search Section */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-4 text-center">Find a product to review</h3>
        <SearchBoxAndListener onSearch={handleSearch} />
      </div>

      {/* Products Section */}
      {filteredProducts && filteredProducts.length > 0 ? (
        <PaginatedProductCarousel
          allProducts={filteredProducts}
          miniCardOptions={{
            size: "md",
            showWriteReview: true,
          }}
          itemsPerPage={3}
          title={searchQuery ? `Results for "${searchQuery}"` : "Browse Products"}
        />
      ) : (
        searchQuery && (
          <div className="text-center py-8">
            <p className="text-gray-600">No products found matching &#34;{searchQuery}&#34;</p>
          </div>
        )
      )}
      
      {/* Call to Action */}
      <div className="mt-12 bg-gradient-to-r from-gray-50 to-blue-50 p-8 rounded-xl shadow-sm text-center border border-gray-200">
        <h2 className="text-2xl font-bold mb-3 text-gray-800">Can&apos;t find a company or product?</h2>
        <p className="text-gray-600 mb-4">
          It might not be listed on Reviewit yet.
        </p>
        <Link 
          href="/submit" 
          className="inline-block bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg"
        >
          Add it and be the first to write a review
        </Link>
      </div>
    </div>
  );
};

export default WriteReviewPage;
