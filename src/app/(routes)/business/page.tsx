import React from "react";
import { <PERSON>ada<PERSON> } from "next";
import <PERSON> from "next/link";
import {
  Building2,
  ArrowRight,
  Star,
  TrendingUp,
  Users,
  MessageSquare,
  Shield,
  BarChart3,
  CheckCircle,
  Zap,
  Target,
  Award,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export const metadata: Metadata = {
  title: "Business Solutions | ReviewIt Guyana",
  description:
    "Grow your business with ReviewIt. Manage reviews, engage customers, and build trust with authentic feedback from real Guyanese customers.",
  keywords:
    "business reviews, Guyana business, customer feedback, business growth",
};

export default function BusinessPage() {
  const features = [
    {
      icon: <Star className="w-6 h-6" />,
      title: "Review Management",
      description: "Monitor and respond to customer reviews in real-time",
      color: "bg-yellow-500",
    },
    {
      icon: <BarChart3 className="w-6 h-6" />,
      title: "Analytics Dashboard",
      description: "Track performance metrics and customer sentiment",
      color: "bg-blue-500",
    },
    {
      icon: <MessageSquare className="w-6 h-6" />,
      title: "Customer Engagement",
      description: "Build relationships through direct communication",
      color: "bg-green-500",
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Trust Building",
      description: "Showcase verified reviews and business credentials",
      color: "bg-purple-500",
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Growth Insights",
      description: "Understand market trends and customer preferences",
      color: "bg-orange-500",
    },
    {
      icon: <Award className="w-6 h-6" />,
      title: "Recognition Program",
      description: "Earn badges and recognition for excellent service",
      color: "bg-pink-500",
    },
  ];

  const benefits = [
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Increase customer trust and credibility",
    },
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Improve online visibility and search rankings",
    },
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Get valuable customer feedback and insights",
    },
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Respond to reviews and build relationships",
    },
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Track performance with detailed analytics",
    },
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Stand out from competitors with verified reviews",
    },
  ];

  const testimonials = [
    {
      name: "Sarah Chen",
      business: "Chen&apos;s Restaurant",
      content:
        "ReviewIt helped us understand our customers better and improve our service quality.",
      rating: 5,
    },
    {
      name: "Michael Singh",
      business: "Singh Electronics",
      content:
        "The analytics dashboard gives us insights we never had before. Highly recommended!",
      rating: 5,
    },
    {
      name: "Jennifer Williams",
      business: "Bella Beauty Salon",
      content:
        "Our customer engagement improved significantly since joining ReviewIt.",
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-myTheme-primary/90 to-myTheme-primary">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,transparent,rgba(255,255,255,0.5),transparent)] pointer-events-none" />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <div className="flex items-center justify-center gap-3 mb-6">
              <Building2 className="w-12 h-12 text-white/90" />
              <h1 className="text-4xl md:text-6xl font-bold text-white">
                Grow Your Business
              </h1>
            </div>

            <p className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto mb-8 leading-relaxed">
              Take control of your online presence with ReviewIt. Get detailed
              insights, respond to reviews, and build trust with authentic
              customer feedback.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Link href="/claim-product">
                <Button
                  size="lg"
                  className="bg-white text-myTheme-primary hover:bg-white/90 font-semibold px-8 py-4 text-lg rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                >
                  <Zap className="w-5 h-5 mr-2" />
                  Get Started Now
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>

              <Link href="/mybusinesses">
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white/30 text-white hover:bg-white/10 font-semibold px-8 py-4 text-lg rounded-full"
                >
                  <Users className="w-5 h-5 mr-2" />
                  View My Businesses
                </Button>
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-1">500+</div>
                <div className="text-white/80 text-sm">Active Businesses</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-1">10K+</div>
                <div className="text-white/80 text-sm">Customer Reviews</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-1">95%</div>
                <div className="text-white/80 text-sm">Satisfaction Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-1">24/7</div>
                <div className="text-white/80 text-sm">Support Available</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Succeed
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive tools designed specifically for Guyanese businesses
              to thrive in the digital age
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-all duration-300 border-0 shadow-sm hover:-translate-y-1"
              >
                <CardHeader>
                  <div
                    className={`w-12 h-12 ${feature.color} rounded-lg flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-200`}
                  >
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl font-semibold text-gray-900 group-hover:text-myTheme-primary transition-colors">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Why Choose ReviewIt for Your Business?
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Join hundreds of successful Guyanese businesses that trust
                ReviewIt to manage their online reputation and grow their
                customer base.
              </p>

              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    {benefit.icon}
                    <span className="text-gray-700 font-medium">
                      {benefit.text}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              <div className="bg-white rounded-2xl shadow-xl p-8 transform rotate-2 hover:rotate-0 transition-transform duration-300">
                <div className="flex items-center gap-3 mb-6">
                  <Target className="w-8 h-8 text-myTheme-primary" />
                  <h3 className="text-2xl font-bold text-gray-900">
                    Success Story
                  </h3>
                </div>
                <blockquote className="text-gray-700 italic mb-4">
                  &ldquo;Since joining ReviewIt, our customer engagement
                  increased by 200% and we gained valuable insights that helped
                  us improve our services significantly.&rdquo;
                </blockquote>
                <div className="flex items-center gap-2">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-current" />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    - Local Business Owner
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Business Owners Say
            </h2>
            <p className="text-xl text-gray-600">
              Real feedback from real businesses across Guyana
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex text-yellow-400 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-current" />
                    ))}
                  </div>
                  <blockquote className="text-gray-700 mb-4 italic">
                    &ldquo;{testimonial.content}&rdquo;
                  </blockquote>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-gray-600">
                      {testimonial.business}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-myTheme-primary to-myTheme-secondary px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Transform Your Business?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Join the growing community of successful businesses on ReviewIt.
            Start building trust and growing your customer base today.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/claim-product">
              <Button
                size="lg"
                className="bg-white text-myTheme-primary hover:bg-white/90 font-semibold px-8 py-4 text-lg rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              >
                <Building2 className="w-5 h-5 mr-2" />
                Claim Your Business
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>

            <Link href="/about">
              <Button
                variant="outline"
                size="lg"
                className="border-white/30 text-white hover:bg-white/10 font-semibold px-8 py-4 text-lg rounded-full"
              >
                Learn More
              </Button>
            </Link>
          </div>

          <div className="mt-8 text-white/80 text-sm">
            🚀 Get started in less than 5 minutes • No setup fees • Cancel
            anytime
          </div>
        </div>
      </section>
    </div>
  );
}
