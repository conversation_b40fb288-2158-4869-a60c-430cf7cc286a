'use client'
import React from 'react'
import ExpandedReview from '@/app/components/ExpandedReview'
import GrandProductCard from '@/app/components/GrandProductCard'
import { iProduct } from '@/app/util/Interfaces'

interface FullReviewPageProps {
    searchParams: {
        id: string
        productid: string
        cid?: string
    }
    productData: iProduct | null
}

export default function FullReviewPage({ searchParams, productData }: FullReviewPageProps) {
    return (
        <div className="max-w-5xl mx-auto w-full">
            <GrandProductCard productId={searchParams.productid} productData={productData} />
            <ExpandedReview
                reviewId={searchParams.id}
                productId={searchParams.productid}
                cId={searchParams.cid || ""}
            />
        </div>
    )
} 