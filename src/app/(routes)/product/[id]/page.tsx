import React from 'react';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { getProductById } from '@/app/actions/productActions';
import SchemaMarkup from '@/components/product/SchemaMarkup';
import ProductPageClient from '@/components/product/ProductPageClient';
import { headers } from 'next/headers';
import { recordProductView } from '@/app/util/analyticsUtils';
import { Button } from '@/components/ui/button'; // Added missing import

interface ProductPageProps {
    params: {
        id: string;
    };
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
    const product = await getProductById(params.id);

    if (!product) {
        return {
            title: 'Product Not Found | Review It',
            description: 'The requested product could not be found.',
        };
    }

    return {
        title: `${product.name} | Review It`,
        description: product.description?.substring(0, 160) || `Read reviews for ${product.name} on Review It.`,
        openGraph: {
            title: product.name,
            description: product.description,
            images: product.display_image ? [product.display_image] : [],
        },
        twitter: {
            card: 'summary_large_image',
            title: product.name,
            description: product.description,
            images: product.display_image ? [product.display_image] : [],
        }
    };
}

export default async function ProductPage({ params }: ProductPageProps) {
    console.log(`[Product Page] Loading product page for ID: ${params.id}`);
    const product = await getProductById(params.id);

    if (!product) {
        console.log(`[Product Page] Product not found for ID: ${params.id}`);
        notFound();
    }

    // Get user agent and referrer for analytics
    const headersList = headers();
    const userAgent = headersList.get('user-agent');
    const referrer = headersList.get('referer');

    // Determine device type from user agent
    const deviceType = userAgent
        ? /mobile|android|iphone/i.test(userAgent)
            ? 'mobile'
            : /tablet|ipad/i.test(userAgent)
                ? 'tablet'
                : 'desktop'
        : undefined;

    // Record the view with enhanced tracking
    console.log(`[Product Page] Recording view for product ID: ${params.id}`);
    try {
        await recordProductView(
            params.id,
            undefined, // userId will be handled by the API
            referrer || undefined,
            deviceType
        );
        console.log(`[Product Page] Successfully recorded view`);
    } catch (error) {
        console.error(`[Product Page] Error recording view:`, error);
    }

    return (
        <>
            <SchemaMarkup product={product} />
            <ProductPageClient product={product} />
            {!product.hasOwner && (
                <div className="mt-8 p-4 bg-white rounded-lg shadow-sm border border-gray-100">
                    <h3 className="text-lg font-semibold mb-4">Own this business?</h3>
                    <Button
                        variant="default"
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                        onClick={() => window.open(`/claim-product/${product.id}`, '_blank')}
                    >
                        Claim This Product
                    </Button>
                    <p className="text-sm text-gray-500 mt-2">
                        Verify ownership to manage this listing and respond to reviews
                    </p>
                </div>
            )}
        </>
    );
}
