"use client";
import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { useSearchParams, useRouter } from "next/navigation";
import { iProduct } from "@/app/util/Interfaces";
import { getProducts } from "@/app/util/serverFunctions";
import ArrangeByPanel from "@/app/components/ArrangeByPanel";
import LoadingSpinner from "@/app/components/LoadingSpinner";
import ProductCard from "@/app/components/ProductCard";
import { allProductsStore } from "@/app/store/store";
import { iCalculatedRating } from "@/app/util/Interfaces";
import { calculateAverageReviewRatingSync } from "@/app/util/calculateAverageReviewRating";
import { WeightedRatingResult } from "@/app/util/calculateWeightedRating";
import { useAuth } from "@clerk/nextjs";
import { Search } from "lucide-react";

const Page = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  // Get products from atom first for instant UI
  const [allProducts, setAllProducts] = useAtom(allProductsStore);
  
  // Background fetch with atom as initial data
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes - consider data fresh
    initialData: allProducts?.length > 0 ? { success: true, data: allProducts } : undefined,
  }) as any;
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>("");

  const { userId } = useAuth();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const updateQueryParams = useCallback(() => {
    const params = new URLSearchParams();
    if (selectedRating) params.set("rating", selectedRating.toString());
    if (selectedTags.length > 0) params.set("tags", selectedTags.join(","));

    router.push(`${window.location.pathname}?${params.toString()}`, {
      scroll: false,
    });
  }, [selectedRating, selectedTags, router]);

  useEffect(() => {
    const ratingParam = searchParams.get("rating");
    const tagsParam = searchParams.get("tags");

    setSelectedRating(ratingParam ? Number(ratingParam) : null);
    setSelectedTags(tagsParam ? tagsParam.split(",") : []);
  }, [searchParams]);

  useEffect(() => {
    updateQueryParams();
  }, [selectedRating, selectedTags, updateQueryParams]);

  // Update the atom when fresh data arrives
  useEffect(() => {
    if (data && data.success && data.data && data.data !== allProducts) {
      setAllProducts(data.data);
    }
  }, [data, allProducts, setAllProducts]);

  useEffect(() => {
    if (selectedRating !== null) {
      // Reset selected tags when rating changes
      setSelectedTags([]);
    }
  }, [selectedRating]);

  const getFilteredTags = useMemo(() => {
    // Use allProducts from atom for instant UI
    if (!allProducts || !Array.isArray(allProducts) || allProducts.length === 0) {
      return [];
    }

    let productsArray: iProduct[] = [];

    // Safely use products from atom
    try {
      productsArray = allProducts;
    } catch (error) {
      console.error("Error processing product data:", error);
      return []; // Return empty array on error
    }

    // Safely filter products
    const filteredProducts = productsArray.filter((product) => {
      try {
        if (!product || !product.reviews) return false;

        // Check if product has weighted rating, otherwise calculate sync
        const hasWeightedRating = product.weightedRating && typeof product.weightedRating === 'object';
        const rating = hasWeightedRating 
          ? product.weightedRating
          : calculateAverageReviewRatingSync(product.reviews, true) as iCalculatedRating;
        return !selectedRating || (rating && rating.roundedRating === selectedRating);
      } catch (error) {
        console.error("Error filtering product:", error);
        return false; // Skip this product on error
      }
    });

    // Safely collect tags
    const tagSet = new Set<string>();
    filteredProducts.forEach((product) => {
      try {
        if (product.tags && Array.isArray(product.tags)) {
          product.tags.forEach((tag) => {
            if (tag) tagSet.add(tag);
          });
        }
      } catch (error) {
        console.error("Error processing product tags:", error);
        // Continue to next product
      }
    });

    return Array.from(tagSet);
  }, [allProducts, selectedRating]);

  const handleRatingChange = (rating: number | null) => {
    setSelectedRating(rating);
    setCurrentPage(1);
  };

  const handleTagChange = (tags: string[]) => {
    setSelectedTags(tags);
    setCurrentPage(1);
  };

  // Show loading only if no products in atom and still loading
  if (isLoading && (!allProducts || allProducts.length === 0)) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  // Handle error state only if no products in atom
  if (isError && (!allProducts || allProducts.length === 0)) {
    return (
      <div className="flex h-full flex-col items-center justify-center p-8 text-center">
        <div className="text-red-500 mb-4 text-xl">
          {error?.toString() || "Failed to load products"}
        </div>
        <p className="text-gray-600 mb-4">
          We are having trouble connecting to our database. Please try again
          later.
        </p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-myTheme-primary text-white rounded-lg hover:bg-myTheme-primary/90"
        >
          Refresh Page
        </button>
      </div>
    );
  }

  // Use products from atom for instant UI
  const products: iProduct[] = allProducts || [];

  // Safely filter products with null checks
  const filteredProducts = products.filter((product) => {
    try {
      if (!product || !product.reviews) return false;

      // Check if product has weighted rating, otherwise calculate sync
      const hasWeightedRating = product.weightedRating && typeof product.weightedRating === 'object';
      const rating = hasWeightedRating 
        ? product.weightedRating
        : calculateAverageReviewRatingSync(product.reviews, true) as iCalculatedRating;

      const matchesRating =
        !selectedRating || (rating && rating.roundedRating === selectedRating);
      const matchesTags =
        selectedTags.length === 0 ||
        (product.tags &&
          Array.isArray(product.tags) &&
          selectedTags.every((tag) => product.tags.includes(tag)));
      const matchesSearch =
        !searchTerm ||
        (product.name &&
          product.name.toLowerCase().includes(searchTerm.toLowerCase()));

      return matchesRating && matchesTags && matchesSearch;
    } catch (error) {
      console.error("Error filtering product:", error);
      return false; // Skip this product on error
    }
  });

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredProducts.slice(
    indexOfFirstItem,
    indexOfLastItem
  );

  console.log("Filtered products:", filteredProducts.length);
  console.log("Current items:", currentItems.length);

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-3">
            <h1 className="text-2xl font-bold text-gray-900">
              Browse Products & Services
            </h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Discover and explore products and services with authentic reviews
            from our community
          </p>
        </div>

        <div className="flex flex-col xl:flex-row gap-8">
          {/* Filters Panel */}
          <div className="w-full xl:w-80 xl:flex-shrink-0">
            <div className="sticky top-4">
              <ArrangeByPanel
                products={products}
                setSelectedRating={handleRatingChange}
                selectedRating={selectedRating}
                selectedTags={selectedTags}
                setSelectedTags={handleTagChange}
                filteredProductsLength={filteredProducts.length}
                availableTags={getFilteredTags}
              />
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1 min-w-0 space-y-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
              <div className="text-sm text-gray-500 mb-4">
                Showing {currentItems.length} of {filteredProducts.length}{" "}
                results
              </div>
              {currentItems.length > 0 ? (
                <div className="space-y-4">
                  {currentItems.map((product: iProduct) => (
<ProductCard key={product.id} product={product} options={{ showLatestReview: false, size: "small", showWriteReview: true, showClaimThisProduct: true }} currentUserId={userId ? userId : null} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 text-lg">
                    No products match the selected filters.
                  </p>
                </div>
              )}

              {/* Pagination */}
              <div className="flex flex-wrap justify-center gap-2 mt-8">
                {Array.from({ length: totalPages }, (_, index) => (
                  <button
                    key={index}
                    onClick={() => handlePageChange(index + 1)}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${currentPage === index + 1
                      ? "bg-myTheme-primary text-white shadow-md"
                      : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                      }`}
                  >
                    {index + 1}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
