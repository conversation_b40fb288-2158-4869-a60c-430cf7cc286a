import Reviews from "@/app/components/Reviews";
import { Metadata } from 'next';
import { generateMetadata as generatePageMetadata } from '@/app/lib/metadata';
import { getReviews, getProduct } from '@/app/util/serverFunctions';
import { fetchProductAndReviews } from '@/app/actions/reviewsPageActions';

type Props = {
  searchParams: { id: string }
}

export async function generateMetadata({ searchParams }: { searchParams: { id: string } }): Promise<Metadata> {
  const productId = searchParams.id;

  if (!productId) {
    return generatePageMetadata({
      title: 'Product Reviews | Review It',
      description: 'Browse and share product reviews on Review It',
    });
  }

  try {
    // Fetch both product details and reviews using the server action
    const response = await fetchProductAndReviews(productId);

    if (!response.success || !response.data) {
      return {
        title: 'Product Not Found | Review It',
        description: 'The requested product could not be found.',
      };
    }

    const { product, reviews } = response.data;

    // Check if product was found
    if (!product) {
      return {
        title: 'Product Not Found | Review It',
        description: 'The requested product could not be found.',
      };
    }

    // Safety check for required product fields
    const productName = product.name || 'Product';
    const productDescription = product.description || `Reviews for ${productName}`;

    // Ensure the image URL is absolute and valid
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://reviewit.gy';
    let imageUrl = '';
    if (product.display_image) {
      imageUrl = product.display_image.startsWith('http')
        ? product.display_image
        : product.display_image.startsWith('/')
          ? `${baseUrl}${product.display_image}`
          : `${baseUrl}/${product.display_image}`;
    } else {
      // If no image, use the default site logo
      imageUrl = `${baseUrl}/logo.png`;
    }

    // Calculate average rating
    const avgRating = reviews.length > 0
      ? reviews.reduce((sum: number, review: any) => sum + review.rating, 0) / reviews.length
      : 0;

    const roundedRating = Math.round(avgRating * 10) / 10;

    // Generate specific metadata for the product review page
    return {
      title: `${productName} | ${reviews.length} Reviews | Rating: ${roundedRating}/5`,
      description: productDescription.substring(0, 160) || `Read ${reviews.length} reviews for ${productName} on Review It. Find ratings, user feedback, and experiences.`,
      metadataBase: new URL(baseUrl),
      alternates: {
        canonical: `${baseUrl}/reviews?id=${encodeURIComponent(productId)}`,
      },
      openGraph: {
        title: `${productName} | ${reviews.length} Reviews`,
        description: productDescription.substring(0, 160),
        url: `${baseUrl}/reviews?id=${encodeURIComponent(productId)}`,
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: productName,
          }
        ],
        type: 'website',
        siteName: 'Review It',
      },
      twitter: {
        card: 'summary_large_image',
        title: `${productName} | ${reviews.length} Reviews`,
        description: productDescription.substring(0, 160),
        images: [imageUrl],
      },
    };
  } catch (error) {
    return generatePageMetadata({
      title: 'Product Reviews | Review It',
      description: 'Browse and share product reviews on Review It',
    });
  }
}

function calculateAverageRating(reviews: any[]): number {
  if (!reviews?.length) return 0;
  const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
  return Number((sum / reviews.length).toFixed(1));
}

export default function ReviewPage({ searchParams }: Props) {
  const productId = searchParams.id;

  if (!productId) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <h1 className="text-2xl font-bold mb-4">Product ID Required</h1>
        <p className="text-gray-600">Please provide a product ID to view reviews.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col bg-myTheme-lightbg w-full items-center justify-start">
      <div className="w-full max-w-6xl mx-auto px-4">
        <Reviews productId={productId} />
      </div>
    </div>
  );
}
