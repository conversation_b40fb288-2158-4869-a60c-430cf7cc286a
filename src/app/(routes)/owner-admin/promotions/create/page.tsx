"use client";

import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { getUser } from "@/app/util/serverFunctions";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, ChevronLeft, AlertTriangle, Upload, Info } from "lucide-react";
import { format } from "date-fns";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { iUser, iBusiness, iProduct } from "@/app/util/Interfaces";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Form validation schema
const promotionFormSchema = z.object({
    title: z.string().min(5, {
        message: "Title must be at least 5 characters",
    }).max(50, {
        message: "Title must not exceed 50 characters",
    }),
    description: z.string().min(10, {
        message: "Description must be at least 10 characters",
    }).max(200, {
        message: "Description must not exceed 200 characters",
    }),
    startDate: z.date({
        required_error: "Start date is required",
    }),
    endDate: z.date({
        required_error: "End date is required",
    }),
    discountType: z.enum(["percentage", "amount", "none"], {
        required_error: "Discount type is required",
    }),
    discountValue: z.coerce.number().min(0).optional(),
    promotionCode: z.string().max(20, {
        message: "Promotion code must not exceed 20 characters",
    }).optional(),
    isActive: z.boolean().default(false),
    productId: z.string({
        required_error: "Product is required",
    }),
}).refine(data => data.endDate > data.startDate, {
    message: "End date must be after start date",
    path: ["endDate"],
}).refine(data => {
    if (data.discountType === "none") return true;
    return data.discountValue !== undefined && data.discountValue > 0;
}, {
    message: "Discount value is required when a discount type is selected",
    path: ["discountValue"],
});

type PromotionFormValues = z.infer<typeof promotionFormSchema>;

// Function to create a promotion
const createPromotion = async (promotionData: any) => {
    const response = await fetch('/api/promotions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(promotionData),
    });

    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create promotion');
    }

    return response.json();
};

export default function CreatePromotionPage() {
    const auth = useAuth();
    const router = useRouter();
    const queryClient = useQueryClient();
    const [previewImage, setPreviewImage] = useState<string | null>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const { data, isLoading, isError, error } = useQuery({
        queryKey: ["user", auth.userId],
        queryFn: async () => await getUser(),
        refetchOnWindowFocus: false,
    }) as any;

    // Form definition
    const form = useForm<PromotionFormValues>({
        resolver: async (values) => {
            try {
                const data = await promotionFormSchema.parseAsync(values);
                return { values: data, errors: {} };
            } catch (error) {
                if (error instanceof z.ZodError) {
                    return { values: {}, errors: error.formErrors?.fieldErrors || {} };
                }
                return { values: {}, errors: {} };
            }
        },
        defaultValues: {
            title: "",
            description: "",
            startDate: new Date(),
            endDate: new Date(new Date().setDate(new Date().getDate() + 30)), // Default to 30 days from now
            discountType: "percentage",
            discountValue: 10,
            promotionCode: "",
            isActive: true,
            productId: "",
        },
    });

    // Form submission handler
    const onSubmit = async (values: PromotionFormValues) => {
        if (isSubmitting) return;
        
        setIsSubmitting(true);
        
        try {
            // Get business ID for the selected product
            const businessId = getProductBusinessId(values.productId);
            if (!businessId) {
                throw new Error("Could not find business for selected product");
            }

            // Create a promotion object from the form values
            const promotionData = {
                title: values.title,
                description: values.description,
                startDate: values.startDate.toISOString(),
                endDate: values.endDate.toISOString(),
                discountPercentage: values.discountType === "percentage" ? values.discountValue : undefined,
                discountAmount: values.discountType === "amount" ? values.discountValue : undefined,
                promotionCode: values.promotionCode || undefined,
                isActive: values.isActive,
                productId: values.productId,
                businessId: businessId,
                image: previewImage || undefined,
            };

            console.log("Creating promotion with data:", promotionData);

            // Call the API to create the promotion
            const result = await createPromotion(promotionData);
            
            if (result.success) {
                // Invalidate and refetch promotions data
                queryClient.invalidateQueries({ queryKey: ['business-promotions'] });
                queryClient.invalidateQueries({ queryKey: ['promotions'] });
                
                // Show success message and redirect
                alert("Promotion created successfully!");
                router.push("/owner-admin/promotions");
            } else {
                throw new Error(result.error || "Failed to create promotion");
            }
        } catch (error) {
            console.error("Error creating promotion:", error);
            alert(`Failed to create promotion: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } finally {
            setIsSubmitting(false);
        }
    };

    // Handle image upload
    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            const reader = new FileReader();

            reader.onload = (event) => {
                if (event.target && typeof event.target.result === 'string') {
                    setPreviewImage(event.target.result);
                }
            };

            reader.readAsDataURL(file);
        }
    };

    // Get business ID for a product
    const getProductBusinessId = (productId: string) => {
        if (!data?.data) return null;

        const user: iUser = data.data;
        const businesses = user.businesses || [];

        for (const business of businesses) {
            const product = (business.products || []).find(p => p.id === productId);
            if (product) {
                return business.id;
            }
        }

        return null;
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (isError) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-red-500" />
                <h3 className="text-xl font-semibold">Error loading user data</h3>
                <p className="text-muted-foreground">{error?.toString() || "An unknown error occurred"}</p>
            </div>
        );
    }

    if (!data?.data) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-yellow-500" />
                <h3 className="text-xl font-semibold">No user data available</h3>
                <p className="text-muted-foreground">We couldn&apos;t load your user profile</p>
            </div>
        );
    }

    const user: iUser = data.data as iUser;
    const businesses = user.businesses || [];
    const allProducts = businesses.flatMap(business => business.products || []);

    if (allProducts.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-yellow-500" />
                <h3 className="text-xl font-semibold">No products available</h3>
                <p className="text-muted-foreground max-w-md text-center">
                    You don&apos;t have any products to create promotions for. Add a product first.
                </p>
                <Button asChild>
                    <Link href="/submit">Add New Product</Link>
                </Button>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center gap-2">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push("/owner-admin/promotions")}
                >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Back to promotions
                </Button>
            </div>

            <div>
                <h2 className="text-3xl font-bold tracking-tight">Create Promotion</h2>
                <p className="text-muted-foreground">
                    Create a new special offer to promote your products
                </p>
            </div>

            <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Tips for effective promotions</AlertTitle>
                <AlertDescription>
                    Set a clear title, specific time period, and attractive discount. Using a promotion code can help track effectiveness.
                </AlertDescription>
            </Alert>

            <Card>
                <CardHeader>
                    <CardTitle>Promotion Details</CardTitle>
                    <CardDescription>
                        Enter the details of your new promotion
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-6">
                                    {/* Product Selection */}
                                    <FormField
                                        control={form.control}
                                        name="productId"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Product</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    defaultValue={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select a product" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {allProducts.map((product) => (
                                                            <SelectItem key={product.id} value={product.id || ""}>
                                                                {product.name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormDescription>
                                                    The product this promotion applies to
                                                </FormDescription>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Title */}
                                    <FormField
                                        control={form.control}
                                        name="title"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Title</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="Summer Special Offer" {...field} />
                                                </FormControl>
                                                <FormDescription>
                                                    A catchy title for your promotion
                                                </FormDescription>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Description */}
                                    <FormField
                                        control={form.control}
                                        name="description"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Description</FormLabel>
                                                <FormControl>
                                                    <Textarea
                                                        placeholder="Get 20% off on all summer products until August 31st"
                                                        className="min-h-[100px]"
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormDescription>
                                                    Describe what customers will get
                                                </FormDescription>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Promotion Code */}
                                    <FormField
                                        control={form.control}
                                        name="promotionCode"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Promotion Code (Optional)</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="SUMMER20" {...field} />
                                                </FormControl>
                                                <FormDescription>
                                                    Code that customers can enter to redeem the offer
                                                </FormDescription>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="space-y-6">
                                    {/* Discount Type */}
                                    <FormField
                                        control={form.control}
                                        name="discountType"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Discount Type</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    defaultValue={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select discount type" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        <SelectItem value="percentage">Percentage Discount</SelectItem>
                                                        <SelectItem value="amount">Fixed Amount Discount</SelectItem>
                                                        <SelectItem value="none">No Discount (Informational)</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                <FormDescription>
                                                    Type of discount offered
                                                </FormDescription>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {/* Discount Value */}
                                    {form.watch("discountType") !== "none" && (
                                        <FormField
                                            control={form.control}
                                            name="discountValue"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>
                                                        {form.watch("discountType") === "percentage"
                                                            ? "Discount Percentage"
                                                            : "Discount Amount"}
                                                    </FormLabel>
                                                    <FormControl>
                                                        <div className="relative">
                                                            <Input
                                                                type="number"
                                                                placeholder={form.watch("discountType") === "percentage" ? "20" : "10"}
                                                                {...field}
                                                            />
                                                            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                                                {form.watch("discountType") === "percentage" ? "%" : "$"}
                                                            </div>
                                                        </div>
                                                    </FormControl>
                                                    <FormDescription>
                                                        {form.watch("discountType") === "percentage"
                                                            ? "Percentage discount (e.g. 20 for 20% off)"
                                                            : "Fixed amount discount in dollars (e.g. 10 for $10 off)"}
                                                    </FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    )}

                                    {/* Date Range */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="startDate"
                                            render={({ field }) => (
                                                <FormItem className="flex flex-col">
                                                    <FormLabel>Start Date</FormLabel>
                                                    <Popover>
                                                        <PopoverTrigger asChild>
                                                            <FormControl>
                                                                <Button
                                                                    variant={"outline"}
                                                                    className="pl-3 text-left font-normal"
                                                                >
                                                                    {field.value ? (
                                                                        format(field.value, "PPP")
                                                                    ) : (
                                                                        <span>Pick a date</span>
                                                                    )}
                                                                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                                                </Button>
                                                            </FormControl>
                                                        </PopoverTrigger>
                                                        <PopoverContent className="w-auto p-0" align="start">
                                                            <Calendar
                                                                mode="single"
                                                                selected={field.value}
                                                                onSelect={field.onChange}
                                                                initialFocus
                                                            />
                                                        </PopoverContent>
                                                    </Popover>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="endDate"
                                            render={({ field }) => (
                                                <FormItem className="flex flex-col">
                                                    <FormLabel>End Date</FormLabel>
                                                    <Popover>
                                                        <PopoverTrigger asChild>
                                                            <FormControl>
                                                                <Button
                                                                    variant={"outline"}
                                                                    className="pl-3 text-left font-normal"
                                                                >
                                                                    {field.value ? (
                                                                        format(field.value, "PPP")
                                                                    ) : (
                                                                        <span>Pick a date</span>
                                                                    )}
                                                                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                                                </Button>
                                                            </FormControl>
                                                        </PopoverTrigger>
                                                        <PopoverContent className="w-auto p-0" align="start">
                                                            <Calendar
                                                                mode="single"
                                                                selected={field.value}
                                                                onSelect={field.onChange}
                                                                initialFocus
                                                            />
                                                        </PopoverContent>
                                                    </Popover>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>

                                    {/* Active Status */}
                                    <FormField
                                        control={form.control}
                                        name="isActive"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                                                <FormControl>
                                                    <input
                                                        type="checkbox"
                                                        checked={field.value}
                                                        onChange={field.onChange}
                                                        className="w-4 h-4 mt-1"
                                                    />
                                                </FormControl>
                                                <div className="space-y-1 leading-none">
                                                    <FormLabel>
                                                        Activate Promotion
                                                    </FormLabel>
                                                    <FormDescription>
                                                        When checked, this promotion will be live as soon as the start date arrives
                                                    </FormDescription>
                                                </div>
                                            </FormItem>
                                        )}
                                    />

                                    {/* Image Upload */}
                                    <div className="space-y-3">
                                        <Label>Promotion Image (Optional)</Label>
                                        <div className="border rounded-md p-4">
                                            {previewImage ? (
                                                <div className="relative aspect-video mb-3">
                                                    <Image
                                                        src={previewImage}
                                                        alt="Promotion preview"
                                                        width={1200}
                                                        height={600}
                                                        className="object-cover rounded-md w-full h-full"
                                                    />
                                                    <Button
                                                        type="button"
                                                        variant="destructive"
                                                        size="sm"
                                                        className="absolute top-2 right-2"
                                                        onClick={() => setPreviewImage(null)}
                                                    >
                                                        Remove
                                                    </Button>
                                                </div>
                                            ) : (
                                                <div className="flex items-center justify-center border border-dashed rounded-md h-32 bg-muted mb-3">
                                                    <Upload className="h-8 w-8 text-muted-foreground" />
                                                </div>
                                            )}
                                            <Input
                                                type="file"
                                                accept="image/*"
                                                onChange={handleImageUpload}
                                                className="cursor-pointer"
                                            />
                                            <p className="text-xs text-muted-foreground mt-2">
                                                Upload an image for this promotion. Recommended size: 1200x600px.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <div className="flex justify-end gap-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => router.push("/owner-admin/promotions")}
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={isSubmitting}>
                                    {isSubmitting ? "Creating..." : "Create Promotion"}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </CardContent>
            </Card>
        </div>
    );
} 