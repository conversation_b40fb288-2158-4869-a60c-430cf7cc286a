"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { getUser } from "@/app/util/serverFunctions";
import {
  getBusinessAnalytics,
  getTrafficSources,
} from "@/app/util/analyticsService";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";
import {
  iUser,
  iBusiness,
  iBusinessAnalytics,
  iTrafficSource,
  iAnalyticsPeriod,
} from "@/app/util/Interfaces";

// Import our new components
import { AnalyticsHeader } from "@/components/analytics/AnalyticsHeader";
import { OverviewTab } from "@/components/analytics/OverviewTab";
import { TrafficTab } from "@/components/analytics/TrafficTab";
import { ProductsTab } from "@/components/analytics/ProductsTab";
import { ReviewsTab } from "@/components/analytics/ReviewsTab";
import { ExportPanel } from "@/components/analytics/ExportPanel";

// Mock analytics data - used as fallback when API fails
const mockBusinessAnalytics: iBusinessAnalytics = {
  id: "ba1",
  businessId: "business1",
  totalViews: 12850,
  uniqueVisitors: 8320,
  totalReviews: 168,
  averageRating: 4.2,
  viewsPerDay: {
    "2023-10-01": 320,
    "2023-10-02": 340,
    "2023-10-03": 360,
    "2023-10-04": 420,
    "2023-10-05": 380,
    "2023-10-06": 410,
    "2023-10-07": 430,
    "2023-10-08": 400,
    "2023-10-09": 450,
    "2023-10-10": 470,
    "2023-10-11": 490,
    "2023-10-12": 510,
    "2023-10-13": 530,
    "2023-10-14": 520,
    "2023-10-15": 560,
    "2023-10-16": 580,
    "2023-10-17": 600,
    "2023-10-18": 620,
    "2023-10-19": 640,
    "2023-10-20": 660,
    "2023-10-21": 680,
    "2023-10-22": 700,
    "2023-10-23": 720,
    "2023-10-24": 740,
    "2023-10-25": 760,
    "2023-10-26": 780,
    "2023-10-27": 800,
    "2023-10-28": 820,
    "2023-10-29": 840,
    "2023-10-30": 860,
  },
  trafficSources: {
    Google: 4560,
    Direct: 2830,
    "Social Media": 1920,
    Email: 980,
    Referral: 1560,
  },
  deviceTypes: {
    Mobile: 5850,
    Desktop: 4320,
    Tablet: 2680,
  },
  conversionRate: 2.8,
  topProducts: [
    {
      id: "product1",
      name: "Premium Coffee Maker",
      views: 3250,
      conversion: 3.5,
    },
    {
      id: "product2",
      name: "Espresso Machine",
      views: 2180,
      conversion: 2.2,
    },
    {
      id: "product3",
      name: "Coffee Grinder Pro",
      views: 1920,
      conversion: 2.7,
    },
  ],
  lastUpdated: new Date(),
};

export default function AnalyticsDashboardPage() {
  const auth = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const businessId = searchParams.get("id");
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedPeriod, setSelectedPeriod] = useState<iAnalyticsPeriod>(() => ({
    startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
    endDate: new Date(),
    comparison: "previous_period",
  }));
  const [selectedBusiness, setSelectedBusiness] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["user", auth.userId],
    queryFn: async () => await getUser(),
    refetchOnWindowFocus: false,
  }) as any;

  // Memoize user data and businesses first (before any early returns)
  const user: iUser = useMemo(() => data?.data || {}, [data?.data]);
  const businesses = useMemo(() => user?.businesses || [], [user?.businesses]);

  // Get current business using useMemo to prevent infinite loops
  const business = useMemo((): iBusiness | null => {
    if (!data?.data) return null;

    if (businessId) {
      return businesses.find((b) => b.id === businessId) || null;
    }

    if (selectedBusiness) {
      return businesses.find((b) => b.id === selectedBusiness) || null;
    }

    return businesses.length > 0 ? businesses[0] : null;
  }, [data?.data, businessId, selectedBusiness, businesses]);

  const currentBusinessId = useMemo(() => business?.id || "", [business?.id]);

  // Initialize selectedBusiness when data loads
  useEffect(() => {
    if (data?.data && !selectedBusiness && !businessId) {
      if (businesses.length > 0) {
        setSelectedBusiness(businesses[0].id);
      }
    }
  }, [data?.data, selectedBusiness, businessId, businesses]);

  // Query for analytics data
  const {
    data: analyticsData,
    isLoading: isLoadingAnalytics,
    isError: isErrorAnalytics,
    refetch: refetchAnalytics,
  } = useQuery({
    queryKey: [
      "business-analytics",
      currentBusinessId,
      selectedPeriod.startDate,
      selectedPeriod.endDate,
    ],
    queryFn: async () => {
      if (!currentBusinessId) return null;
      return await getBusinessAnalytics(currentBusinessId, selectedPeriod);
    },
    enabled: !!currentBusinessId,
    refetchOnWindowFocus: false,
  });

  // Query for traffic sources
  const {
    data: trafficSourcesData,
    isLoading: isLoadingTraffic,
    isError: isErrorTraffic,
  } = useQuery({
    queryKey: [
      "traffic-sources",
      currentBusinessId,
      selectedPeriod.startDate,
      selectedPeriod.endDate,
    ],
    queryFn: async () => {
      if (!currentBusinessId) return null;
      return await getTrafficSources(currentBusinessId, selectedPeriod);
    },
    enabled: !!currentBusinessId && activeTab === "traffic",
    refetchOnWindowFocus: false,
  });

  // Use the real data from the API, but ensure it's not null/undefined
  const analyticsDataSafe = useMemo(() => analyticsData || mockBusinessAnalytics, [analyticsData]);

  // Handle period change with useCallback to prevent re-renders
  const handlePeriodChange = useCallback((period: iAnalyticsPeriod) => {
    setSelectedPeriod(period);
  }, []);

  // Refresh analytics data with useCallback
  const refreshData = useCallback(() => {
    setIsRefreshing(true);
    refetchAnalytics().then(() => {
      setIsRefreshing(false);
    });
  }, [refetchAnalytics]);

  // Stabilize setSelectedBusiness callback
  const handleBusinessChange = useCallback((businessId: string | null) => {
    setSelectedBusiness(businessId);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
        <AlertTriangle className="h-12 w-12 text-red-500" />
        <h3 className="text-xl font-semibold">Error loading user data</h3>
        <p className="text-muted-foreground">
          {error?.toString() || "An unknown error occurred"}
        </p>
      </div>
    );
  }

  // If no business is found
  if (!business) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
        <AlertTriangle className="h-12 w-12 text-yellow-500" />
        <h3 className="text-xl font-semibold">No business selected</h3>
        <p className="text-muted-foreground">
          Please select a business to view analytics
        </p>
        <Button asChild>
          <Link href="/owner-admin">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  // Show loading state for analytics data
  if (isLoadingAnalytics) {
    return (
      <div className="space-y-6">
        <AnalyticsHeader
          business={business}
          businesses={data?.data?.businesses || []}
          selectedBusiness={selectedBusiness}
          setSelectedBusiness={handleBusinessChange}
          onPeriodChange={handlePeriodChange}
          onRefresh={refreshData}
          isRefreshing={isRefreshing}
        />

        <div className="flex items-center justify-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  // Error fetching analytics
  if (isErrorAnalytics || !analyticsData) {
    return (
      <div className="space-y-6">
        <AnalyticsHeader
          business={business}
          businesses={data?.data?.businesses || []}
          selectedBusiness={selectedBusiness}
          setSelectedBusiness={handleBusinessChange}
          onPeriodChange={handlePeriodChange}
          onRefresh={refreshData}
          isRefreshing={isRefreshing}
        />

        <div className="flex flex-col items-center justify-center py-16 space-y-4">
          <AlertTriangle className="h-12 w-12 text-red-500" />
          <h3 className="text-xl font-semibold">
            Error loading analytics data
          </h3>
          <p className="text-muted-foreground">
            An error occurred while fetching analytics data
          </p>
          <Button onClick={refreshData}>Try Again</Button>
        </div>
      </div>
    );
  }

  // We have data, render the dashboard
  return (
    <div className="space-y-4 sm:space-y-6 px-2 sm:px-4 lg:px-6">
      <AnalyticsHeader
        business={business}
        businesses={businesses}
        selectedBusiness={selectedBusiness}
        setSelectedBusiness={handleBusinessChange}
        onPeriodChange={handlePeriodChange}
        onRefresh={refreshData}
        isRefreshing={isRefreshing}
      />

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <div className="w-full">
          <TabsList className="w-full sm:w-auto">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="traffic">Traffic</TabsTrigger>
            <TabsTrigger value="products">Products</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="overview" className="space-y-6">
          <OverviewTab analyticsData={analyticsDataSafe} />
        </TabsContent>

        <TabsContent value="traffic" className="space-y-6">
          <TrafficTab
            analyticsData={analyticsDataSafe}
            trafficSourcesData={trafficSourcesData}
            isLoadingTraffic={isLoadingTraffic}
            isErrorTraffic={isErrorTraffic}
          />
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <ProductsTab analyticsData={analyticsDataSafe} />
        </TabsContent>

        <TabsContent value="reviews" className="space-y-6">
          <ReviewsTab analyticsData={analyticsDataSafe} />
        </TabsContent>
      </Tabs>

      <ExportPanel
        analyticsData={analyticsDataSafe}
        selectedPeriod={selectedPeriod}
      />
    </div>
  );
}
