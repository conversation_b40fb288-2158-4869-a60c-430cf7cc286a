"use client";

import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { getUser, getReviews } from "@/app/util/serverFunctions";
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    CardDescription,
    CardFooter
} from "@/components/ui/card";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
    ChevronDown,
    Search,
    MessageSquare,
    Star,
    Thum<PERSON>Up,
    ThumbsDown,
    Filter,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>ir<PERSON>
} from "lucide-react";
import { iUser, iProduct, iBusiness, iReview } from "@/app/util/Interfaces";

export default function ReviewsManagement() {
    const auth = useAuth();
    const searchParams = useSearchParams();
    const [searchTerm, setSearchTerm] = useState("");
    const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
    const [selectedRating, setSelectedRating] = useState<number | null>(null);
    const [responseMode, setResponseMode] = useState<Record<string, boolean>>({});
    const [responses, setResponses] = useState<Record<string, string>>({});

    // Handle URL parameters
    useEffect(() => {
        const productId = searchParams.get("product");
        if (productId) {
            setSelectedProduct(productId);
        }
    }, [searchParams]);

    const { data: userData, isLoading: userLoading, isError: userError, error: userErrorData } = useQuery({
        queryKey: ["user", auth.userId],
        queryFn: async () => await getUser(),
        refetchOnWindowFocus: false,
    }) as any;

    const businesses = userData?.data?.businesses || [];
    const allProducts = businesses.flatMap((business: iBusiness) => business.products || []);

    const { data: fetchedReviews, isLoading: reviewsLoading } = useQuery({
        queryKey: ['reviews', selectedProduct],
        queryFn: async () => {
            // If a product is selected, get reviews for that product
            if (selectedProduct) {
                const response = await getReviews(selectedProduct);
                if (!response.success || !response.data) {
                    throw new Error(response.error || 'Failed to fetch reviews');
                }
                return response.data;
            }
            // If no product is selected, get all reviews for the user's products
            const allProductIds = allProducts.map((p: iProduct) => p.id).filter(Boolean) as string[];
            const allReviews = await Promise.all(
                allProductIds.map(async (productId) => {
                    const response = await getReviews(productId);
                    if (!response.success || !response.data) {
                        return [];
                    }
                    return response.data;
                })
            );
            return allReviews.flat();
        },
        enabled: !!allProducts.length,
    });

    // Show loading state while data is being fetched
    if (userLoading || reviewsLoading) {
        return (
            <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    // Handle error state
    if (userError) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-red-500" />
                <h3 className="text-xl font-semibold">Error loading reviews</h3>
                <p className="text-muted-foreground">{userErrorData?.toString() || "An unknown error occurred"}</p>
            </div>
        );
    }

    // Handle case where user data is not available
    if (!userData?.data) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-yellow-500" />
                <h3 className="text-xl font-semibold">No user data available</h3>
                <p className="text-muted-foreground">We couldn&apos;t load your user profile</p>
            </div>
        );
    }

    const user: iUser = userData.data as iUser;

    // Get the selected product (if any)
    const selectedProductObj = selectedProduct
        ? allProducts.find((p: iProduct) => p.id === selectedProduct)
        : null;

    // Get all reviews across all products or for the selected product
    let reviews: iReview[] = [];
    if (selectedProduct) {
        reviews = selectedProductObj?.reviews || [];
    } else {
        reviews = allProducts.flatMap((product: iProduct) => product.reviews || []);
    }

    // Apply filters
    if (searchTerm) {
        const term = searchTerm.toLowerCase();
        reviews = reviews.filter(review =>
            review.title.toLowerCase().includes(term) ||
            review.body.toLowerCase().includes(term)
        );
    }

    if (selectedRating !== null) {
        reviews = reviews.filter(review => Math.round(review.rating) === selectedRating);
    }

    // Sort reviews - newest first
    reviews.sort((a, b) => new Date(b.createdDate || 0).getTime() - new Date(a.createdDate || 0).getTime());

    // Get product name by ID (for displaying product name in reviews)
    const getProductName = (productId: string) => {
        const product = allProducts.find((p: iProduct) => p.id === productId);
        return product ? product.name : "Unknown Product";
    };

    // Toggle response mode for a specific review
    const toggleResponseMode = (reviewId: string) => {
        setResponseMode(prev => ({
            ...prev,
            [reviewId]: !prev[reviewId]
        }));
    };

    // Handle response text changes
    const handleResponseChange = (reviewId: string, text: string) => {
        setResponses(prev => ({
            ...prev,
            [reviewId]: text
        }));
    };

    // Submit response (would connect to API in real implementation)
    const submitResponse = (reviewId: string) => {
        // In real implementation, this would send the response to the server
        console.log(`Submitting response for review ${reviewId}: ${responses[reviewId]}`);

        // Mock success - Clear response mode and keep the response text
        toggleResponseMode(reviewId);

        // Show success message
        alert("Response submitted successfully!");
    };

    return (
        <div className="space-y-6">
            <div className="flex flex-col lg:flex-row justify-between gap-4">
                <div>
                    <h2 className="text-3xl font-bold tracking-tight">Reviews Management</h2>
                    <p className="text-muted-foreground">
                        View and respond to customer reviews
                    </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                    <Button asChild variant="outline">
                        <Link href="/owner-admin">
                            Dashboard
                        </Link>
                    </Button>
                    <Button asChild>
                        <Link href="/owner-admin/products">
                            Manage Products
                        </Link>
                    </Button>
                </div>
            </div>

            <div className="grid gap-4 md:grid-cols-4">
                {/* Search and filters */}
                <div className="md:col-span-3 space-y-4">
                    <div className="flex flex-col sm:flex-row gap-2">
                        <div className="relative flex-grow">
                            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                type="search"
                                placeholder="Search reviews..."
                                className="pl-8"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                        </div>
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline">
                                    <Filter className="mr-2 h-4 w-4" />
                                    Rating
                                    <ChevronDown className="ml-2 h-4 w-4" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => setSelectedRating(null)}>
                                    All Ratings
                                </DropdownMenuItem>
                                {[5, 4, 3, 2, 1].map((rating) => (
                                    <DropdownMenuItem
                                        key={rating}
                                        onClick={() => setSelectedRating(rating)}
                                    >
                                        {rating} {rating === 1 ? 'Star' : 'Stars'}
                                    </DropdownMenuItem>
                                ))}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </div>

                {/* Product selector */}
                <div>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" className="w-full">
                                {selectedProduct
                                    ? getProductName(selectedProduct)
                                    : "All Products"}
                                <ChevronDown className="ml-2 h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[200px]">
                            <DropdownMenuItem onClick={() => setSelectedProduct(null)}>
                                All Products
                            </DropdownMenuItem>
                            {allProducts.map((product: iProduct) => (
                                <DropdownMenuItem
                                    key={product.id}
                                    onClick={() => product.id && setSelectedProduct(product.id)}
                                >
                                    {product.name}
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>

            {/* Reviews list */}
            <div>
                {reviews.length === 0 ? (
                    <div className="flex flex-col items-center justify-center bg-muted rounded-lg p-8 space-y-4">
                        <div className="rounded-full bg-background p-3">
                            <MessageSquare className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <h3 className="text-xl font-semibold">No reviews found</h3>
                        <p className="text-center text-muted-foreground max-w-md">
                            {searchTerm || selectedRating !== null
                                ? "Try adjusting your filters to see more reviews"
                                : "Your products haven't received any reviews yet. As customers leave reviews, they'll appear here."}
                        </p>
                    </div>
                ) : (
                    <div className="grid gap-4">
                        {reviews.map((review: iReview) => (
                            <ReviewCard
                                key={review.id}
                                review={review}
                                productName={getProductName(review.productId)}
                                isResponseMode={responseMode[review.id || ''] || false}
                                responseText={responses[review.id || ''] || ''}
                                onToggleResponse={() => toggleResponseMode(review.id || '')}
                                onResponseChange={(text) => handleResponseChange(review.id || '', text)}
                                onSubmitResponse={() => submitResponse(review.id || '')}
                            />
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}

// Review Card component
interface ReviewCardProps {
    review: iReview;
    productName: string;
    isResponseMode: boolean;
    responseText: string;
    onToggleResponse: () => void;
    onResponseChange: (text: string) => void;
    onSubmitResponse: () => void;
}

function ReviewCard({
    review,
    productName,
    isResponseMode,
    responseText,
    onToggleResponse,
    onResponseChange,
    onSubmitResponse
}: ReviewCardProps) {
    const stars = [];
    for (let i = 0; i < 5; i++) {
        stars.push(
            <Star
                key={i}
                className={`h-4 w-4 ${i < Math.round(review.rating) ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
            />
        );
    }

    return (
        <Card>
            <CardHeader>
                <div className="flex justify-between items-start">
                    <div>
                        <CardTitle className="text-lg font-semibold">{review.title}</CardTitle>
                        <CardDescription>
                            For product: <Link href={`/product/${review.productId}`} className="hover:underline">{productName}</Link>
                        </CardDescription>
                    </div>
                    <div className="flex flex-col items-end">
                        <div className="flex">{stars}</div>
                        <CardDescription>
                            {review.createdDate ? new Date(review.createdDate).toLocaleDateString() : 'Unknown date'}
                        </CardDescription>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    <p className="text-sm">{review.body}</p>

                    {/* Helpful votes */}
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center">
                            <ThumbsUp className="h-4 w-4 mr-1" />
                            <span>{review.helpfulVotes || 0} helpful</span>
                        </div>
                        <div className="flex items-center">
                            <ThumbsDown className="h-4 w-4 mr-1" />
                            <span>{review.unhelpfulVotes || 0} unhelpful</span>
                        </div>
                    </div>

                    {/* If the review has images, show them */}
                    {review.images && review.images.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                            {review.images.map((image: string, index: number) => (
                                <div key={index} className="h-16 w-16 relative rounded overflow-hidden">
                                    <Image
                                        src={image}
                                        alt={`Review image ${index + 1}`}
                                        width={64}
                                        height={64}
                                        className="h-full w-full object-cover"
                                    />
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </CardContent>
            <CardFooter>
                {isResponseMode ? (
                    <div className="w-full space-y-3">
                        <Textarea
                            placeholder="Write your response to this review..."
                            value={responseText}
                            onChange={(e) => onResponseChange(e.target.value)}
                            className="min-h-[100px]"
                        />
                        <div className="flex justify-end space-x-2">
                            <Button variant="outline" onClick={onToggleResponse}>
                                Cancel
                            </Button>
                            <Button onClick={onSubmitResponse} disabled={!responseText.trim()}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Submit Response
                            </Button>
                        </div>
                    </div>
                ) : (
                    <Button variant="outline" onClick={onToggleResponse}>
                        <Reply className="mr-2 h-4 w-4" />
                        Respond to Review
                    </Button>
                )}
            </CardFooter>
        </Card>
    );
} 