import { useAtom } from "jotai";
import { useEffect } from "react";
import {
    currentUserAtom,
    userLoading<PERSON>tom,
    userFetch<PERSON>tom,
    userFetchedAtom,
    isUserLoading,
    hasUserData
} from "@/app/store/store";

export const useUser = () => {
    const [user] = useAtom(currentUserAtom);
    const [loading] = useAtom(userLoadingAtom);
    const [fetched] = useAtom(userFetchedAtom);
    const [, fetchUser] = useAtom(userFetchAtom);

    // Auto-fetch user on mount if not already loaded
    useEffect(() => {
        if (!hasUserData(user) && !loading && !fetched) {
            fetchUser();
        }
    }, [user, loading, fetched, fetchUser]);

    const isLoggedIn = hasUserData(user);
    const isNotLoggedIn = fetched && !isLoggedIn;
    const isStillLoading = loading || (!fetched && !isLoggedIn);

    return {
        user,
        loading,
        fetched,
        isLoading: isStillLoading,
        isLoggedIn,
        isNotLoggedIn,
        hasData: hasUserData(user),
        refetch: fetchUser,
    };
}; 