import { atom } from "jotai";
import {
  iProduct,
  iProductOwnerNotification,
  iReview,
  iUser,
  iUserNotification,
} from "../util/Interfaces";
import { getNotifications, getUser } from "../util/serverFunctions";
import { iNotification } from "../util/Interfaces";

// Helper function to create an empty user object that satisfies iUser interface
function createEmptyUser(): iUser {
  return {
    id: '',
    bio: null,
    userName: '',
    avatar: null,
    createdDate: new Date(),
    email: '',
    firstName: '',
    lastName: '',
    clerkUserId: '',
    isDeleted: false,
    role: 'USER',
    status: 'ACTIVE',
    // Essential arrays that components check
    businesses: [],
    reviews: [],
    comments: [],
    likedReviews: [],
    _count: {
      reviews: 0,
      comments: 0,
      product: 0,
    },
    // Optional fields can be undefined
    lastLoginAt: undefined,
    loginCount: undefined,
    suspendedUntil: undefined,
    suspendedReason: undefined,
    product: undefined,
    createdBy: undefined,
    createdById: undefined,
    adminActions: undefined,
    moderationEvents: undefined,
    commentVotes: undefined,
    productClaims: undefined,
    reviewedClaims: undefined,
  };
}

export const notificationsAtom = atom<iNotification[]>([]);
export const userNotificationsAtom = atom<iUserNotification[]>([]);
export const ownerNotificationsAtom = atom<iProductOwnerNotification[]>([]);
export const allProductsStore = atom<iProduct[]>([]);

// Enhanced allProductsAtom with cache synchronization
export const allProductsAtom = atom(
  (get) => get(allProductsStore),
  (get, set, update: iProduct[] | ((prev: iProduct[]) => iProduct[])) => {
    // Update the store
    if (typeof update === 'function') {
      set(allProductsStore, update);
    } else {
      set(allProductsStore, update);
    }
    
    // Trigger Redis cache refresh on client update (only in browser)
    if (typeof window !== 'undefined') {
      fetch('/api/revalidate?path=/browse')
        .then(response => {
          if (response.ok) {
            console.log('Cache revalidation triggered for /browse');
          } else {
            console.warn('Failed to trigger cache revalidation');
          }
        })
        .catch(error => {
          console.warn('Error triggering cache revalidation:', error);
        });
    }
  }
);
export const currentReviewAtom = atom<iReview | null>(null);

// Create separate atoms for user state management
export const currentUserAtom = atom<iUser>(createEmptyUser());
export const userLoadingAtom = atom<boolean>(false);
export const userFetchingAtom = atom<boolean>(false); // Prevent multiple fetches
export const userFetchedAtom = atom<boolean>(false); // Track if we've attempted to fetch user

// Derived atom to handle user fetching
export const userFetchAtom = atom(
  (get) => get(userFetchingAtom),
  async (get, set) => {
    // Skip if we're on server side
    if (typeof window === 'undefined') {
      set(currentUserAtom, createEmptyUser());
      set(userLoadingAtom, false);
      return;
    }

    // Prevent multiple simultaneous fetches
    if (get(userFetchingAtom)) {
      return;
    }

    // Set loading and fetching states
    set(userFetchingAtom, true);
    set(userLoadingAtom, true);

    try {
      const res = await getUser();
      if (!res.success || !res.data) {
        set(currentUserAtom, createEmptyUser());
      } else {
        const user = res.data as iUser;
        set(currentUserAtom, user);
      }
    } catch (error) {
      console.error('Client-side getUser error:', error);
      set(currentUserAtom, createEmptyUser());
    } finally {
      set(userLoadingAtom, false);
      set(userFetchingAtom, false);
      set(userFetchedAtom, true);
    }
  }
);

export const avatarTriggerAtom = atom<string | null>(null);

// export const AllNotificationsAtom = atom(async (get) => {
// const user = await get(currentUserAtom);
// const { userNotifications, ownerNotifications } = await getNotifications(user.id);
// return { userNotifications, ownerNotifications };
// })
export const badWordsAtom = atom<string[]>([]);

// Helper function to check if user is in loading/empty state (useful for skeletons)
export function isUserLoading(user: iUser | undefined, loading: boolean = false): boolean {
  return loading || !user || user.id === '' || user.clerkUserId === '';
}

// Helper function to check if user has real data
export function hasUserData(user: iUser | undefined): boolean {
  return !!(user && user.id && user.clerkUserId && user.userName);
}
