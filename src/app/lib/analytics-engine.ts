// src/app/lib/analytics-engine.ts
import { prisma } from '../util/prismaClient';

export async function calculateBusinessGrowthMetrics(businessId: string) {
  try {
    console.log(`Starting analytics calculation for business: ${businessId}`);
    
    // 1. Fetch Data
    const businessWithProductsAndReviews = await prisma.business.findUnique({
      where: { id: businessId },
      include: {
        products: {
          include: {
            reviews: true,
          },
        },
      },
    });

    if (!businessWithProductsAndReviews) {
      console.error(`Business with id ${businessId} not found.`);
      throw new Error(`Business with id ${businessId} not found`);
    }

    const { products } = businessWithProductsAndReviews;
    const allReviews = products.flatMap((p: any) => p.reviews);
    
    console.log(`Found ${products.length} products with ${allReviews.length} total reviews for business ${businessId}`);

    // 2. Calculate Average Response Time
    let averageReviewResponseTime: number | null = null;
    try {
      const respondedReviews = allReviews.filter((r: any) => r.ownerRespondedAt);
      if (respondedReviews.length > 0) {
        const totalResponseTime = respondedReviews.reduce((acc: any, review: any) => {
          if (!review.ownerRespondedAt || !review.createdDate) {
            console.warn(`Invalid date data for review in business ${businessId}`);
            return acc;
          }
          const responseTime =
            review.ownerRespondedAt.getTime() - review.createdDate.getTime();
          return acc + responseTime;
        }, 0);
        averageReviewResponseTime =
          totalResponseTime / respondedReviews.length / (1000 * 60 * 60); // in hours
      }
      console.log(`Calculated average response time: ${averageReviewResponseTime} hours`);
    } catch (error) {
      console.error(`Error calculating average response time for business ${businessId}:`, error);
      averageReviewResponseTime = null;
    }

    // 3. Calculate Negative Review Response Rate
    let negativeReviewResponseRate: number | null = null;
    try {
      const negativeReviews = allReviews.filter((r: any) => r.rating <= 2);
      if (negativeReviews.length > 0) {
        const respondedNegativeReviews = negativeReviews.filter(
          (r: any) => r.ownerRespondedAt,
        ).length;
        negativeReviewResponseRate =
          (respondedNegativeReviews / negativeReviews.length) * 100;
      }
      console.log(`Calculated negative review response rate: ${negativeReviewResponseRate}%`);
    } catch (error) {
      console.error(`Error calculating negative review response rate for business ${businessId}:`, error);
      negativeReviewResponseRate = null;
    }

    // 4. Calculate Content Freshness Score
    let productContentFreshnessScore: number | null = null;
    try {
      if (products.length > 0) {
        const now = new Date();
        const totalScore = products.reduce((acc: any, product: any) => {
          let descriptionScore = 0;
          if (product.descriptionLastUpdatedAt) {
            try {
              const diffDays =
                (now.getTime() - product.descriptionLastUpdatedAt.getTime()) /
                (1000 * 60 * 60 * 24);
              if (diffDays <= 7) {
                descriptionScore = 100;
              } else if (diffDays <= 30) {
                descriptionScore = 50;
              }
            } catch (error) {
              console.warn(`Invalid descriptionLastUpdatedAt for product ${product.id} in business ${businessId}`);
            }
          }

          let imageScore = 0;
          if (product.imagesLastUpdatedAt) {
            try {
              const diffDays =
                (now.getTime() - product.imagesLastUpdatedAt.getTime()) /
                (1000 * 60 * 60 * 24);
              if (diffDays <= 7) {
                imageScore = 100;
              } else if (diffDays <= 30) {
                imageScore = 50;
              }
            } catch (error) {
              console.warn(`Invalid imagesLastUpdatedAt for product ${product.id} in business ${businessId}`);
            }
          }
          return acc + (descriptionScore + imageScore) / 2;
        }, 0);
        productContentFreshnessScore = totalScore / products.length;
      }
      console.log(`Calculated content freshness score: ${productContentFreshnessScore}`);
    } catch (error) {
      console.error(`Error calculating content freshness score for business ${businessId}:`, error);
      productContentFreshnessScore = null;
    }

    // 5. Update Database
    try {
      await prisma.businessAnalytics.upsert({
        where: { businessId },
        create: {
          businessId,
          averageReviewResponseTime,
          negativeReviewResponseRate,
          productContentFreshnessScore,
          lastCalculated: new Date(),
        },
        update: {
          averageReviewResponseTime,
          negativeReviewResponseRate,
          productContentFreshnessScore,
          lastCalculated: new Date(),
        },
      });
      console.log(`Successfully updated analytics for business ${businessId}`);
    } catch (error) {
      console.error(`Error updating database for business ${businessId}:`, error);
      throw new Error(`Failed to update analytics for business ${businessId}: ${error}`);
    }
    
  } catch (error) {
    console.error(`Error calculating business growth metrics for business ${businessId}:`, error);
    throw error; // Re-throw to be handled by the calling function
  }
}
