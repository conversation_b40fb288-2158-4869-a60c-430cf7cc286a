import Redis, { RedisOptions } from 'ioredis';

interface RedisConfig {
  host?: string;
  port?: number;
  password?: string;
  tlsEnabled?: boolean;
}

class RedisService {
  private static instance: RedisService;
  private client: Redis;
  private healthy: boolean = true;
  private lastHealthCheck: number = 0;
  private readonly HEALTH_CHECK_INTERVAL: number;
  private readonly COMMAND_TIMEOUT: number;
  private readonly SET_TIMEOUT: number;

  private constructor() {
    this.HEALTH_CHECK_INTERVAL = parseInt(process.env.REDIS_HEALTH_CHECK_INTERVAL || '30000');
    this.COMMAND_TIMEOUT = parseInt(process.env.REDIS_COMMAND_TIMEOUT || '5000');
    this.SET_TIMEOUT = parseInt(process.env.REDIS_SET_TIMEOUT || '2000');
    this.client = this.createRedisClient();
    this.setupEventHandlers();
  }

  public static getInstance(): RedisService {
    if (!RedisService.instance) {
      RedisService.instance = new RedisService();
    }
    return RedisService.instance;
  }

  private createRedisClient(): Redis {
    const config = this.getRedisConfig();
    
    const options: RedisOptions = {
      host: config.host,
      port: config.port,
      password: config.password,
      connectTimeout: 30000,
      commandTimeout: 10000,
      lazyConnect: true,
      maxRetriesPerRequest: 3,
      enableOfflineQueue: false,
      retryStrategy: (times) => {
        const delay = Math.min(times * 100, 2000); // Exponential backoff
        console.log(`Redis connection failed. Retrying in ${delay}ms (attempt ${times})...`);
        return delay;
      },
      family: 4,
      keepAlive: 30000,
      showFriendlyErrorStack: true
    };

    if (config.tlsEnabled) {
      options.tls = {};
    }

    this.logConfiguration(config, options);
    return new Redis(options);
  }

  private getRedisConfig(): RedisConfig {
    return {
      host: process.env.UPSTASH_REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      tlsEnabled: process.env.REDIS_TLS_ENABLED === 'true'
    };
  }

  private logConfiguration(config: RedisConfig, options: RedisOptions): void {
    console.log('Redis Configuration:');
    console.log('Host:', config.host);
    console.log('Port:', config.port);
    console.log('Password:', config.password ? '[REDACTED]' : 'undefined');
    console.log('TLS:', config.tlsEnabled ? 'enabled' : 'disabled');
    
    const redactedConnectionString = `redis${config.tlsEnabled ? 's' : ''}://${config.password ? ':REDACTED@' : ''}${config.host}:${config.port}`;
    console.log('Connection String:', redactedConnectionString);
  }

  private setupEventHandlers(): void {
    this.client.on('error', (err: any) => {
      console.error('Redis connection error:', {
        message: err?.message,
        code: err?.code,
        errno: err?.errno,
        syscall: err?.syscall,
        address: err?.address,
        port: err?.port
      });
      this.healthy = false;
    });

    this.client.on('connect', () => {
      console.log('Redis connected successfully to', this.client.options.host + ':' + this.client.options.port);
      this.healthy = true;
    });

    this.client.on('ready', () => {
      console.log('Redis client ready');
      this.healthy = true;
    });

    this.client.on('close', () => {
      console.log('Redis connection closed');
      this.healthy = false;
    });

    this.client.on('reconnecting', () => {
      console.log('Redis reconnecting...');
    });

    this.client.on('end', () => {
      console.log('Redis connection ended');
      this.healthy = false;
    });
  }

  public async isHealthy(): Promise<boolean> {
    const now = Date.now();
    if (now - this.lastHealthCheck < this.HEALTH_CHECK_INTERVAL) {
      return this.healthy;
    }

    try {
      console.log('Redis health check - current status:', this.client.status);

      if (this.client.status !== 'ready') {
        console.log('Redis not ready, attempting to connect...');
        await this.client.connect().catch(err => {
            console.error("Failed to connect during health check", err);
            throw err;
        });
        console.log('Redis connection attempt completed, status:', this.client.status);
      }

      console.log('Sending ping to Redis...');
      const pingPromise = this.client.ping();
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error(`Health check timeout after ${this.COMMAND_TIMEOUT}ms`)), this.COMMAND_TIMEOUT)
      );

      const result = await Promise.race([pingPromise, timeoutPromise]);
      console.log('Redis ping result:', result);
      this.healthy = true;
      this.lastHealthCheck = now;
      return true;
    } catch (error: any) {
      console.error('Redis health check failed:', {
        message: error?.message,
        code: error?.code,
        errno: error?.errno,
        syscall: error?.syscall,
        address: error?.address,
        port: error?.port,
        stack: error?.stack
      });
      this.healthy = false;
      this.lastHealthCheck = now;
      return false;
    }
  }

  public async get(key: string): Promise<string | null> {
    try {
      const getPromise = this.client.get(key);
      const timeoutPromise = new Promise<null>((_, reject) =>
        setTimeout(() => reject(new Error('Redis get timeout')), this.COMMAND_TIMEOUT)
      );

      const result = await Promise.race([getPromise, timeoutPromise]);
      if (result) {
        console.log(`Redis CACHE HIT for key: ${key}`);
      } else {
        console.log(`Redis CACHE MISS for key: ${key}`);
      }
      return result;
    } catch (error) {
      console.warn(`Redis get failed for key ${key}:`, error);
      this.healthy = false;
      return null;
    }
  }

  public async set(key: string, value: string, ttl: number): Promise<void> {
    console.log(`🔍 Attempting to set cache key: ${key}`);

    if (!await this.isHealthy()) {
      console.warn(`❌ Redis unhealthy - skipping cache set for key: ${key}`);
      return;
    }

    try {
      console.log(`🔍 Setting Redis key with TTL ${ttl}: ${key}`);
      const setPromise = this.client.setex(key, ttl, value);
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Redis set timeout')), this.SET_TIMEOUT)
      );

      await Promise.race([setPromise, timeoutPromise]);
      console.log(`✅ Successfully set cache key: ${key}`);
    } catch (error) {
      console.error(`❌ Redis set failed for key ${key}:`, error);
    }
  }

  public async del(key: string): Promise<void> {
    if (!(await this.isHealthy())) {
      return;
    }

    try {
      const delPromise = this.client.del(key);
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Redis del timeout')), this.SET_TIMEOUT)
      );

      await Promise.race([delPromise, timeoutPromise]);
      console.log(`Redis key deleted: ${key}`);
    } catch (error) {
      console.warn(`Redis del failed for key ${key}:`, error);
    }
  }

  public async getFromCache<T>(key: string): Promise<T | null> {
    try {
      const data = await this.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn(`Redis cache get failed (non-fatal): ${error}`);
      return null;
    }
  }

  public async setInCache(key: string, value: any, ttl: number): Promise<void> {
    try {
      await this.set(key, JSON.stringify(value), ttl);
    } catch (error) {
      console.warn(`Redis cache set failed (non-fatal): ${error}`);
    }
  }

  // Advanced operations for complex use cases
  public async keys(pattern: string): Promise<string[]> {
    return new Promise((resolve, reject) => {
        const stream = this.client.scanStream({
            match: pattern,
        });
        const keys: string[] = [];
        stream.on('data', (resultKeys) => {
            keys.push(...resultKeys);
        });
        stream.on('end', () => {
            resolve(keys);
        });
        stream.on('error', (err) => {
            console.warn(`Redis scan failed for pattern ${pattern}:`, err);
            reject(err);
        });
    });
  }

  public async incr(key: string): Promise<number> {
    try {
      return await this.client.incr(key);
    } catch (error) {
      console.warn(`Redis incr failed for key ${key}:`, error);
      return 0;
    }
  }

  public async expire(key: string, seconds: number): Promise<void> {
    try {
      await this.client.expire(key, seconds);
    } catch (error) {
      console.warn(`Redis expire failed for key ${key}:`, error);
    }
  }

  // For testing purposes - allows graceful shutdown
  public async disconnect(): Promise<void> {
    await this.client.disconnect();
  }

  // Get raw client for advanced operations (use sparingly)
  public getClient(): Redis {
    return this.client;
  }
}

// Export singleton instance
export const redisService = RedisService.getInstance();
export default redisService;