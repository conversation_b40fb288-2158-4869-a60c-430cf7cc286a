'use server';

import { prisma } from "@/app/util/prismaClient";
import { iReview } from "@/app/util/Interfaces";

export async function getReviewsByProductId(productId: string): Promise<iReview[]> {
    console.log(`[Review Actions] Fetching reviews for product ID: ${productId}`);

    try {
        const reviews = await prisma.review.findMany({
            where: {
                productId: productId,
            },
            include: {
                user: true,
                product: true,
                comments: {
                    include: {
                        user: true,
                    },
                },
                likedBy: true,
            },
            orderBy: {
                createdDate: 'desc',
            },
        });

        console.log(`[Review Actions] Found ${reviews.length} reviews for product ID: ${productId}`);
        return reviews as iReview[];
    } catch (error) {
        console.error(`[Review Actions] Error fetching reviews for product ID: ${productId}`, error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
} 