'use server';

import { prisma } from "@/app/util/prismaClient";
import { iProduct, iReview } from "@/app/util/Interfaces";
import { ApiResponse } from "@/app/util/serverFunctions";

interface ProductAndReviews {
    product: iProduct | null;
    reviews: iReview[];
}

export async function fetchProductAndReviews(productId: string): Promise<ApiResponse<ProductAndReviews>> {
    try {
        // Fetch product data
        const product = await prisma.product.findUnique({
            where: { id: productId },
            include: {
                business: true,
            },
        });

        // Fetch reviews
        const reviews = await prisma.review.findMany({
            where: {
                productId: productId,
                isPublic: true,
                isDeleted: false,
            },
            include: {
                user: true,
                product: true,
                comments: {
                    include: {
                        user: true,
                    },
                },
                likedBy: true,
            },
            orderBy: {
                createdDate: 'desc',
            },
        });

        // Increment view count only if product exists
        if (product) {
            await prisma.product.update({
                where: { id: productId },
                data: {
                    viewCount: {
                        increment: 1,
                    },
                },
            });
        }

        return {
            success: true,
            data: {
                product: product as iProduct,
                reviews: reviews as iReview[]
            }
        };
    } catch (error) {
        console.error(`[Reviews Page Action] Error fetching data for product ID: ${productId}`, error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to fetch product and reviews'
        };
    } finally {
        await prisma.$disconnect();
    }
} 