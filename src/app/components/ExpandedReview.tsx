"use client";
import {
  useEffect,
  useMemo,
  useState,
  useCallback,
  lazy,
  Suspense,
} from "react";
import { useAtom } from "jotai";
import { useRouter } from "next/navigation";
import { useAuth } from "@clerk/nextjs";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import ReviewCard from "./ReviewCard";
import { currentReviewAtom, currentUserAtom } from "../store/store";
import { iReview, iComment, iUser, ApiResponse } from "../util/Interfaces";
import {
  createCommentOnReview,
  createReplyOnComment,
  deleteComment,
  editComment,
  getReviews,
} from "../util/serverFunctions";
import LoadingSpinner from "./LoadingSpinner";
import CommentForm from "./CommentForm";
import DisplayError from "./DisplayError";
import useScrollToComment from "../util/UseScrollToComment";

const CommentList = lazy(() => import("./CommentList"));

const ExpandedReview = ({
  reviewId,
  productId,
  cId,
}: {
  reviewId: string;
  productId: string;
  cId: string;
}) => {
  const isCommentLoaded = useScrollToComment(cId, {
    maxAttempts: 10,
    intervalDuration: 500,
  });
  const { userId, isLoaded, isSignedIn } = useAuth();
  const queryClient = useQueryClient();
  const [reviewAtom] = useAtom(currentReviewAtom);
  const [isOpen, setIsOpen] = useState(true);
  const [textAreaValue, setTextAreaValue] = useState("");
  const [currentUser] = useAtom(currentUserAtom);
  const router = useRouter();
  const clerkUserId = userId as string;
  const [allReviews, setAllReviews] = useState<iReview[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [comment, setComment] = useState<iComment>({
    reviewId: "",
    body: "",
    createdDate: new Date(),
    user: {} as iUser,
    review: {} as iReview,
    userId: "",
    isDeleted: false,
    upvotes: 0,
    downvotes: 0,
  });

  const commentMutation = useMutation({
    mutationFn: async (comment: iComment) => {
      const data = createCommentOnReview(comment);
      toast.promise(data, {
        loading: "Loading...",
        success: () => "Comment saved successfully!",
        error: "Error saving comment",
      });
    },
    onMutate: (newData: iComment) => {
      queryClient.setQueryData<iReview | undefined>(
        ["review", reviewId],
        (oldData) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            comments: [
              ...(oldData.comments || []),
              { ...newData, user: currentUser, isDeleted: false },
            ],
          };
        }
      );
    },
    onError: (error: Error) => {
      <DisplayError error={error.message} />;
      console.error(error);
    },
  });

  const replyMutation = useMutation({
    mutationFn: async (reply: { parentId: string; body: string }) => {
      const data = await createReplyOnComment({
        ...comment,
        ...reply,
        id: Date.now().toString(),
        user: currentUser,
      });
      return data;
    },
  });

  const handleCommentSubmit = useCallback(
    async (newTextAreaValue: string) => {
      if (isLoaded && !isSignedIn) {
        router.push("https://accounts.reviewit.gy/sign-in");
        return;
      }
      setTextAreaValue(newTextAreaValue);
      setIsOpen(!isOpen);
      commentMutation.mutate({ ...comment, body: newTextAreaValue });
    },
    [isLoaded, isSignedIn, router, isOpen, commentMutation, comment]
  );

  const handleReply = useCallback(
    async (parentId: string, body: string) => {
      try {
        await replyMutation.mutateAsync({ parentId, body });
      } catch (error) {
        console.error("Failed to add reply:", error);
      }
    },
    [replyMutation]
  );

  const handleEdit = async (commentId: string, body: string) => {
    editComment(commentId, body);
  };

  const handleDelete = async (commentId: string) => {
    const deleteResponse = await deleteComment(commentId);
    if (deleteResponse.success) {
      toast.success("Comment successfully deleted!");
    } else {
      toast.error(deleteResponse.error || "Failed to delete comment");
    }
  };

  const { data, isPending, isError, error } = useQuery({
    queryKey: ["review", reviewId],
    queryFn: async () => {
      setIsLoading(true);
      if (reviewAtom !== null) {
        setIsLoading(false);
        return reviewAtom;
      }
      const response = await getReviews(productId);
      if (!response.success || !Array.isArray(response.data)) {
        throw new Error(response.error || 'Failed to fetch reviews');
      }
      setIsLoading(false);
      setAllReviews(response.data);
      const foundReview = response.data.find(
        (review: iReview) => review.id === reviewId
      );
      if (!foundReview) {
        throw new Error('Review not found');
      }
      return foundReview;
    },
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (data) {
      setComment((prevComment) => ({
        ...prevComment,
        reviewId: reviewId,
        body: textAreaValue,
        createdDate: new Date(),
        user: currentUser,
        review: reviewAtom || data,
        userId: currentUser?.id || "",
      }));
    }
  }, [data, textAreaValue, currentUser, reviewAtom, reviewId]);

  const sortedComments = useMemo(() => {
    return (
      data?.comments
        ?.slice()
        .sort(
          (a: iComment, b: iComment) =>
            new Date(b.createdDate!).valueOf() -
            new Date(a.createdDate!).valueOf()
        ) || []
    );
  }, [data?.comments]);

  const reviewData = useMemo(() => {
    return reviewAtom || data;
  }, [reviewAtom, data]);

  useEffect(() => {
    if (isCommentLoaded) {
      console.log("Comment has been scrolled to");
    }
  }, [isCommentLoaded]);

  if (isPending || isLoading) return <LoadingSpinner />;
  if (isError) return (
    <div className="flex flex-col items-center justify-center p-8">
      <h2 className="text-xl font-semibold text-red-600 mb-2">Error Loading Review</h2>
      <p className="text-gray-600">{error instanceof Error ? error.message : 'An error occurred while loading the review'}</p>
    </div>
  );
  if (!reviewData) return (
    <div className="flex flex-col items-center justify-center p-8">
      <h2 className="text-xl font-semibold text-gray-600 mb-2">Review Not Found</h2>
      <p className="text-gray-500">The requested review could not be found.</p>
    </div>
  );

  return (
    <div className="flex flex-col w-full p-2 md:px-36 sm:pt-8 bg-myTheme-lightbg ">
      {/* Combined Product and Review Card - Product section removed */}
      <div className="bg-white rounded-lg shadow-md w-full">
        {/* Product Information Section - REMOVED */}
        {/* <div className="p-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold mb-4 text-gray-700">
            Product Information
          </h2>
          <ProductCard
            product={reviewData?.product!}
            options={productCardOptions}
            reviews={allReviews}
            currentUserId={userId ? userId : null}
          />
        </div> */}

        {/* Review, CommentList, and CommentForm Section - No separate card, just a padded section */}
        <div className="p-4">
          <h2 className="text-xl font-semibold mb-4 text-gray-700">Review</h2>
          <div className="bg-white rounded-lg shadow-md border border-gray-100 p-6 mb-8">
            <ReviewCard review={reviewData} showFullContent={true} />
          </div>

          {/* Comment List Section - Now before CommentForm */}
          <div className="mt-8">
            <h3 className="text-lg font-semibold mb-4 text-gray-700">
              Comments ({sortedComments.length}) {/* Ensure count is always shown */}
            </h3>
            {sortedComments.length > 0 ? (
              <Suspense fallback={<LoadingSpinner />}>
                <CommentList
                  comments={sortedComments}
                  currentUser={currentUser}
                  onReply={handleReply}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  clerkUserId={clerkUserId}
                  product={reviewData?.product || undefined}
                />
              </Suspense>
            ) : (
              <div className="text-gray-500 text-center py-4">
                No comments yet. Be the first to comment!
              </div>
            )}
          </div>

          {/* CommentForm - Now after CommentList */}
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-700">Leave a comment</h3>
            <CommentForm
              isOpen={isOpen}
              onClose={setIsOpen}
              onSubmit={handleCommentSubmit}
              product={reviewData?.product || undefined}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpandedReview;
