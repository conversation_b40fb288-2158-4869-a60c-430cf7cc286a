"use client";
import { iReview, iProduct } from "../util/Interfaces";
import { useEffect, useState } from "react";
import { useUser } from "@clerk/nextjs";
import DisplayError from "@/app/components/DisplayError";
import { useQuery } from "@tanstack/react-query";
import { getProduct } from "../util/serverFunctions";
import LoadingSpinner from "./LoadingSpinner";
import { useAtom } from "jotai";
import { allProductsAtom } from "../store/store";
import { useRouter, useSearchParams } from "next/navigation";

// Import the extracted components
import OwnershipMessage from "./review/OwnershipMessage";
import ReviewFormHeader from "./review/ReviewFormHeader";
import ReviewRatingSection from "./review/ReviewRatingSection";
import ReviewTitleSection from "./review/ReviewTitleSection";
import ReviewDateSection from "./review/ReviewDateSection";
import ReviewBodySection from "./review/ReviewBodySection";
import ReviewMediaSection from "./review/ReviewMediaSection";
import ReviewSubmitSection from "./review/ReviewSubmitSection";
import ProductCardSection from "./review/ProductCardSection";

const ReviewForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const searchRating = searchParams.get("rating");
  const id = searchParams.get("id")!;
  const [disabled, setDisabled] = useState(false);
  const { user, isLoaded, isSignedIn } = useUser();
  const [linksArray, setLinksArray] = useState<string[]>([]);
  const [videosArray, setVideosArray] = useState<string[]>([]);
  const [url, setUrl] = useState("");
  const [allUploaded, setAllUploaded] = useState(false);
  // make sure there is an int in searchRating and make sure its between 1 and 5
  const [rating, setRating] = useState(
    searchRating ? parseInt(searchRating) : 2,
  ); // Initial value
  const [startDate, setStartDate] = useState(new Date());
  const [error, setError] = useState<string | null>(null);
  const [reviewEligibility, setReviewEligibility] = useState({
    isEligible: true,
    message: "",
    nextEligibleDate: null,
    daysRemaining: 0
  });
  const [isCheckingEligibility, setIsCheckingEligibility] = useState(false);
  const [reviewData, setReviewData] = useState<iReview>({
    body: "",
    createdDate: new Date(),
    helpfulVotes: 0,
    unhelpfulVotes: 0,
    rating: rating,
    title: "",
    productId: id,
    userId: user?.publicMetadata?.id as string || '',
    isVerified: false,
    verifiedBy: undefined,
    isPublic: true,
    images: linksArray,
    videos: videosArray,
    links: linksArray,
    comments: [],
    createdBy: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() : '',
    isDeleted: false,
    likedBy: [],
    voteCount: null,
  });

  const [products, setProducts] = useAtom(allProductsAtom);
  const productCardOptions = {
    showLatestReview: false,
    size: "rating-md",
    showWriteReview: false,
    showClaimThisProduct: true,
  };

  // Move useQuery to the top level, before any conditional returns
  const { data: product, isLoading } = useQuery({
    queryKey: ['product', id],
    queryFn: async () => {
      const response = await getProduct(id);
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to fetch product');
      }
      return response.data;
    },
    enabled: !!id,
  });

  // All useEffect hooks must be together at the top level
  useEffect(() => {
    if (linksArray.length > 0) {
      setReviewData(
        (prevData): iReview => ({
          ...prevData,
          images: linksArray,
        }),
      );
    }
  }, [linksArray]);

  // Check if the user is eligible to write a review
  useEffect(() => {
    const checkEligibility = async () => {
      // Make sure we have a valid user ID from metadata
      const userId = user?.publicMetadata?.id as string | undefined;
      if (!userId || !id) return;

      try {
        setIsCheckingEligibility(true);
        const response = await fetch('/api/check-review-eligibility', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            userId,
            productId: id
          })
        });

        if (!response.ok) {
          console.error("Error checking eligibility:", response.statusText);
          return;
        }

        const eligibilityData = await response.json();
        setReviewEligibility(eligibilityData);

        // If not eligible, set the error message
        if (!eligibilityData.isEligible) {
          setError(eligibilityData.message);
        }
      } catch (error) {
        console.error("Error checking review eligibility:", error);
      } finally {
        setIsCheckingEligibility(false);
      }
    };

    if (isLoaded && isSignedIn && user?.publicMetadata?.id && id) {
      checkEligibility();
    }
  }, [isLoaded, isSignedIn, user?.publicMetadata?.id, id, setError]);

  // Add authentication check
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('https://accounts.reviewit.gy/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  // Update user ID in reviewData when it becomes available
  useEffect(() => {
    if (user?.publicMetadata?.id) {
      setReviewData(prevData => ({
        ...prevData,
        userId: user.publicMetadata.id as string,
        createdBy: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() : ''
      }));
    }
  }, [user]);

  const handleEditorValue = (value: string) => {
    if (value === "" || value === "<p></p>") {
      setReviewData((prevData): iReview => ({ ...prevData, body: "" }));
      return;
    }
    setReviewData((prevData): iReview => ({ ...prevData, body: value }));
    setError((prevError) => (prevError = null));
  };

  const ratingChanged = (newRating: number) => {
    setRating(newRating);
    function addRating(rating: number) {
      // not sure if this is necessary, but it should be the safest way. test before making simpler
      setReviewData((prevData): iReview => ({ ...prevData, rating: rating }));
    }
    addRating(newRating);
  };

  const sendToServer = async () => {
    try {
      console.log("here is reviewData", reviewData);
      const response = await fetch(`/api/create/review`, {
        method: "POST",
        body: JSON.stringify(reviewData),
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'include',
      });

      const responseData = await response.json();

      if (response.ok) {
        router.push(`/reviews?id=${id}`);
      } else {
        console.log(responseData);
        // Display error message from the server
        setError(responseData.data || "Failed to submit review");
        setDisabled(false);
      }
    } catch (error) {
      let err = error as Error;
      setError((prevError) => (prevError = err.message));
      console.log(err);
      setDisabled(false);
    }
  };

  // Show loading state while authentication is being checked
  if (!isLoaded || isCheckingEligibility) {
    return <LoadingSpinner />;
  }

  // Don't render the form if not signed in
  if (!isSignedIn) {
    return null;
  }

  // Don't render the form if not eligible
  if (!reviewEligibility.isEligible) {
    return (
      <div className="pt-8 flex flex-col h-full w-full sm:w-3/4 items-center bg-myTheme-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold mb-4">Review Not Available</h2>
        <p className="text-red-500 font-medium text-center mb-2">{reviewEligibility.message}</p>
        {reviewEligibility.nextEligibleDate && (
          <p className="text-sm mb-4 text-center">
            You can review this product again in {reviewEligibility.daysRemaining} day{reviewEligibility.daysRemaining !== 1 ? 's' : ''}
            (after {new Date(reviewEligibility.nextEligibleDate).toLocaleDateString()})
          </p>
        )}
        <button
          onClick={() => router.push(`/reviews?id=${id}`)}
          className="px-4 py-2 bg-myTheme-primary text-white rounded-md hover:bg-myTheme-secondary"
        >
          Back to Reviews
        </button>
      </div>
    );
  }

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;

    setReviewData((prevData): iReview => {
      if (name === "videoUrl") {
        return {
          ...prevData,
          videos:
            prevData.videos.length > 0
              ? [value, ...prevData.videos.slice(1)] // Update if array has items
              : [value], // Create new array if empty
        };
      } else {
        // Handle other fields normally
        return { ...prevData, [name]: value };
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    setDisabled(true);
    e.preventDefault();
    //disable the form form
    e.currentTarget.disabled = true;

    e.currentTarget.checkValidity();
    if (!e.currentTarget.checkValidity()) {
      setError("Please fill out all fields");
      return;
    }
    if (
      (reviewData.body === "" || reviewData.body === null) &&
      linksArray.length === 0 &&
      reviewData.videos.length === 0
    ) {
      setError((prevError) => (prevError = "Review body is empty"));
      setDisabled(false);
      return;
    }
    // INFO: enable this to send to server
    await sendToServer();
    setDisabled(false);
  };

  if (isLoading) return <LoadingSpinner />;
  if (!product)
    return (
      <p>
        fetch error - cannot give more details cause product variable was taken
      </p>
    );

  const amITheOwner = product.business?.ownerId === user?.id;
  if (amITheOwner) {
    return <OwnershipMessage />;
  }

  return (
    <div className="pt-8 flex flex-col h-full w-full sm:w-3/4 items-center bg-myTheme-white rounded-lg shadow-md">
      <ReviewFormHeader />
      <form
        onSubmit={handleSubmit}
        className="flex flex-col w-full space-y-6 bg-myTheme-white rounded-md p-6 overflow-y-auto"
      >
        {product && (
          <ProductCardSection
            product={product}
            productCardOptions={productCardOptions}
            userId={user?.id ? user.id : null}
          />
        )}

        <ReviewRatingSection
          rating={rating}
          ratingChanged={ratingChanged}
        />

        <ReviewTitleSection
          handleChange={handleChange}
        />

        <ReviewDateSection
          startDate={startDate}
          setStartDate={setStartDate}
          handleChange={handleChange}
        />

        <ReviewBodySection
          onEditorValue={handleEditorValue}
        />

        <ReviewMediaSection
          url={url}
          setUrl={setUrl}
          handleChange={handleChange}
          setLinksArray={setLinksArray}
          setAllUploaded={setAllUploaded}
          allUploaded={allUploaded}
        />

        <ReviewSubmitSection
          disabled={disabled}
          reviewData={reviewData}
          allUploaded={allUploaded}
        />
      </form>
      <DisplayError error={error} />
    </div>
  );
};

export default ReviewForm;
