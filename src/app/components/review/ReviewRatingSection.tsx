import React from 'react';
import { InteractiveRating } from '../RatingSystem';

interface ReviewRatingSectionProps {
    rating: number;
    ratingChanged: (newRating: number) => void;
}

const ReviewRatingSection: React.FC<ReviewRatingSectionProps> = ({ rating, ratingChanged }) => {
    return (
        <div className="space-y-2 border-b pb-4">
            <label
                htmlFor="rating"
                className="block text-lg font-semibold text-myTheme-primary"
            >
                Rate your experience
            </label>
            <InteractiveRating
                name="rating"
                rating={rating}
                onRatingChange={ratingChanged}
                size="lg"
            />
        </div>
    );
};

export default ReviewRatingSection;