import React from 'react';
import VideoEmbed from '../VideoEmbed';
import MultiFileUpload from '../fileUpload/MultiFileUpload';

interface ReviewMediaSectionProps {
    url: string;
    setUrl: React.Dispatch<React.SetStateAction<string>>;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
    setLinksArray: React.Dispatch<React.SetStateAction<string[]>>;
    setAllUploaded: React.Dispatch<React.SetStateAction<boolean>>;
    allUploaded: boolean;
}

const ReviewMediaSection: React.FC<ReviewMediaSectionProps> = ({
    url,
    setUrl,
    handleChange,
    setLinksArray,
    setAllUploaded,
    allUploaded
}) => {
    return (
        <>
            <div className="container mx-auto px-4">
                <h1 className="text-2xl font-bold mb-4">Embed Video</h1>
                <input
                    type="text"
                    name="videoUrl"
                    value={url}
                    onChange={(e) => {
                        setUrl(e.target.value);
                        handleChange(e);
                    }}
                    placeholder="Enter YouTube, Instagram, or TikTok video URL"
                    className="w-full p-2 border rounded mb-4 bg-white"
                />
                <VideoEmbed url={url} />
            </div>
            <div className="flex justify-center items-center w-full">
                <MultiFileUpload
                    setLinksArray={setLinksArray}
                    setAllUploaded={setAllUploaded}
                    allUploaded={allUploaded}
                />
            </div>
        </>
    );
};

export default ReviewMediaSection; 