import React from 'react';
import EditorPreview from '../EditorPreview';
import { iReview } from '../../util/Interfaces';

interface ReviewSubmitSectionProps {
    disabled: boolean;
    reviewData: iReview;
    allUploaded: boolean;
}

const ReviewSubmitSection: React.FC<ReviewSubmitSectionProps> = ({
    disabled,
    reviewData,
    allUploaded
}) => {
    return (
        <div className="flex gap-2 h-full">
            {!disabled && reviewData.body !== "" && (
                <div className="bg-gray-100 rounded-md h-full">
                    <EditorPreview reviewData={reviewData} />
                </div>
            )}
            {allUploaded && (
                <button
                    disabled={disabled}
                    type="submit"
                    className="w-full bg-myTheme-primary hover:bg-myTheme-secondary text-white font-semibold py-3 px-6 rounded-md transition duration-300 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Submit Review
                </button>
            )}
        </div>
    );
};

export default ReviewSubmitSection; 