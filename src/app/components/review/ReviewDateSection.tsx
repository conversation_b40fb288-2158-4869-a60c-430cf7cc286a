import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface ReviewDateSectionProps {
    startDate: Date;
    setStartDate: (date: Date) => void;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const ReviewDateSection: React.FC<ReviewDateSectionProps> = ({
    startDate,
    setStartDate,
    handleChange
}) => {
    return (
        <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 space-y-2">
                <label
                    htmlFor="dateItHappened"
                    className="block text-lg font-semibold text-myTheme-primary"
                >
                    Date
                </label>
                <DatePicker
                    id="dateItHappened"
                    name="dateItHappened"
                    selected={startDate}
                    onChange={(date) => setStartDate(date!)}
                    className="z-50 w-full px-4 py-2 border border-gray-300 rounded-md bg-myTheme-white focus:ring-2 focus:ring-myTheme-primary focus:border-transparent"
                />
            </div>
            <div className="flex-1 space-y-2">
                <label
                    htmlFor="transactionNumber"
                    className="block text-lg font-semibold text-myTheme-primary"
                >
                    Receipt #
                </label>
                <input
                    placeholder="(optional)"
                    type="text"
                    id="transactionNumber"
                    name="transactionNumber"
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md bg-myTheme-white focus:ring-2 focus:ring-myTheme-primary focus:border-transparent"
                />
            </div>
        </div>
    );
};

export default ReviewDateSection; 