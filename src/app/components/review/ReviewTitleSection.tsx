import React from 'react';

interface ReviewTitleSectionProps {
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const ReviewTitleSection: React.FC<ReviewTitleSectionProps> = ({ handleChange }) => {
    return (
        <div className="space-y-2">
            <label
                htmlFor="title"
                className="block text-lg font-semibold text-myTheme-primary"
            >
                Title your experience
            </label>
            <input
                required
                placeholder="Be creative"
                type="text"
                id="title"
                name="title"
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-md bg-myTheme-white focus:ring-2 focus:ring-myTheme-primary focus:border-transparent"
            />
        </div>
    );
};

export default ReviewTitleSection; 