import React from 'react';
import Editor from '../Editor';

interface ReviewBodySectionProps {
    onEditorValue: (value: string) => void;
}

const ReviewBodySection: React.FC<ReviewBodySectionProps> = ({ onEditorValue }) => {
    return (
        <div className="space-y-2">
            <label
                htmlFor="experience"
                className="block text-lg font-semibold text-myTheme-primary"
            >
                Tell us more about your experience
            </label>
            <Editor onEditorValue={onEditorValue} />
        </div>
    );
};

export default ReviewBodySection; 