import Link from "next/link";
import Version from "./Version";
import { sideLinks } from "@/app/util/links";
import { IconContext } from "react-icons";
import { FiHome, FiPackage, FiGrid, FiPlusSquare, FiUser, FiHelpCircle, FiAlertTriangle, FiChevronDown, FiFileText, FiBarChart } from "react-icons/fi";
import { IoPricetagOutline } from "react-icons/io5";
import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import { useUser } from "@/app/hooks/useUser";
import { iUser } from "@/app/util/Interfaces";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Skeleton } from "./Skeleton";

interface SideLinksProps {
  onSideLinkClick: () => void;
}

const iconMap: { [key: string]: React.ElementType } = {
  home: FiHome,
  product: FiPackage,
  category: FiGrid,
  add: FiPlusSquare,
  user: FiUser,
  price: IoPricetagOutline,
  bug: FiAlertTriangle,
};

const SideLinks: React.FC<SideLinksProps> = ({ onSideLinkClick }) => {
  const pathname = usePathname();
  const [isBusinessesOpen, setIsBusinessesOpen] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const { user, isLoading: userIsLoading, isLoggedIn, isNotLoggedIn } = useUser();

  useEffect(() => {
    const checkAdminPermission = async () => {
      try {
        const response = await fetch('/api/admin/check-permission');
        const data = await response.json();
        setIsAdmin(data.isAdmin);
      } catch (error) {
        console.error('Error checking admin permission:', error);
        setIsAdmin(false);
      }
    };

    checkAdminPermission();
  }, []);

  return (
    <IconContext.Provider value={{ className: "w-5 h-5" }}>
      <div className="flex flex-col flex-1 justify-start items-start">
        <nav className="w-full space-y-1">
          {sideLinks.map((link, index: number) => {
            // Skip admin link if user is not admin
            if (link.adminOnly && !isAdmin) return null;

            const Icon = iconMap[link.icon] || FiHelpCircle;
            const isActive = pathname === link.link;

            if (link.name === 'My Businesses') {
              // Show skeleton only while actually loading user data
              if (userIsLoading) {
                return (
                  <div key={index} className="w-full px-4 py-3">
                    <div className="flex items-center space-x-3">
                      <Icon className="mr-3 flex-shrink-0 text-blue-500" />
                      <span className="animate-pulse text-blue-600 font-medium">Checking Biz...</span>
                    </div>
                  </div>
                );
              }

              // Hide completely if user is not logged in
              if (isNotLoggedIn) {
                return null;
              }

              return (
                <Collapsible
                  key={index}
                  className="w-full"
                  open={isBusinessesOpen}
                  onOpenChange={setIsBusinessesOpen}>
                  <CollapsibleTrigger
                    className={`
                      flex items-center px-4 py-3 text-sm font-medium rounded-lg w-full transition-all duration-200
                      ${isActive
                        ? 'bg-gray-100 text-myTheme-accent font-semibold'
                        : 'text-gray-700 hover:text-myTheme-accent hover:bg-gray-50'}
                    `}
                  >
                    <Icon className="mr-3 flex-shrink-0" />
                    <span>{link.name}</span>
                    <FiChevronDown className="ml-auto w-4 h-4 text-gray-400 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                    {isActive && (
                      <span className="ml-auto w-1.5 h-1.5 rounded-full bg-myTheme-accent"></span>
                    )}
                  </CollapsibleTrigger>
                  <CollapsibleContent className="ml-7 mt-1 space-y-1">
                    <Link
                      href={link.link}
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:text-myTheme-accent rounded-lg hover:bg-gray-50 w-full transition-all duration-200"
                      onClick={onSideLinkClick}
                    >
                      <IoPricetagOutline className="mr-3 w-4 h-4" />
                      My Businesses
                    </Link>
                    {user?.businesses && user.businesses.length > 0 && (
                      <Link
                        href="/owner-admin"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:text-myTheme-accent rounded-lg hover:bg-gray-50 w-full transition-all duration-200"
                        onClick={onSideLinkClick}
                      >
                        <FiBarChart className="mr-3 w-4 h-4" />
                        Advanced Dashboard
                      </Link>
                    )}
                    <Link
                      href="/claim-product"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:text-myTheme-accent rounded-lg hover:bg-gray-50 w-full transition-all duration-200"
                      onClick={onSideLinkClick}
                    >
                      <FiPackage className="mr-3 w-4 h-4" />
                      Claim Product
                    </Link>
                    <Link
                      href="/claims"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:text-myTheme-accent rounded-lg hover:bg-gray-50 w-full transition-all duration-200"
                      onClick={onSideLinkClick}
                    >
                      <FiFileText className="mr-3 w-4 h-4" />
                      My Claims
                    </Link>
                    <Link
                      href="/submit"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:text-myTheme-accent rounded-lg hover:bg-gray-50 w-full transition-all duration-200"
                      onClick={onSideLinkClick}
                    >
                      <FiPlusSquare className="mr-3 w-4 h-4" />
                      Add Product/Business
                    </Link>
                  </CollapsibleContent>
                </Collapsible>
              );
            }

            return (
              <Link
                key={index}
                href={link.link}
                onClick={onSideLinkClick}
                className={`
                  flex items-center px-4 py-3 text-sm font-medium rounded-lg w-full transition-all duration-200
                  ${isActive
                    ? 'bg-gray-100 text-myTheme-accent font-semibold'
                    : 'text-gray-700 hover:text-myTheme-accent hover:bg-gray-50'}
                `}
              >
                <Icon className="mr-3 flex-shrink-0" />
                <span>{link.name}</span>
                {isActive && (
                  <span className="ml-auto w-1.5 h-1.5 rounded-full bg-myTheme-accent"></span>
                )}
              </Link>
            );
          })}
        </nav>
        <div className="flex h-full flex-1 justify-center items-end w-full mt-8">
          <Version />
        </div>
      </div>
    </IconContext.Provider>
  );
};

export default SideLinks;
