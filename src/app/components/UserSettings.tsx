"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Download,
  Bell,
  Shield,
  Eye,
  Smartphone,
  Settings2,
  Check,
  AlertCircle,
} from "lucide-react";
import { iUser } from "../util/Interfaces";

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: "accepted" | "dismissed" }>;
}

interface UserSettingsProps {
  user: iUser;
  onUpdateUser: (updatedUser: Partial<iUser>) => void;
}

const UserSettings: React.FC<UserSettingsProps> = ({ user, onUpdateUser }) => {
  // PWA Installation States
  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null);
  const [isStandalone, setIsStandalone] = useState(false);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [installError, setInstallError] = useState<string | null>(null);
  const [installSuccess, setInstallSuccess] = useState(false);

  // User Preferences
  const [emailNotifications, setEmailNotifications] = useState(
    user.emailNotifications ?? true,
  );
  const [pushNotifications, setPushNotifications] = useState(
    user.pushNotifications ?? true,
  );
  const [profileVisibility, setProfileVisibility] = useState(
    user.profileVisibility ?? "public",
  );
  const [showEmail, setShowEmail] = useState(user.showEmail ?? false);

  useEffect(() => {
    const checkStandaloneMode = () => {
      const isStandalone =
        window.matchMedia("(display-mode: standalone)").matches ||
        (window.navigator as any).standalone ||
        document.referrer.includes("android-app://");

      setIsStandalone(isStandalone);
    };

    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setIsInstallable(true);
    };

    // Check if beforeinstallprompt is supported
    setIsInstallable("beforeinstallprompt" in window);

    window.addEventListener(
      "beforeinstallprompt",
      handleBeforeInstallPrompt as EventListener,
    );

    // Check initial state
    checkStandaloneMode();

    // Set up a listener for changes in display mode
    const mediaQuery = window.matchMedia("(display-mode: standalone)");
    mediaQuery.addListener(checkStandaloneMode);

    return () => {
      window.removeEventListener(
        "beforeinstallprompt",
        handleBeforeInstallPrompt as EventListener,
      );
      mediaQuery.removeListener(checkStandaloneMode);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      setInstallError("Installation prompt not available");
      return;
    }

    setIsInstalling(true);
    setInstallError(null);

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      if (outcome === "accepted") {
        console.log("User accepted the install prompt");
        setInstallSuccess(true);
      } else {
        console.log("User dismissed the install prompt");
      }
    } catch (error) {
      console.error("Failed to show prompt:", error);
      setInstallError("Failed to install the app. Please try again later.");
    } finally {
      setIsInstalling(false);
      setDeferredPrompt(null);
      setIsInstallable(false);
    }
  };

  const handleNotificationChange = (type: string, value: boolean) => {
    const updates: Partial<iUser> = {};

    if (type === "email") {
      setEmailNotifications(value);
      updates.emailNotifications = value;
    } else if (type === "push") {
      setPushNotifications(value);
      updates.pushNotifications = value;
    }

    onUpdateUser(updates);
  };

  const handlePrivacyChange = (type: string, value: boolean | string) => {
    const updates: Partial<iUser> = {};

    if (type === "visibility") {
      const visibilityValue = value as "public" | "private" | "friends";
      setProfileVisibility(visibilityValue);
      updates.profileVisibility = visibilityValue;
    } else if (type === "showEmail") {
      setShowEmail(value as boolean);
      updates.showEmail = value as boolean;
    }

    onUpdateUser(updates);
  };

  return (
    <div className="space-y-6">
      {/* App Installation Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5 text-blue-600" />
            Mobile App
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {isStandalone ? (
            <Alert>
              <Check className="h-4 w-4" />
              <AlertTitle>App Installed</AlertTitle>
              <AlertDescription>
                You&apos;re using the ReviewIt app! Enjoy the enhanced
                experience.
              </AlertDescription>
            </Alert>
          ) : (
            <>
              <div className="flex items-start gap-3">
                <Download className="h-5 w-5 text-blue-600 mt-1" />
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">
                    Install ReviewIt App
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Get faster access, offline capabilities, and push
                    notifications by installing our app.
                  </p>
                </div>
              </div>

              {isInstallable && deferredPrompt ? (
                <Button
                  onClick={handleInstallClick}
                  disabled={isInstalling}
                  className="w-full sm:w-auto"
                >
                  <Download className="h-4 w-4 mr-2" />
                  {isInstalling ? "Installing..." : "Install App"}
                </Button>
              ) : (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    App installation is not available on this device/browser, or
                    the app is already installed.
                  </AlertDescription>
                </Alert>
              )}

              {installError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Installation Error</AlertTitle>
                  <AlertDescription>{installError}</AlertDescription>
                </Alert>
              )}

              {installSuccess && (
                <Alert>
                  <Check className="h-4 w-4" />
                  <AlertTitle>Installation Successful</AlertTitle>
                  <AlertDescription>
                    The app has been successfully installed. You can now access
                    it from your home screen.
                  </AlertDescription>
                </Alert>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Notifications Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-green-600" />
            Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <label className="text-sm font-medium">Email Notifications</label>
              <p className="text-xs text-gray-600">
                Receive notifications about reviews, comments, and updates via
                email
              </p>
            </div>
            <Switch
              checked={emailNotifications}
              onCheckedChange={(checked) =>
                handleNotificationChange("email", checked)
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <label className="text-sm font-medium">Push Notifications</label>
              <p className="text-xs text-gray-600">
                Get instant notifications when someone interacts with your
                content
              </p>
            </div>
            <Switch
              checked={pushNotifications}
              onCheckedChange={(checked) =>
                handleNotificationChange("push", checked)
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Privacy Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-purple-600" />
            Privacy & Visibility
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <label className="text-sm font-medium">Profile Visibility</label>
              <p className="text-xs text-gray-600">
                Control who can see your profile and reviews
              </p>
            </div>
            <select
              value={profileVisibility}
              onChange={(e) =>
                handlePrivacyChange("visibility", e.target.value)
              }
              className="text-sm border border-gray-300 rounded-md px-3 py-1"
            >
              <option value="public">Public</option>
              <option value="private">Private</option>
              <option value="friends">Friends Only</option>
            </select>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <label className="text-sm font-medium">Show Email Address</label>
              <p className="text-xs text-gray-600">
                Display your email address on your public profile
              </p>
            </div>
            <Switch
              checked={showEmail}
              onCheckedChange={(checked) =>
                handlePrivacyChange("showEmail", checked)
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Account Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings2 className="h-5 w-5 text-gray-600" />
            Account Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Member since:</span>
              <p className="font-medium">
                {new Date(user.createdDate).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </p>
            </div>
            <div>
              <span className="text-gray-600">Total Reviews:</span>
              <p className="font-medium">{user.reviews?.length || 0}</p>
            </div>
            <div>
              <span className="text-gray-600">Total Comments:</span>
              <p className="font-medium">{user.comments?.length || 0}</p>
            </div>
            <div>
              <span className="text-gray-600">Helpful Votes:</span>
              <p className="font-medium">{user.likedReviews?.length || 0}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserSettings;
