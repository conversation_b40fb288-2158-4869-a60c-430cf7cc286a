/**
 * The ProductCard component is a React component that displays information about a product,
 * including its image, name, address/description, rating, and number of reviews.
 *
 * The component can work in two modes:
 * 1. If the `reviews` prop is provided, it will use those reviews to calculate the rating
 *    and display the product information. In this case, the product data is taken from
 *    the first review in the `reviews` array.
 * 2. If the `reviews` prop is not provided, it will fetch the reviews from the server
 *    using the `useQuery` hook from `@tanstack/react-query`. The product data is taken
 *    from the `product` prop in this case.
 *
 * The component also handles the following options:
 * - `showLatestReview`: Whether to show the link to the latest review.
 * - `size`: The size of the rating stars.
 * - `showWriteReview`: Whether to show the "Write Review" link.
 * - `showClaimThisProduct`: Whether to show the "Claim this product" link.
 *
 * The component renders a card-like UI with the product image, name, address/description,
 * rating stars, and other relevant information. It also includes a "Write Review" link
 * that navigates to a review creation page with a pre-set rating of 3 stars.
 */
"use client";
import {
  iProduct,
  iReview,
  iCalculatedRating,
  iUser,
  OGImage,
} from "@/app/util/Interfaces";
import Image from "next/legacy/image";
import { ReadOnlyRating } from "./RatingSystem";
import Link from "next/link";
import { calculateAverageReviewRatingSync } from "../util/calculateAverageReviewRating";
import RatingDisplayWithThreshold from "./RatingDisplayWithThreshold";
import { WeightedRatingResult } from "../util/calculateWeightedRating";
import VerticalLinks from "./VerticalLinks";
import { useRouter } from "next/navigation";
import ClaimProductComponent from "./ClaimProductComponent";
import { motion } from "framer-motion";
import {
  MdEmail,
  MdPhone,
  MdLanguage,
  MdAccessTime,
  MdLocationOn,
  MdCalendarToday,
  MdEdit,
  MdReport,
  MdShare,
  MdVerified,
} from "react-icons/md";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { isProductOwner } from "../util/clientFunctions";
import { formatPhoneNumber, getPhoneNumberLink } from "../util/formatters";
import { useState, useEffect } from "react";
import { generateComponentMetadata } from "../lib/componentMetadata";
import { generateShareMetadata } from "../lib/shareUtils";
import { ShareButtonWrapper } from "./ShareButtonWrapper";
import { Metadata } from "next";
import {
  Eye,
  MoreHorizontal,
  MapPin,
  MessageSquare,
  Search,
} from "lucide-react";
import OpeningHours from "./OpeningHours";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MINIMUM_REVIEWS } from "@/app/config/rating";

interface ProductCardProps {
  reviews?: iReview[] | null;
  options: {
    showLatestReview: boolean;
    size: string;
    showWriteReview: boolean;
    showClaimThisProduct: boolean;
  };
  product?: iProduct | null;
  currentUserId: string | null;
  useProductLink?: boolean; // Added new prop
}

const ratingColors = {
  1: "bg-red-500",
  2: "bg-orange-500",
  3: "bg-yellow-500",
  4: "bg-green-400",
  5: "bg-green-600",
};

const ProductCard: React.FC<ProductCardProps> = ({
  reviews,
  options,
  product,
  currentUserId,
  useProductLink = false, // Default to false
}) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!product) return <div>No product or reviews found</div>;

  const currentProduct =
    reviews && reviews.length > 0 ? reviews[0].product : product;
  const allReviews = product.reviews || (reviews as iReview[]);
  
  // Check if product already has weighted rating from API
  const hasWeightedRating = product.weightedRating && typeof product.weightedRating === 'object' && 'hasMinimumReviews' in product.weightedRating;
  const ratingResult = hasWeightedRating 
    ? product.weightedRating 
    : calculateAverageReviewRatingSync(allReviews, true);

  // Updated ownership check to be more comprehensive
  const canClaimProduct =
    mounted && currentUserId && !product.businessId && !product.hasOwner;
  const amITheOwner = mounted && isProductOwner(currentUserId, product);

  function isCalculatedRating(result: any): result is iCalculatedRating {
    return (
      typeof result === "object" &&
      "roundedRating" in result &&
      "roundedRatingOneDecimalPlace" in result &&
      "numberOfReviews" in result
    );
  }

  let roundedRating = 0;
  let roundedRatingOneDecimalPlace = "0";
  let numberOfReviews = 0;

  if (isCalculatedRating(ratingResult)) {
    roundedRating = ratingResult.roundedRating;
    roundedRatingOneDecimalPlace =
      ratingResult.roundedRatingOneDecimalPlace.toString();
    numberOfReviews = ratingResult.numberOfReviews;
  } else if (typeof ratingResult === "number") {
    roundedRating = ratingResult as number;
    roundedRatingOneDecimalPlace = (ratingResult as number).toFixed(1);
    numberOfReviews = allReviews?.length;
  }

  // Generate metadata for sharing
  const metadata = generateShareMetadata({
    title: `${product.name} | ${numberOfReviews} Reviews | Rating: ${roundedRatingOneDecimalPlace}/5`,
    description:
      product.description ||
      `Check out reviews for ${product.name} on Review It. ${numberOfReviews} reviews with an average rating of ${roundedRatingOneDecimalPlace}/5.`,
    imageUrl: product.display_image,
    url: `/reviews?id=${product.id}`,
    rating: roundedRating,
    reviewCount: numberOfReviews,
  });

  const cardVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.2,
        ease: "easeOut",
      },
    },
    hover: {
      y: -2,
      transition: {
        duration: 0.15,
        ease: "easeInOut",
      },
    },
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      className="w-full"
    >
      <div className="w-full max-w-4xl bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-150">
        <Link href={useProductLink ? `/product/${currentProduct?.id}` : `/reviews?id=${currentProduct?.id}`} className="block p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Image Section */}
            {currentProduct?.display_image && (
              <div className="flex-shrink-0 flex justify-center sm:block">
                <div className="relative w-32 h-32 sm:w-40 sm:h-40 rounded-lg overflow-hidden">
                  <div className="absolute inset-0">
                    <Image
                      src={currentProduct.display_image}
                      alt={`${currentProduct.name} Image`}
                      layout="fill"
                      objectFit="contain"
                      className="transition-transform duration-200 group-hover:scale-105 bg-gray-50"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Content Section */}
            <div className="flex-grow min-w-0">
              <div className="flex flex-col sm:items-start gap-2">
                <div className="w-full">
                  <div className="flex flex-wrap items-center gap-2">
                    <h2 className="text-lg font-semibold text-gray-900 break-words">
                      {currentProduct?.name}
                    </h2>
                    {currentProduct?.hasOwner && currentProduct?.business?.isVerified === true && (
                      <Badge
                        variant="secondary"
                        className="bg-blue-100 text-blue-700"
                      >
                        <MdVerified className="mr-1" size={14} />
                        Verified Business
                      </Badge>
                    )}
                    {currentProduct?.hasOwner && currentProduct?.business?.isVerified === false && (
                      <span className="text-xs text-gray-400 font-normal">
                        Unverified
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 flex items-center mt-1">
                    <MdLocationOn className="shrink-0 mr-1" />
                    {currentProduct?.latitude && currentProduct?.longitude ? (
                      <Link
                        href={`/location?id=${currentProduct.id}`}
                        className="truncate hover:text-blue-600 transition-colors"
                      >
                        {currentProduct?.address}
                      </Link>
                    ) : (
                      <span className="truncate">{currentProduct?.address}</span>
                    )}
                  </p>
                </div>

                {/* Rating Section */}
                <div className="flex items-center gap-2">
                  {allReviews && allReviews.length > 0 ? (
                    ratingResult && 'hasMinimumReviews' in ratingResult && ratingResult.hasMinimumReviews ? (
                      <div className="flex items-center gap-2">
                        <ReadOnlyRating
                          name={currentProduct?.id!}
                          rating={'displayRating' in ratingResult ? (ratingResult as WeightedRatingResult).displayRating : roundedRating}
                          size="lg"
                        />
                        <span className="text-sm text-gray-500">
                          ({numberOfReviews} reviews)
                        </span>
                      </div>
                    ) : (
                      <div className="flex flex-col items-start">
                        <div className="flex items-center gap-2">
                          <ReadOnlyRating
                            name={currentProduct?.id!}
                            rating={0}
                            size="lg"
                          />
                          <span className="text-sm text-gray-500 whitespace-nowrap">
                            ({numberOfReviews} reviews)
                          </span>
                        </div>
                        <span className="text-xs text-gray-400 mt-1">
                          ({MINIMUM_REVIEWS - numberOfReviews} more needed for rating)
                        </span>
                      </div>
                    )
                  ) : (
                    <div className="text-sm text-gray-500">
                      <span>No reviews yet</span>
                      <span className="text-xs text-gray-400 ml-1">
                        ({MINIMUM_REVIEWS} needed for rating)
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Details Section */}
              <div className="mt-3 space-y-2">
                {currentProduct?.openingHrs &&
                  currentProduct?.closingHrs &&
                  currentProduct?.openingDays &&
                  currentProduct.openingDays.length > 0 && (
                    <OpeningHours product={currentProduct} />
                  )}

                <div className="flex flex-wrap gap-2 text-sm text-gray-600">
                  {currentProduct?.telephone && (
                    <a
                      href={getPhoneNumberLink(currentProduct.telephone)}
                      className="text-sm text-gray-600 hover:text-blue-600 transition-colors flex items-center"
                    >
                      <MdPhone className="shrink-0 mr-1" />
                      {formatPhoneNumber(currentProduct.telephone)}
                    </a>
                  )}
                  {currentProduct?.email && (
                    <span className="flex items-center">
                      <MdEmail className="shrink-0 mr-1" />{" "}
                      {currentProduct.email}
                    </span>
                  )}
                </div>

                {currentProduct?.description && (
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {currentProduct.description}
                  </p>
                )}

                {/* Tags Section */}
                {currentProduct?.tags && currentProduct.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1.5 mt-2">
                    {currentProduct.tags.slice(0, 3).map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="px-2 py-0 text-xs font-normal bg-gray-100 text-gray-600"
                      >
                        {tag}
                      </Badge>
                    ))}
                    {currentProduct.tags.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{currentProduct.tags.length - 3} more
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </Link>

        {/* Footer Section */}
        <div className="border-t border-gray-100 p-4 bg-gray-50 rounded-b-lg">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
            <div className="flex items-center gap-3">
              {currentProduct?.website && currentProduct.website.length > 0 && (
                <a
                  href={currentProduct.website[0]}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 hover:text-blue-800 flex items-center transition-colors"
                >
                  <MdLanguage className="mr-1" /> Visit Website
                </a>
              )}
              <span className="text-xs text-gray-500 flex items-center">
                <MdCalendarToday className="mr-1" />
                Added:{" "}
                {new Date(currentProduct?.createdDate!).toLocaleDateString()}
              </span>
            </div>

            <div className="flex flex-col sm:flex-row items-center w-full sm:w-auto gap-2">
              {mounted &&
                options.showClaimThisProduct &&
                canClaimProduct &&
                !amITheOwner &&
                currentProduct && (
                  <div className="w-full sm:w-auto">
                    <ClaimProductComponent product={currentProduct} />
                  </div>
                )}

              <div className="grid grid-cols-2 sm:flex sm:flex-row gap-2 w-full sm:w-auto">
                <div className="w-full">
                  <Link
                    href={`/product/${currentProduct?.id}`}
                    className="w-full"
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full flex items-center justify-center gap-1"
                    >
                      <Eye size={16} />
                      <span>View</span>
                    </Button>
                  </Link>
                </div>

                {mounted && options.showWriteReview && (
                  <div className="w-full">
                    <Link
                      href={`/cr/?id=${currentProduct?.id}&rating=3`}
                      className="w-full"
                    >
                      <Button
                        variant="default"
                        size="sm"
                        className="w-full flex items-center justify-center gap-1 bg-black hover:bg-gray-800 text-white"
                      >
                        <MdEdit size={16} />
                        <span>Review</span>
                      </Button>
                    </Link>
                  </div>
                )}

                <div className="w-full">
                  <ShareButtonWrapper metadata={metadata} className="w-full" />
                </div>

                <div className="w-full">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full flex items-center justify-center gap-1"
                      >
                        <MoreHorizontal size={16} />
                        <span>More</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem>
                        <Link
                          href={`/reviews?id=${currentProduct?.id}`}
                          className="flex items-center gap-2 w-full"
                        >
                          <MessageSquare size={16} />
                          View All Reviews
                        </Link>
                      </DropdownMenuItem>
                      {currentProduct?.latitude &&
                        currentProduct?.longitude && (
                          <DropdownMenuItem>
                            <Link
                              href={`/location?id=${currentProduct?.id}`}
                              className="flex items-center gap-2 w-full"
                            >
                              <MapPin size={16} />
                              View on Map
                            </Link>
                          </DropdownMenuItem>
                        )}
                      {currentProduct?.tags &&
                        currentProduct.tags.length > 0 && (
                          <DropdownMenuItem>
                            <Link
                              href={`/browse?tags=${encodeURIComponent(currentProduct.tags.slice(0, 2).join(","))}`}
                              className="flex items-center gap-2 w-full"
                            >
                              <Search size={16} />
                              View Similar Products
                            </Link>
                          </DropdownMenuItem>
                        )}
                      <DropdownMenuItem>
                        <Button
                          variant="ghost"
                          className="flex items-center gap-2 w-full text-red-600 hover:text-red-700"
                        >
                          <MdReport size={16} />
                          Report Product
                        </Button>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ProductCard;
