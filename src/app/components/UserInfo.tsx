import React, { useState, useRef, useEffect, useMemo } from "react";
import Image from "next/image";
import Link from "next/link";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { avatarTriggerAtom } from "@/app/store/store";
import { useAtom } from "jotai";
import {
  MessageCircle,
  ThumbsUp,
  FileText,
  Search,
  Camera,
  Edit,
  Save,
  AlertCircle,
  Settings,
} from "lucide-react";
import { iUser, iReview, iProduct } from "../util/Interfaces";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { uploadProfilePicToCloudinary } from "../util/uploadImageToCloudinary";
import { useImageResizer } from "../util/useImageResizer";
import { toast } from "sonner";
import { useAuth } from "@clerk/nextjs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import UserSettings from "./UserSettings";

interface UserInfoProps {
  user: iUser;
  onUpdateUser: (updatedUser: Partial<iUser>) => void;
}

// Define gradient combinations
const profileGradients = [
  "from-blue-500 to-purple-600",
  "from-pink-500 to-orange-400",
  "from-green-400 to-blue-500",
  "from-purple-500 to-pink-500",
  "from-yellow-400 to-orange-500",
  "from-indigo-500 to-purple-600",
  "from-teal-400 to-blue-500",
  "from-red-500 to-pink-500",
  "from-emerald-500 to-teal-500",
  "from-violet-500 to-purple-600",
  "from-cyan-500 to-blue-500",
  "from-rose-500 to-pink-500",
  "from-amber-500 to-orange-500",
  "from-sky-500 to-indigo-500",
  "from-fuchsia-500 to-purple-500",
];

export default function UserInfo({ user, onUpdateUser }: UserInfoProps) {
  const { userId: clerkUserId } = useAuth();
  let {
    firstName,
    lastName,
    userName,
    avatar,
    reviews,
    comments,
    likedReviews,
    bio,
    commentVotes,
    id,
  } = user;
  const [activeTab, setActiveTab] = useState("reviews");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<"date" | "rating">("date");
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [editedFirstName, setEditedFirstName] = useState(firstName);
  const [editedLastName, setEditedLastName] = useState(lastName);
  const [editedUserName, setEditedUserName] = useState(userName);
  const [editedBio, setEditedBio] = useState(bio || "");
  const [isAvatarUploading, setIsAvatarUploading] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [currentAvatar, setCurrentAvatar] = useState(avatar || "");
  const [, setAvatarTrigger] = useAtom(avatarTriggerAtom);
  const [isLoadingReviews, setIsLoadingReviews] = useState(false);
  const [selectedGradient, setSelectedGradient] = useState("");

  dayjs.extend(relativeTime);

  // Calculate total likes
  const reviewLikes = likedReviews?.length || 0;
  const commentUpvotes =
    commentVotes?.filter((vote) => vote.voteType === "UP").length || 0;
  const totalLikes = reviewLikes + commentUpvotes;

  // Get unique tags from all reviews
  const allTags = Array.from(
    new Set(reviews?.flatMap((review) => review.product?.tags || []) || []),
  );

  // Enhanced filtering function with type safety
  const filteredReviews = useMemo(() => {
    if (!reviews) return [];

    return reviews
      .filter((review) => {
        const matchesSearch =
          searchTerm === "" ||
          (review.title?.toLowerCase().includes(searchTerm.toLowerCase()) ??
            false) ||
          (review.body?.toLowerCase().includes(searchTerm.toLowerCase()) ??
            false) ||
          (review.product?.name
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase()) ??
            false);

        const matchesTag =
          !selectedTag ||
          (review.product?.tags?.includes(selectedTag) ?? false);

        return matchesSearch && matchesTag;
      })
      .sort((a, b) => {
        if (sortBy === "date") {
          return (
            new Date(b.createdDate || 0).getTime() -
            new Date(a.createdDate || 0).getTime()
          );
        } else {
          return (b.rating || 0) - (a.rating || 0);
        }
      });
  }, [reviews, searchTerm, selectedTag, sortBy]);

  // Handle loading state separately
  useEffect(() => {
    setIsLoadingReviews(true);
    const timer = setTimeout(() => {
      setIsLoadingReviews(false);
    }, 100); // Small delay to prevent flickering
    return () => clearTimeout(timer);
  }, [searchTerm, selectedTag, sortBy]);

  const filteredComments = comments?.filter((comment) =>
    comment.body.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const filteredLikes = likedReviews?.filter((liked) => {
    const likedReviews =
      liked.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
      liked.userId !== user.id;
    return likedReviews;
  });

  // Input sanitization to prevent XSS
  const sanitizeInput = (input: string): string => {
    return input.replace(/<[^>]*>/g, "");
  };

  // Form validation
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!editedFirstName.trim()) {
      errors.firstName = "First name is required";
    }

    if (!editedLastName.trim()) {
      errors.lastName = "Last name is required";
    }

    if (!editedUserName.trim()) {
      errors.userName = "Username is required";
    } else if (editedUserName.length < 3) {
      errors.userName = "Username must be at least 3 characters";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAvatarClick = () => {
    if (isEditable) {
      fileInputRef.current?.click();
    }
  };

  const { processImage, isResizing } = useImageResizer();

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (!isEditable) return;
    const file = event.target.files?.[0];
    if (file) {
      setIsAvatarUploading(true);
      const tempUrl = URL.createObjectURL(file);
      setCurrentAvatar(tempUrl);

      try {
        // Process the file directly with our enhanced hook
        const result = await processImage(file, {
          maxDimension: 400, // Appropriate size for profile pictures
          quality: 0.8,
        });

        // Extract the dataUrl from the result
        let dataUrl: string;
        if ("dataUrl" in result) {
          dataUrl = result.dataUrl ?? "";
        } else {
          // This shouldn't happen now that we always return dataUrl
          console.warn("Unexpected result type when processing profile image");
          throw new Error("Failed to process image");
        }

        try {
          const res = await uploadProfilePicToCloudinary(dataUrl);
          const imageUrl = res.secure_url;
          setCurrentAvatar(imageUrl);
          onUpdateUser({ avatar: imageUrl });
          setAvatarTrigger(imageUrl);
          toast.success("Profile picture updated successfully");
        } catch (error) {
          console.error("Error uploading image:", error);
          toast.error("Failed to upload image. Please try again.");
        } finally {
          setIsAvatarUploading(false);
          URL.revokeObjectURL(tempUrl);
        }
      } catch (error) {
        console.error("Error processing image:", error);
        toast.error("Failed to process image. Please try again.");
        setIsAvatarUploading(false);
        URL.revokeObjectURL(tempUrl);
      }
    }
  };

  const handleSaveProfile = () => {
    if (!isEditable) return;

    if (validateForm()) {
      onUpdateUser({
        firstName: sanitizeInput(editedFirstName),
        lastName: sanitizeInput(editedLastName),
        userName: sanitizeInput(editedUserName),
        bio: sanitizeInput(editedBio),
      });
      setIsEditingProfile(false);
    } else {
      toast.error("Please fix the errors in the form");
    }
  };

  const isEditable = id === clerkUserId;

  // Select a random gradient on component mount
  useEffect(() => {
    const randomIndex = Math.floor(Math.random() * profileGradients.length);
    setSelectedGradient(profileGradients[randomIndex]);
  }, []);

  return (
    <Card className="w-full border-0 shadow-none">
      <CardHeader
        className={`relative h-32 sm:h-48 md:h-64 overflow-hidden bg-gradient-to-br ${selectedGradient} transition-all duration-500`}
      >
        {/* Stylized background text */}
        <div className="absolute inset-0 pointer-events-none select-none">
          <div className="absolute top-6 left-4 md:top-10 md:left-10 opacity-10 text-white text-3xl sm:text-5xl md:text-7xl font-extrabold tracking-wide whitespace-nowrap -rotate-6 drop-shadow-lg">
            ReviewIt
          </div>
          <div className="absolute top-6 right-4 md:top-10 md:right-10 opacity-10 text-white text-2xl sm:text-4xl md:text-6xl font-extrabold tracking-wide whitespace-nowrap rotate-3 drop-shadow-lg">
            Reviews
          </div>
          <div className="absolute bottom-6 right-4 md:bottom-10 md:right-10 opacity-10 text-white text-2xl sm:text-4xl md:text-6xl font-extrabold tracking-wide whitespace-nowrap rotate-6 drop-shadow-lg">
            Authentic
          </div>
          {/* Username background text */}
          {userName && (
            <div className="absolute bottom-6 left-4 md:bottom-10 md:left-10 opacity-10 text-white text-2xl sm:text-4xl md:text-6xl font-extrabold tracking-wide whitespace-nowrap rotate-2 drop-shadow-lg">
              @{userName}
            </div>
          )}
        </div>
        <div className="absolute inset-0 bg-black/5"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative">
            <div
              className={`relative h-24 w-24 sm:h-32 sm:w-32 md:h-40 md:w-40 ${isEditable ? "cursor-pointer" : ""}`}
              onClick={handleAvatarClick}
              role={isEditable ? "button" : undefined}
              aria-label={isEditable ? "Change profile picture" : undefined}
              tabIndex={isEditable ? 0 : undefined}
              onKeyDown={(e) => {
                if (isEditable && (e.key === "Enter" || e.key === " ")) {
                  e.preventDefault();
                  handleAvatarClick();
                }
              }}
            >
              <Avatar className="h-full w-full border-4 border-white/90 shadow-lg backdrop-blur-sm">
                <AvatarImage
                  src={currentAvatar || ""}
                  alt={`${editedFirstName} ${editedLastName}`}
                  className="object-cover"
                />
                <AvatarFallback className="bg-white/80 text-gray-700">
                  {`${editedFirstName?.charAt(0) || ""}${editedLastName?.charAt(0) || ""}`}
                </AvatarFallback>
              </Avatar>
              {(isAvatarUploading || isResizing) && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                  {isResizing && (
                    <span className="absolute text-white text-xs mt-12">
                      Processing...
                    </span>
                  )}
                </div>
              )}
            </div>
            {isEditable && (
              <div
                className="absolute bottom-0 right-0 bg-primary rounded-full p-2 cursor-pointer shadow-md hover:shadow-lg transition-shadow"
                onClick={handleAvatarClick}
                role="button"
                aria-label="Change profile picture"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    handleAvatarClick();
                  }
                }}
              >
                <Camera className="h-4 w-4 text-white" aria-hidden="true" />
              </div>
            )}
          </div>
          {isEditable && (
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              className="hidden"
              aria-label="Upload profile picture"
            />
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-2 sm:pt-4 md:pt-6">
        <div className="text-center mb-4 sm:mb-6">
          {isEditingProfile && isEditable ? (
            <div className="flex flex-col gap-4 max-w-md mx-auto bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-center mb-2">
                Edit Profile
              </h3>
              <div className="space-y-4">
                <div className="space-y-1">
                  <label
                    htmlFor="firstName"
                    className="text-sm font-medium text-gray-700"
                  >
                    First Name
                  </label>
                  <Input
                    id="firstName"
                    value={editedFirstName}
                    onChange={(e) => setEditedFirstName(e.target.value)}
                    placeholder="First Name"
                    className={`bg-white ${formErrors.firstName ? "border-red-500" : ""}`}
                    aria-invalid={!!formErrors.firstName}
                    aria-describedby={
                      formErrors.firstName ? "firstName-error" : undefined
                    }
                  />
                  {formErrors.firstName && (
                    <p
                      id="firstName-error"
                      className="text-xs text-red-500 flex items-center"
                    >
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {formErrors.firstName}
                    </p>
                  )}
                </div>

                <div className="space-y-1">
                  <label
                    htmlFor="lastName"
                    className="text-sm font-medium text-gray-700"
                  >
                    Last Name
                  </label>
                  <Input
                    id="lastName"
                    value={editedLastName}
                    onChange={(e) => setEditedLastName(e.target.value)}
                    placeholder="Last Name"
                    className={`bg-white ${formErrors.lastName ? "border-red-500" : ""}`}
                    aria-invalid={!!formErrors.lastName}
                    aria-describedby={
                      formErrors.lastName ? "lastName-error" : undefined
                    }
                  />
                  {formErrors.lastName && (
                    <p
                      id="lastName-error"
                      className="text-xs text-red-500 flex items-center"
                    >
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {formErrors.lastName}
                    </p>
                  )}
                </div>

                <div className="space-y-1">
                  <label
                    htmlFor="userName"
                    className="text-sm font-medium text-gray-700"
                  >
                    Username
                  </label>
                  <Input
                    id="userName"
                    value={editedUserName}
                    onChange={(e) => setEditedUserName(e.target.value)}
                    placeholder="Username"
                    className={`bg-white ${formErrors.userName ? "border-red-500" : ""}`}
                    aria-invalid={!!formErrors.userName}
                    aria-describedby={
                      formErrors.userName ? "userName-error" : undefined
                    }
                  />
                  {formErrors.userName && (
                    <p
                      id="userName-error"
                      className="text-xs text-red-500 flex items-center"
                    >
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {formErrors.userName}
                    </p>
                  )}
                </div>

                <div className="space-y-1">
                  <label
                    htmlFor="bio"
                    className="text-sm font-medium text-gray-700"
                  >
                    Bio
                  </label>
                  <Textarea
                    id="bio"
                    value={editedBio}
                    onChange={(e) => setEditedBio(e.target.value)}
                    placeholder="Write your bio here..."
                    className="bg-white min-h-[100px] resize-none"
                    aria-label="Bio"
                  />
                </div>
              </div>

              <div className="flex gap-3 justify-end mt-4">
                <Button
                  variant="outline"
                  onClick={() => setIsEditingProfile(false)}
                  className="min-w-[100px] border-gray-300 hover:bg-gray-100"
                  aria-label="Cancel profile editing"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveProfile}
                  className="min-w-[100px] bg-blue-600 hover:bg-blue-700 text-white"
                  aria-label="Save profile changes"
                >
                  <Save className="h-4 w-4 mr-2" aria-hidden="true" />
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <>
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold">{`${editedFirstName} ${editedLastName}`}</h2>
              <p className="text-muted-foreground">@{editedUserName}</p>
              <p className="text-sm text-muted-foreground mt-2 max-w-md mx-auto">
                {editedBio || "No bio yet."}
              </p>
              {isEditable && (
                <Button
                  variant="outline"
                  onClick={() => setIsEditingProfile(true)}
                  className="mt-4 hover:bg-primary/5"
                  aria-label="Edit profile"
                >
                  <Edit className="h-4 w-4 mr-2" aria-hidden="true" />
                  Edit Profile
                </Button>
              )}
            </>
          )}
        </div>

        <Tabs
          defaultValue="reviews"
          className="w-full"
          onValueChange={setActiveTab}
          aria-label="User content tabs"
        >
          <TabsList className="grid w-full grid-cols-4 mb-4 bg-white border rounded-lg p-2 h-20 sm:h-20">
            <TabsTrigger
              value="reviews"
              className="data-[state=active]:bg-primary/5 rounded-md text-xs sm:text-sm flex-col sm:flex-row gap-1 sm:gap-2 py-3 sm:py-4 h-full"
            >
              <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="hidden sm:inline">Reviews</span>
              <span className="text-xs">({reviews?.length || 0})</span>
            </TabsTrigger>
            <TabsTrigger
              value="comments"
              className="data-[state=active]:bg-primary/5 rounded-md text-xs sm:text-sm flex-col sm:flex-row gap-1 sm:gap-2 py-3 sm:py-4 h-full"
            >
              <MessageCircle className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="hidden sm:inline">Comments</span>
              <span className="text-xs">({comments?.length || 0})</span>
            </TabsTrigger>
            <TabsTrigger
              value="likes"
              className="data-[state=active]:bg-primary/5 rounded-md text-xs sm:text-sm flex-col sm:flex-row gap-1 sm:gap-2 py-3 sm:py-4 h-full"
            >
              <ThumbsUp className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="hidden sm:inline">Likes</span>
              <span className="text-xs">({reviewLikes})</span>
            </TabsTrigger>
            {isEditable && (
              <TabsTrigger
                value="settings"
                className="data-[state=active]:bg-primary/5 rounded-md text-xs sm:text-sm flex-col sm:flex-row gap-1 sm:gap-2 py-3 sm:py-4 h-full"
              >
                <Settings className="h-4 w-4 sm:h-5 sm:w-5" />
                <span className="hidden sm:inline">Settings</span>
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="reviews">
            <div className="space-y-4">
              <div className="flex flex-col gap-3">
                <div className="relative">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search reviews..."
                    className="pl-10 bg-white rounded-lg text-sm"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Select
                    value={selectedTag || "all"}
                    onValueChange={(value) =>
                      setSelectedTag(value === "all" ? null : value)
                    }
                  >
                    <SelectTrigger className="w-full sm:w-[140px] text-sm">
                      <SelectValue placeholder="All Tags" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Tags</SelectItem>
                      {allTags.map((tag) => (
                        <SelectItem key={tag} value={tag}>
                          {tag}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={sortBy}
                    onValueChange={(value) =>
                      setSortBy(value as "date" | "rating")
                    }
                  >
                    <SelectTrigger className="w-full sm:w-[140px] text-sm">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="date">Sort by Date</SelectItem>
                      <SelectItem value="rating">Sort by Rating</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <ScrollArea className="h-[450px] sm:h-[300px]">
                <div className="w-full rounded-lg border p-2 sm:p-4">
                  {isLoadingReviews ? (
                    <div className="flex justify-center items-center h-64">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                  ) : (
                    <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 text-sm">
                      {filteredReviews?.map((review) => (
                        <Link
                          key={review.id}
                          href={`/fr?id=${review.id}&productid=${review.productId}`}
                          className="block"
                        >
                          <Card className="h-full hover:shadow-md transition-shadow">
                            <CardContent className="p-3 sm:p-4">
                              <div className="flex items-start gap-2 sm:gap-3 mb-2">
                                <div className="relative w-8 h-8 sm:w-10 sm:h-10 rounded-md overflow-hidden flex-shrink-0">
                                  <Image
                                    src={
                                      review.product?.display_image ||
                                      "/images/default-product.png"
                                    }
                                    alt={review.title || "Product image"}
                                    fill
                                    className="object-cover"
                                    onError={(e) => {
                                      const target =
                                        e.target as HTMLImageElement;
                                      target.src =
                                        "/images/default-product.png";
                                    }}
                                  />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="font-medium line-clamp-2 text-sm sm:text-base leading-tight">
                                    {review.title}
                                  </p>
                                  <p className="text-xs text-muted-foreground line-clamp-1 mt-0.5">
                                    {review.product?.name}
                                  </p>
                                </div>
                              </div>
                              <div className="space-y-1.5 text-xs text-muted-foreground">
                                <div className="flex items-center justify-between">
                                  <span className="font-medium text-yellow-600">
                                    {review.rating}/5 ⭐
                                  </span>
                                  <span className="text-xs">
                                    {review.comments?.length || 0} comments
                                  </span>
                                </div>
                                <div className="flex flex-wrap gap-1">
                                  {review.product?.tags
                                    ?.slice(0, 2)
                                    .map((tag) => (
                                      <span
                                        key={tag}
                                        className="px-1.5 py-0.5 bg-gray-100 rounded text-xs"
                                      >
                                        {tag}
                                      </span>
                                    ))}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                  {dayjs(review.createdDate).fromNow()}
                                </p>
                              </div>
                            </CardContent>
                          </Card>
                        </Link>
                      ))}
                      {(!filteredReviews || filteredReviews.length === 0) && (
                        <div className="col-span-full text-center text-muted-foreground py-8">
                          No reviews found
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>
          <TabsContent value="comments">
            <ScrollArea className="h-[450px] sm:h-[300px]">
              <div className="w-full rounded-lg border p-2 sm:p-4">
                <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                  {filteredComments?.map((comment) => (
                    <Link
                      key={comment.id}
                      href={`/fr?id=${comment.review?.id}&productid=${comment.review?.productId}`}
                      className="block"
                    >
                      <Card className="h-full hover:shadow-md transition-shadow">
                        <CardContent className="p-3 sm:p-4">
                          <p className="line-clamp-4 text-sm leading-relaxed">
                            {comment.body}
                          </p>
                          <div className="flex justify-between items-center mt-3 text-xs text-muted-foreground">
                            <span>{dayjs(comment.createdDate).fromNow()}</span>
                            <span className="bg-gray-100 px-2 py-1 rounded">
                              {comment.replies?.length || 0} replies
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                  {(!filteredComments || filteredComments.length === 0) && (
                    <div className="col-span-full text-center text-muted-foreground py-8">
                      No comments yet
                    </div>
                  )}
                </div>
              </div>
            </ScrollArea>
          </TabsContent>
          <TabsContent value="likes">
            <ScrollArea className="h-[450px] sm:h-[300px]">
              <div className="w-full rounded-lg border p-2 sm:p-4">
                <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                  {filteredLikes?.map((liked) => (
                    <Link
                      key={liked.id}
                      href={`/fr?id=${liked.id}&productid=${liked.productId}`}
                      className="block"
                    >
                      <Card className="h-full hover:shadow-md transition-shadow">
                        <CardContent className="p-3 sm:p-4">
                          <div className="flex items-start gap-2 sm:gap-3 mb-2">
                            <div className="relative w-8 h-8 sm:w-10 sm:h-10 rounded-md overflow-hidden flex-shrink-0">
                              <Image
                                src={liked.product?.display_image || ""}
                                alt={liked.title}
                                fill
                                className="object-cover"
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="font-medium line-clamp-2 text-sm leading-tight">
                                {liked.title}
                              </p>
                              <p className="text-xs text-muted-foreground line-clamp-1 mt-0.5">
                                {liked.product?.name}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <ThumbsUp
                                className="h-3 w-3"
                                aria-hidden="true"
                              />
                              <span>{liked.helpfulVotes || 0} helpful</span>
                            </div>
                            <span>{dayjs(liked.createdDate).fromNow()}</span>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                  {(!filteredLikes || filteredLikes.length === 0) && (
                    <div className="col-span-full text-center text-muted-foreground py-8">
                      No liked reviews yet
                    </div>
                  )}
                </div>
              </div>
            </ScrollArea>
          </TabsContent>
          {isEditable && (
            <TabsContent value="settings">
              <UserSettings user={user} onUpdateUser={onUpdateUser} />
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
}
