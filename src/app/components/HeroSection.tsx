"use client";

import Image from "next/legacy/image";
import SearchBoxAndListener from "./SearchBoxAndListener";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { useEffect, useState } from "react";

const HeroSection = () => {
  const [allProducts] = useAtom(allProductsStore);
  const [stats, setStats] = useState({
    reviews: 0,
    products: 0,
    categories: 0
  });

  useEffect(() => {
    if (allProducts && allProducts.length > 0) {
      // Calculate total reviews
      const totalReviews = allProducts.reduce((acc, product) => {
        return acc + (product._count?.reviews || 0);
      }, 0);

      // Get unique categories from tags
      const uniqueCategories = new Set<string>();
      allProducts.forEach(product => {
        product.tags?.forEach(tag => uniqueCategories.add(tag));
      });

      setStats({
        reviews: totalReviews,
        products: allProducts.length,
        categories: uniqueCategories.size
      });
    }
  }, [allProducts]);

  return (
    <section className="relative h-[550px] sm:h-[500px] md:h-[600px] lg:h-[500px] w-full">
      <div className="absolute inset-0">
        <Image
          src="/hero1.jpg"
          alt="ReviewIt Guyana - Your trusted review platform"
          layout="fill"
          className="object-cover object-center"
          quality={75}
          priority
        />
      </div>
      {/* Increased opacity from 60 to 75, added a darker gradient overlay, and increased backdrop blur */}
      <div className="relative flex justify-center items-center h-full w-full text-center text-white bg-opacity-75 bg-myTheme-neutral backdrop-blur-[4px] bg-gradient-to-b from-black/50 to-myTheme-neutral/70">
        <div className="flex flex-1 justify-center flex-col relative">
          <div className="flex flex-1 justify-center">
            <div className="flex flex-col h-full w-11/12 sm:w-7/12 py-4 sm:py-8">
              <div className="flex-1 md:mt-4">
                {/* Enhanced headings with iPad Mini optimizations */}
                <h1 className="text-4xl sm:text-5xl md:text-4xl lg:text-6xl font-black mb-4 md:mb-6 text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)] leading-tight">
                  ReviewIt Guyana - Find & Share Reviews in Guyana
                </h1>
                <p className="text-xl sm:text-2xl md:text-xl lg:text-3xl mt-2 font-semibold pb-2 text-white drop-shadow-[0_1px_3px_rgba(0,0,0,0.7)]">
                  Your trusted platform for honest reviews on businesses and services
                </p>
                <p className="text-lg sm:text-xl md:text-lg lg:text-2xl mt-0 font-medium pb-4 md:pb-6 text-myTheme-light drop-shadow-[0_1px_3px_rgba(0,0,0,0.7)]">
                  Join thousands of reviewers sharing their experiences.
                </p>
              </div>

              <div className="flex flex-col justify-end space-y-4 md:space-y-6 relative z-[200]">
                <form className="flex w-full">
                  <div className="relative w-full">
                    <SearchBoxAndListener />
                  </div>
                </form>

                {/* Added subtle text shadow to stats for better visibility */}
                <div className="flex flex-wrap justify-center gap-2 sm:gap-4 text-xs md:text-sm text-white bg-black/30 py-2 md:py-3 px-2 rounded-lg drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] w-fit mx-auto">
                  {!allProducts ? (
                    // Loading skeleton
                    <>
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-white animate-pulse bg-white/20 rounded w-12 h-5"></span>
                        <span className="animate-pulse bg-white/20 rounded w-16 h-4"></span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-white animate-pulse bg-white/20 rounded w-12 h-5"></span>
                        <span className="animate-pulse bg-white/20 rounded w-16 h-4"></span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-white animate-pulse bg-white/20 rounded w-12 h-5"></span>
                        <span className="animate-pulse bg-white/20 rounded w-16 h-4"></span>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-white drop-shadow-[0_1px_1px_rgba(0,0,0,1)]">{stats.reviews}+</span> Reviews
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-white drop-shadow-[0_1px_1px_rgba(0,0,0,1)]">{stats.products}+</span> Products
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-white drop-shadow-[0_1px_1px_rgba(0,0,0,1)]">{stats.categories}+</span> Tags
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
