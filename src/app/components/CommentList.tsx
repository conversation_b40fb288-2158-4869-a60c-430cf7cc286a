import React, { useMemo, useCallback, useState } from "react";
import { iComment, iUser, iProduct } from "../util/Interfaces";
import Comment from "./Comment";
import { Button } from "@/components/ui/button";
import { Search, ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface CommentListProps {
  comments: iComment[];
  onReply: (parentId: string, body: string) => Promise<void>;
  onEdit: (commentId: string, body: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  clerkUserId: string;
  currentUser: iUser;
  product?: iProduct;
}

type SortOption = "best" | "new" | "old";

const CommentList: React.FC<CommentListProps> = ({
  comments,
  onReply,
  onEdit,
  onDelete,
  clerkUserId,
  currentUser,
  product,
}) => {
  console.log("CommentList received comments:", comments);
  console.log(
    "Is this a new review with no comments?",
    !comments || comments.length === 0
  );

  const [sortBy, setSortBy] = useState<SortOption>("best");
  const [searchQuery, setSearchQuery] = useState("");

  // Memoize the organizeComments function
  const organizeComments = useCallback((comments: iComment[]): iComment[] => {
    const commentMap = new Map<string, iComment>();
    const rootComments: iComment[] = [];

    comments.forEach((comment) => {
      comment.replies = [];
      commentMap.set(comment.id!, comment);
    });

    comments.forEach((comment) => {
      if (comment.parentId) {
        const parent = commentMap.get(comment.parentId);
        if (parent) {
          parent.replies!.push(comment);
        }
      } else {
        rootComments.push(comment);
      }
    });

    return rootComments;
  }, []);

  // Sort function based on selected sort option
  const sortComments = useCallback(
    (comments: iComment[]): iComment[] => {
      const sortedComments = [...comments];

      // Helper function to sort a single level of comments
      const sortCommentLevel = (a: iComment, b: iComment) => {
        switch (sortBy) {
          case "new":
            return (
              new Date(b.createdDate).getTime() -
              new Date(a.createdDate).getTime()
            );
          case "old":
            return (
              new Date(a.createdDate).getTime() -
              new Date(b.createdDate).getTime()
            );
          case "best":
          default:
            // Calculate scores
            const scoreA = (a.upvotes || 0) - (a.downvotes || 0);
            const scoreB = (b.upvotes || 0) - (b.downvotes || 0);

            // If scores are equal, sort by recency
            if (scoreA === scoreB) {
              return (
                new Date(b.createdDate).getTime() -
                new Date(a.createdDate).getTime()
              );
            }

            // Otherwise sort by score
            return scoreB - scoreA;
        }
      };

      // Sort root comments
      sortedComments.sort(sortCommentLevel);

      // Sort replies recursively
      const sortReplies = (comment: iComment) => {
        if (comment.replies && comment.replies.length > 0) {
          comment.replies.sort(sortCommentLevel);
          comment.replies.forEach(sortReplies);
        }
      };

      // Sort replies for each root comment
      sortedComments.forEach(sortReplies);

      return sortedComments;
    },
    [sortBy]
  );

  // Filter comments based on search query
  const filterComments = useCallback(
    (comments: iComment[]): iComment[] => {
      if (!searchQuery.trim()) return comments;

      return comments.filter(
        (comment) =>
          comment.body.toLowerCase().includes(searchQuery.toLowerCase()) ||
          comment.user?.userName
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
      );
    },
    [searchQuery]
  );

  // Use useMemo to organize, sort and filter comments
  const processedComments = useMemo(() => {
    const organized = organizeComments(comments);
    const sorted = sortComments(organized);
    return filterComments(sorted);
  }, [comments, organizeComments, sortComments, filterComments]);

  // Memoize the renderComment function
  const renderComment = useCallback(
    (comment: iComment, depth: number = 0) => (
      <Comment
        clerkUserId={clerkUserId}
        key={comment.id || "no key"}
        comment={comment}
        onReply={(parentId, body) => onReply(parentId, body)}
        onEdit={onEdit}
        onDelete={onDelete}
        depth={depth}
        currentUser={currentUser}
        product={product}
      >
        {comment.replies &&
          comment.replies.map((reply) => renderComment(reply, depth + 1))}
      </Comment>
    ),
    [clerkUserId, onReply, onEdit, onDelete, currentUser, product]
  );

  return (
    <div className="flex flex-col w-full bg-gray-50 max-w-full overflow-hidden rounded-md">
      {/* Comment header with sort and search */}
      <div className="flex items-center justify-between border-b border-gray-200 p-3 bg-white">
        <div className="flex items-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center text-sm font-medium text-gray-700 gap-1 h-8"
              >
                Sort by: {sortBy.charAt(0).toUpperCase() + sortBy.slice(1)}
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem onClick={() => setSortBy("best")}>
                Best
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("new")}>
                New
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("old")}>
                Old
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="relative flex items-center">
          <Search className="h-4 w-4 absolute left-2 text-gray-400" />
          <input
            type="text"
            placeholder="Search comments"
            className="pl-8 pr-3 py-1 text-sm border border-gray-200 rounded-full w-full max-w-[180px] md:max-w-[240px] focus:outline-none focus:ring-1 focus:ring-blue-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* Comments list */}
      <div className="p-3 space-y-3">
        {processedComments.length > 0 ? (
          processedComments.map((comment) => renderComment(comment))
        ) : (
          <div className="text-center py-6 text-gray-500">
            {searchQuery ? "No comments match your search" : "No comments yet"}
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(CommentList);
