"use client";
import { iProduct, iReview } from "../util/Interfaces";
import { useQuery } from "@tanstack/react-query";
import { getReviews } from "../util/serverFunctions";
import LoadingSpinner from "./LoadingSpinner";
import ProductCard from "./ProductCard";
import "dayjs/locale/en"; // Import the English locale
import ReviewCard from "./ReviewCard";
import Link from "next/link";
import WriteAReview from "./WriteAReview";
import RatingDistribution from "@/components/product/RatingDistribution";
import { CompanyActivityCard } from "@/components/company-activity-card";
import { useAuth } from "@clerk/nextjs";
import Breadcrumb from "./Breadcrumb";
import FAQSection from "./FAQSection";
import Schema from "./Schema";
import ProductShowcase from "./ProductShowcase";
import { Eye } from "lucide-react";
import { useState, useEffect } from "react";
import { fetchProductAndReviews } from "@/app/actions/reviewsPageActions";
import { ShareButtonWrapper } from "./ShareButtonWrapper";
import { generateShareMetadata } from "../lib/shareUtils";

const Reviews = ({ productId }: { productId: string }) => {
  const [product, setProduct] = useState<iProduct | null>(null);
  const [reviews, setReviews] = useState<iReview[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { userId } = useAuth();

  useEffect(() => {
    const fetchData = async () => {
      console.log(
        `[Reviews Component] Fetching data for product ID: ${productId}`
      );
      try {
        setIsLoading(true);
        setError(null);

        // Use the server action to fetch both product and reviews
        const data = await fetchProductAndReviews(productId);
        console.log(`[Reviews Component] Data received:`, data);

        if (!data.success) {
          throw new Error(data.error || 'Failed to fetch data');
        }

        if (!data.data?.product || !data.data?.reviews) {
          throw new Error('Product or reviews data is missing');
        }

        setProduct(data.data.product);
        setReviews(data.data.reviews);

        setIsLoading(false);
      } catch (error) {
        console.error(`[Reviews Component] Error fetching data:`, error);
        setError("Failed to load reviews");
        setIsLoading(false);
      }
    };

    fetchData();
  }, [productId]);

  const productCardOptions = {
    showLatestReview: false,
    size: "rating-sm",
    showWriteReview: true,
    showClaimThisProduct: true,
  };

  if (isLoading) return <LoadingSpinner />;
  if (error) return <p>{error}</p>;
  if (!product) return <p>Product not found</p>;

  // Generate breadcrumb items
  const breadcrumbItems = [
    { label: "Home", href: "/" },
    { label: "Products", href: "/products" },
    { label: product.name, href: `/reviews?id=${productId}` },
  ];

  // Generate share metadata for the product
  const shareMetadata = product
    ? generateShareMetadata({
      title: `${product.name} | ${reviews.length} Reviews`,
      description: product.description,
      url: typeof window !== "undefined" ? window.location.href : "",
      imageUrl: product.display_image,
      rating: product.rating,
      reviewCount: reviews.length,
    })
    : null;

  // No additional functions needed

  if (reviews.length === 0) {
    return (
      <div className="flex flex-col w-full h-full p-2 sm:pt-8">
        <Breadcrumb items={breadcrumbItems} />
        <div className="w-full flex justify-center items-center">
          <ProductCard
            options={productCardOptions}
            reviews={reviews}
            product={product}
            currentUserId={userId ? userId : null}
            useProductLink={true}
          />
        </div>
        <ProductShowcase product={product} />
        <div className="w-full text-center mt-4 space-y-3">
          <div className="flex flex-col sm:flex-row justify-center gap-3">
            <Link
              href={`/cr?id=${productId}&rating=3`}
              className="inline-flex items-center px-6 py-3 text-base font-medium text-white bg-myTheme-primary hover:bg-myTheme-primary/90 rounded-lg transition-colors duration-200"
            >
              Be the first to review this product
            </Link>
            <Link
              href={`/product/${productId}`}
              className="inline-flex items-center px-6 py-3 text-base font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
            >
              <Eye className="mr-2 h-5 w-5" />
              View Details
            </Link>
          </div>
        </div>
        <div className="w-full max-w-4xl mx-auto">
          <div className="flex flex-col lg:flex-row w-full gap-4">
            <div className="w-full lg:w-1/2">
              <CompanyActivityCard product={product} reviews={reviews} />
            </div>
            <div className="w-full lg:w-1/2">
              <RatingDistribution reviews={reviews} />
            </div>
          </div>
        </div>
        <div className="w-full max-w-4xl mx-auto">
          <FAQSection
            product={product}
            category={product.tags[0]}
            productName={product.name}
          />
        </div>
        <Schema
          productName={product.name}
          description={product.description}
          rating={0}
          reviewCount={0}
          reviews={[]}
          image={product.display_image}
          category={product.tags[0]}
          sku={product.id}
          brand={product.business?.ownerName || product.name}
        />
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center w-full h-full p-2 sm:pt-8">
      <div className="w-full max-w-4xl mx-auto">
        <Breadcrumb items={breadcrumbItems} />
        <div className="w-full flex justify-center mb-4">
          <ProductCard
            options={productCardOptions}
            reviews={reviews}
            product={product}
            currentUserId={userId ? userId : null}
            useProductLink={true}
          />
        </div>
        <ProductShowcase product={product} />
      </div>
      <div className="w-full max-w-4xl mx-auto">
        <div className="flex flex-col lg:flex-row w-full gap-4">
          <div className="w-full lg:w-1/2">
            <CompanyActivityCard product={product} reviews={reviews} />
          </div>
          <div className="w-full lg:w-1/2">
            <RatingDistribution reviews={reviews} />
          </div>
        </div>
      </div>
      <div className="w-full max-w-4xl mx-auto">
        <div className="flex flex-col w-full justify-center items-center gap-4 my-4 py-4 bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl border border-gray-100">
          <div className="flex flex-col sm:flex-row w-full justify-center items-center gap-4">
            <WriteAReview hideRecentReviews={true} />
          </div>
          {shareMetadata && (
            <div className="flex justify-center">
              <ShareButtonWrapper
                metadata={shareMetadata}
                className="w-full sm:w-auto"
              />
            </div>
          )}
        </div>
      </div>
      <div className="w-full max-w-4xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {reviews.map((review: iReview) => (
            <ReviewCard key={review.id} review={review} />
          ))}
        </div>
      </div>
      <div className="w-full max-w-4xl mx-auto mt-8">
        <FAQSection
          product={product}
          category={product.tags[0]}
          productName={product.name}
        />
      </div>
      <Schema
        productName={product.name}
        description={product.description}
        rating={calculateAverageRating(reviews)}
        reviewCount={reviews.length}
        reviews={reviews.map((review) => ({
          author: review.createdBy || "Anonymous",
          reviewRating: review.rating,
          reviewBody: review.body,
          datePublished:
            review.createdDate?.toString() || new Date().toString(),
        }))}
        image={product.display_image}
        category={product.tags[0]}
        sku={product.id}
        brand={product.business?.ownerName || product.name}
      />
    </div>
  );
};

// Helper function to calculate average rating
const calculateAverageRating = (reviews: iReview[]): number => {
  if (reviews.length === 0) return 0;
  const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
  return sum / reviews.length;
};

export default Reviews;
