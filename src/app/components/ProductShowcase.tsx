import { iProduct } from "../util/Interfaces";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import ImageModal from "./ImageModal";

interface ProductShowcaseProps {
    product: iProduct;
}

const ProductShowcase = ({ product }: ProductShowcaseProps) => {
    const [selectedImage, setSelectedImage] = useState<string | null>(null);

    const hasContent = product.images.length > 0 ||
        product.videos.length > 0 ||
        product.links.length > 0 ||
        product.website.length > 0;

    if (!hasContent) return null;

    return (
        <>
            <div className="w-full max-w-4xl mx-auto mt-4 sm:mt-8 p-3 sm:p-4 bg-white rounded-lg shadow-md">
                <h2 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6">Product Showcase</h2>

                {/* Images Section */}
                {product.images.length > 0 && (
                    <div className="mb-6 sm:mb-8">
                        <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Images</h3>
                        <div className="grid grid-cols-4 sm:grid-cols-5 lg:grid-cols-6 gap-1 sm:gap-2">
                            {product.images.map((image, index) => (
                                <div
                                    key={index}
                                    className="relative aspect-square rounded-lg overflow-hidden cursor-pointer shadow-sm hover:shadow-md transition-shadow duration-200 max-w-[80px] sm:max-w-[90px] mx-auto bg-gray-100"
                                    onClick={() => setSelectedImage(image)}
                                >
                                    <img
                                        src={image}
                                        alt={`${product.name} image ${index + 1}`}
                                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                                        sizes="(max-width: 640px) 45vw, (max-width: 1024px) 30vw, 25vw"
                                    />
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* Videos Section */}
                {product.videos.length > 0 && (
                    <div className="mb-6 sm:mb-8">
                        <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Videos</h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                            {product.videos.map((video, index) => (
                                <div key={index} className="relative aspect-video rounded-lg overflow-hidden shadow-sm">
                                    <iframe
                                        src={video}
                                        className="absolute inset-0 w-full h-full"
                                        allowFullScreen
                                        title={`${product.name} video ${index + 1}`}
                                    />
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* Links Section */}
                {(product.links.length > 0 || product.website.length > 0) && (
                    <div>
                        <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Related Links</h3>
                        <div className="space-y-2 sm:space-y-3">
                            {product.website.map((site, index) => (
                                <div key={`website-${index}`} className="flex items-center">
                                    <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                    </svg>
                                    <Link
                                        href={site.startsWith('http') ? site : `https://${site}`}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800 hover:underline text-sm sm:text-base break-all"
                                    >
                                        {site}
                                    </Link>
                                </div>
                            ))}
                            {product.links.map((link, index) => (
                                <div key={`link-${index}`} className="flex items-center">
                                    <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                    </svg>
                                    <Link
                                        href={link.startsWith('http') ? link : `https://${link}`}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800 hover:underline text-sm sm:text-base break-all"
                                    >
                                        {link}
                                    </Link>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>

            {/* Image Modal */}
            {selectedImage && (
                <ImageModal
                    src={selectedImage}
                    alt={product.name}
                    onClose={() => setSelectedImage(null)}
                />
            )}
        </>
    );
};

export default ProductShowcase; 