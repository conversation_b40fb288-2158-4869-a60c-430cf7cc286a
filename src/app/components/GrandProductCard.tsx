'use client'
import React from 'react'
import Image from 'next/image'
import { iProduct } from '@/app/util/Interfaces'
import { Star, MapPin, Phone, Globe, Clock, Tag } from 'lucide-react'

interface GrandProductCardProps {
    productId: string
    productData: iProduct | null
}

export default function GrandProductCard({ productId, productData }: GrandProductCardProps) {
    if (!productData) {
        return (
            <div className="bg-white p-8 rounded-lg shadow-lg mb-8 text-center">
                <h2 className="text-2xl font-semibold text-gray-700">Product information is currently unavailable.</h2>
                <p className="text-gray-500">We couldn&apos;t load the details for product ID: {productId}</p>
            </div>
        );
    }

    const {
        name,
        description,
        display_image,
        images,
        rating,
        address,
        telephone,
        website,
        openingHrs,
        closingHrs,
        openingDays,
        tags,
        _count
    } = productData;

    const totalReviews = _count?.reviews || 0;

    return (
        <div className="bg-white p-6 md:p-8 rounded-xl shadow-xl mb-10 border border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
                {/* Image Section */}
                <div className="md:col-span-1 flex justify-center items-start">
                    {display_image ? (
                        <Image
                            src={display_image}
                            alt={name || 'Product image'}
                            width={300}
                            height={300}
                            className="rounded-lg object-cover w-full h-auto max-h-80 shadow-md"
                        />
                    ) : (
                        <div className="w-full h-60 bg-gray-200 rounded-lg flex items-center justify-center text-gray-500">
                            No Image Available
                        </div>
                    )}
                </div>

                {/* Details Section */}
                <div className="md:col-span-2">
                    <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-3">{name}</h1>

                    {rating > 0 && (
                        <div className="flex items-center mb-4">
                            <div className="flex items-center">
                                {[...Array(5)].map((_, i) => (
                                    <Star
                                        key={i}
                                        className={`h-6 w-6 ${i < Math.round(rating) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
                                    />
                                ))}
                            </div>
                            <span className="ml-2 text-lg text-gray-600">{rating.toFixed(1)} ({totalReviews} review{totalReviews !== 1 ? 's' : ''})</span>
                        </div>
                    )}

                    {description && <p className="text-gray-600 mb-5 text-md leading-relaxed">{description}</p>}

                    <div className="space-y-3 text-gray-700">
                        {address && (
                            <div className="flex items-center">
                                <MapPin className="h-5 w-5 mr-2 text-myTheme-primary" />
                                <span>{address}</span>
                            </div>
                        )}
                        {telephone && (
                            <div className="flex items-center">
                                <Phone className="h-5 w-5 mr-2 text-myTheme-primary" />
                                <span>{telephone}</span>
                            </div>
                        )}
                        {website && website.length > 0 && (
                            <div className="flex items-center">
                                <Globe className="h-5 w-5 mr-2 text-myTheme-primary" />
                                <a href={website[0]} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                                    {website[0]}
                                </a>
                            </div>
                        )}
                        {openingHrs && closingHrs && openingDays && openingDays.length > 0 && (
                            <div className="flex items-center">
                                <Clock className="h-5 w-5 mr-2 text-myTheme-primary" />
                                <span>Open: {openingDays.join(', ')} from {openingHrs} - {closingHrs}</span>
                            </div>
                        )}
                    </div>

                    {tags && tags.length > 0 && (
                        <div className="mt-6">
                            <h3 className="text-sm font-semibold text-gray-500 mb-2 flex items-center">
                                <Tag className="h-4 w-4 mr-1.5" /> Tags
                            </h3>
                            <div className="flex flex-wrap gap-2">
                                {tags.map(tag => (
                                    <span key={tag} className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full shadow-sm">
                                        {tag}
                                    </span>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Additional Images Gallery (Optional) */}
            {images && images.length > 1 && (
                <div className="mt-8 pt-6 border-t border-gray-200">
                    <h3 className="text-xl font-semibold text-gray-700 mb-4">More Images</h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                        {images.slice(1, 6).map((img, index) => (
                            <div key={index} className="aspect-square relative rounded-md overflow-hidden shadow-sm hover:shadow-lg transition-shadow">
                                <Image
                                    src={img}
                                    alt={`${name || 'Product'} image ${index + 1}`}
                                    layout="fill"
                                    objectFit="cover"
                                />
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    )
} 