"use client";
import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from "@clerk/nextjs";
import { Button } from '@/components/ui/button';
import { LogIn } from 'lucide-react';
import Link from 'next/link';

const ClaimProductComponent = ({ product }: { product: any }) => {
  const { isSignedIn } = useAuth();
  const router = useRouter();

  // Debug the product object
  console.log('Product in ClaimProductComponent:', product);
  console.log('Product ID:', product?.id);

  if (!isSignedIn) {
    return (
      <Link href="https://accounts.reviewit.gy/sign-in" className="w-full">
        <Button variant="outline" className="w-full bg-white hover:bg-gray-100 flex items-center justify-center gap-2">
          <LogIn className="h-4 w-4" />
          Sign in to claim
        </Button>
      </Link>
    );
  }

  const handleClaimProduct = () => {
    // Store the product ID in localStorage for the claim form to use
    console.log('Setting product ID in localStorage:', product.id);
    localStorage.setItem('claimProductId', product.id);
    // Redirect to the claim form page
    router.push('/claim-product');
  };

  return (
    <Button
      variant="outline"
      className="w-full bg-white hover:bg-gray-100 flex items-center justify-center gap-2 text-myTheme-primary"
      onClick={handleClaimProduct}
    >
      Claim This Product
    </Button>
  );
};

export default ClaimProductComponent;
