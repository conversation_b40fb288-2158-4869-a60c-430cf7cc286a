import React, { useEffect, useState } from "react";
import Image from "next/legacy/image";
import Link from "next/link";
import { iReview, iVoteCount } from "../util/Interfaces";
import dayjs from "dayjs";
import DOMPurify from "dompurify";
import { useAtom } from "jotai";
import { currentReviewAtom } from "../store/store";
import { updateHelpfulVote, removeHelpfulVote } from "../util/serverFunctions";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useUser, useAuth } from "@clerk/nextjs";
import { MdOutlineThumbUp } from "react-icons/md";
import { toast } from "sonner";
import ImageGallery from "./ImageGallery";
import VideoEmbed from "./VideoEmbed";
import ReviewStats from "./ReviewStats";
import { ReadOnlyRating } from "./RatingSystem";
import { MessageSquare } from "lucide-react";
import { generateShareMetadata } from '../lib/shareUtils';
import { ShareButtonWrapper } from './ShareButtonWrapper';
import { stripSpecificHtmlTags } from '../util/helpers';
import ReportButton from './ReportButton';

interface ReviewCardProps {
  review: iReview;
  showFullContent?: boolean;
}

interface ThumbsUpSectionProps {
  review: iReview;
  hasUserLiked: boolean;
  hideButton: boolean;
  auth: ReturnType<typeof useAuth>;
  handleHelpfulClick: () => void;
  voteCount: iVoteCount | null;
  isLoading?: boolean;
  isAuthor: boolean;
}

const ThumbsUpSection = ({ hasUserLiked, auth, handleHelpfulClick, voteCount, isLoading, isAuthor }: ThumbsUpSectionProps) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle null voteCount case
  const voteCountValue = voteCount?.helpfulVotes ?? 0;

  if (!mounted) {
    return (
      <span className="flex items-center gap-1.5 text-myTheme-primary px-2 py-1 bg-myTheme-primary/10 rounded-full text-sm">
        <MdOutlineThumbUp className="text-base" />
        {voteCountValue}
      </span>
    );
  }

  if (isAuthor) {
    return (
      <span
        className="flex items-center gap-1.5 text-gray-400 px-2 py-1 bg-gray-100 rounded-full text-sm cursor-not-allowed"
        title="You cannot vote on your own review."
      >
        <MdOutlineThumbUp className="text-base" />
        {voteCountValue}
      </span>
    );
  }

  if (!auth.isSignedIn) {
    return (
      <span className="flex items-center gap-1.5 text-myTheme-primary px-2 py-1 bg-myTheme-primary/10 rounded-full text-sm">
        <MdOutlineThumbUp className="text-base" />
        {voteCountValue}
      </span>
    );
  }

  return (
    <button
      onClick={handleHelpfulClick}
      disabled={isLoading || isAuthor}
      className={`flex items-center gap-1.5 px-2 py-1 rounded-full text-sm transition-colors ${isAuthor
        ? "text-gray-400 bg-gray-100 cursor-not-allowed"
        : hasUserLiked
          ? "bg-green-100 hover:bg-green-200 text-green-700"
          : "text-myTheme-primary bg-myTheme-primary/10 hover:bg-myTheme-primary/20"
        } ${isLoading ? "opacity-50 cursor-not-allowed" : ""}`}
      aria-label={isAuthor ? "You cannot vote on your own review" : hasUserLiked ? "Remove helpful vote" : "Mark as helpful"}
      aria-disabled={isLoading || isAuthor}
    >
      <MdOutlineThumbUp className={`text-base ${isLoading ? "animate-pulse" : ""} ${hasUserLiked && !isAuthor ? "text-green-600" : isAuthor ? "text-gray-400" : "text-myTheme-primary"}`} />
      {voteCountValue}
    </button>
  );
};

const ReviewCard: React.FC<ReviewCardProps> = ({ review, showFullContent = false }) => {
  const {
    user,
    createdDate,
    title,
    body,
    rating,
    comments,
    voteCount,
    images,
  } = review;
  const auth = useAuth();
  const [reviewAtom, setReview] = useAtom(currentReviewAtom);
  const formattedBody = body.replace(/<p><\/p>/g, "<br>");
  const queryClient = useQueryClient();
  const [hideButton, setHideButton] = React.useState(false);
  const userData = useUser();
  const userInDbId = userData.user?.publicMetadata.id as unknown as string;
  const isAuthor = !!(review.userId === userInDbId && auth.isSignedIn);

  // Generate metadata for sharing
  const metadata = generateShareMetadata({
    title: `${title} - Review of ${review.product?.name} | Rating: ${rating}/5`,
    description: `${stripSpecificHtmlTags(body).substring(0, 100)}... | Review by @${review.user?.userName}`,
    imageUrl: review.product?.display_image,
    url: `/fr?id=${review.id}&productid=${review.productId}`,
    rating: rating,
    reviewCount: 1,
  });

  const likedData = review.likedBy || reviewAtom?.likedBy || [];
  const hasUserLiked = likedData.some((user) => user.id === userInDbId);

  // Create a local state for optimistic updates
  const [localVoteCount, setLocalVoteCount] = useState<number>(voteCount?.helpfulVotes || 0);
  const [localHasUserLiked, setLocalHasUserLiked] = useState<boolean>(hasUserLiked);

  // Update local state when props change
  useEffect(() => {
    setLocalVoteCount(voteCount?.helpfulVotes ?? 0);
    setLocalHasUserLiked(hasUserLiked);
  }, [voteCount?.helpfulVotes, hasUserLiked]);

  const mutation = useMutation({
    mutationFn: async () => {
      if (!review.id || !userInDbId) {
        throw new Error("Missing required data for vote operation");
      }
      if (!voteCount) {
        throw new Error("Vote count data is missing");
      }

      try {
        // Use hasUserLiked from props, not the local state which has already been updated optimistically
        if (hasUserLiked) {
          await removeHelpfulVote({ reviewId: review.id, userInDbId });
        } else {
          await updateHelpfulVote({ reviewId: review.id, userInDbId });
        }
      } catch (error) {
        console.error("Vote operation failed:", error);
        throw error;
      }
    },
    onSuccess: () => {
      // Use hasUserLiked from props for the toast message
      toast.success(hasUserLiked ? "Vote removed successfully!" : "Like saved successfully!");
      setHideButton(!hasUserLiked);

      // The optimistic update has already been applied, so we don't need to update the state again
      // But we can ensure the query cache is updated with the latest state
      if (reviewAtom) {
        // The query cache should already be updated from the optimistic update
        // This is just a safeguard
        queryClient.setQueryData([review.id], reviewAtom);
      }
    },
    onMutate: async () => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: [review.id] });

      // Snapshot the previous value
      const previousReview = queryClient.getQueryData([review.id]);
      const previousVoteCount = localVoteCount;
      const previousHasUserLiked = localHasUserLiked;

      // Store the current state before we change it
      const isCurrentlyLiked = localHasUserLiked;

      // Optimistically update local state
      if (isCurrentlyLiked) {
        setLocalVoteCount((prev) => (prev !== undefined ? prev - 1 : 0));
        setLocalHasUserLiked(false);
      } else {
        setLocalVoteCount((prev) => (prev !== undefined ? prev + 1 : 1));
        setLocalHasUserLiked(true);
      }

      // Optimistically update the review atom and cache
      if (reviewAtom) {
        const optimisticReview = { ...reviewAtom };

        // Create voteCount object if it doesn't exist
        if (!optimisticReview.voteCount) {
          optimisticReview.voteCount = {
            id: voteCount?.id || '',
            reviewId: review.id || '',
            helpfulVotes: 0,
            unhelpfulVotes: voteCount?.unhelpfulVotes || 0,
            review: review
          };
        }

        if (isCurrentlyLiked) {
          // If currently liked, we're removing the like
          optimisticReview.voteCount.helpfulVotes! -= 1;
          optimisticReview.likedBy = optimisticReview.likedBy?.filter(user => user.id !== userInDbId);
        } else {
          // If not currently liked, we're adding a like
          optimisticReview.voteCount.helpfulVotes! += 1;
          optimisticReview.likedBy = [...(optimisticReview.likedBy || []), { id: userInDbId } as any];
        }

        // Update both the atom and the cache
        setReview(optimisticReview);
        queryClient.setQueryData([review.id], optimisticReview);
      }

      // Return a context object with the snapshotted values
      return { previousReview, previousVoteCount, previousHasUserLiked };
    },
    onError: (err, _variables, context) => {
      const errorMessage = err instanceof Error ? err.message : "An unexpected error occurred";
      toast.error(localHasUserLiked ? `Failed to remove vote: ${errorMessage}` : `Like failed: ${errorMessage}`);

      // Revert to the previous state
      if (context) {
        setLocalVoteCount(context.previousVoteCount);
        setLocalHasUserLiked(context.previousHasUserLiked);

        if (context.previousReview) {
          setReview(context.previousReview as iReview);
          queryClient.setQueryData([review.id], context.previousReview);
        }
      }
    },
  });

  const handleHelpfulClick = () => {
    if (mutation.isPending || isAuthor) return;
    mutation.mutate();
  };

  return (
    <div className="bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 group flex flex-col">
      <div className="flex flex-col p-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-2">
          <Link href={`/userprofile/${review.user?.id}`} className="flex items-center w-full sm:w-auto">
            <Image
              src={user?.avatar || "/logo.png"}
              alt={user?.id!}
              width={40}
              height={40}
              className="rounded-full object-cover w-10 h-10 ring-2 ring-myTheme-primary/10"
            />
            <div className="ml-2">
              <p className="font-medium text-gray-900 group-hover:text-myTheme-primary transition-colors text-sm">@{user?.userName}</p>
              <div className="flex items-center">
                <ReadOnlyRating
                  name={review.id!}
                  rating={rating}
                  size="sm"
                />
              </div>
              <div className="text-xs text-gray-500">
                {dayjs(createdDate?.toString()).format("MMM D, YYYY")}
              </div>
            </div>
          </Link>
          <div className="mt-2 sm:mt-0">
            <ReviewStats review={review} setReview={() => { setReview(review) }} />
            <ReportButton
              reviewId={review.id!}
              authorId={review.userId}
              className="mt-1"
              onReport={(reportId) => {
                toast.success('Review reported successfully');
              }}
            />
          </div>
        </div>

        <Link
          href={`/fr/?id=${review.id}&productid=${review.productId}`}
          onClick={() => setReview(review)}
          className="block mb-4 group-hover:text-myTheme-primary transition-colors"
        >
          <h2 className="text-xl font-bold text-gray-900 group-hover:text-myTheme-primary transition-colors">{title}</h2>
        </Link>
        <div className="flex-1">
          <div
            className={`text-xs text-gray-600 leading-relaxed mb-2 ${showFullContent ? '' : 'line-clamp-4'}`}
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(showFullContent ? formattedBody : formattedBody.substring(0, 200) + (formattedBody.length > 200 ? '...' : '')),
            }}
          />
          <div className="mb-2">
            {images && images.length > 0 && (
              <ImageGallery images={images} />
            )}
          </div>
          <div className="mb-2">
            {review.videos && review.videos[0] && (
              <VideoEmbed url={review.videos[0]} />
            )}
          </div>
        </div>
        <div className="flex items-center justify-between pt-2 border-t border-gray-100 mt-auto">
          <div className="flex items-center space-x-2">
            <ThumbsUpSection
              review={review}
              hasUserLiked={localHasUserLiked}
              hideButton={hideButton || mutation.isPending}
              auth={auth}
              handleHelpfulClick={handleHelpfulClick}
              voteCount={localVoteCount !== undefined ? {
                helpfulVotes: localVoteCount,
                id: voteCount?.id || '',
                reviewId: review.id || '',
                unhelpfulVotes: voteCount?.unhelpfulVotes || 0,
                review: review
              } : voteCount || null}
              isLoading={mutation.isPending}
              isAuthor={isAuthor}
            />
          </div>
          <div className="flex items-center gap-2">
            <Link href={`/fr?id=${review.id}&productid=${review.productId}`} onClick={() => setReview(review)}>
              <span className="text-xs text-myTheme-primary hover:text-myTheme-primary/80 transition-colors flex items-center gap-1">
                <MessageSquare size={14} />
                {comments && comments.length > 0
                  ? `${comments.length}`
                  : "Add"}
              </span>
            </Link>
            <ShareButtonWrapper metadata={metadata} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewCard;
