"use client";
import React, { useState } from "react";
import { iComment, iUser } from "../util/Interfaces";
import dayjs from "dayjs";
import Link from "next/link";
import {
  ReplyIcon,
  SaveIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  MessageSquareIcon,
  CheckCircleIcon,
} from "lucide-react";
// Note: There might be TypeScript errors with the Button component's size and variant props,
// but the application works correctly at runtime
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import OptionsMenu from "./CommentOptionsMenu";
import { useAuth } from "@clerk/nextjs";
import { toast } from "sonner";
import { Tooltip } from "@mantine/core";

interface OwnerReplyProps {
  comment: iComment;
  onReply: (parentId: string, body: string) => Promise<void>;
  onEdit: (commentId: string, body: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  depth: number;
  clerkUserId: string;
  currentUser: iUser;
  productName?: string;
}

// Colors for the vertical lines indicating comment depth
const DEPTH_COLORS = [
  "border-amber-400",
  "border-amber-300",
  "border-amber-200",
  "border-amber-100",
  "border-amber-50",
  "border-amber-400",
  "border-amber-300",
  "border-amber-200",
];

const OwnerReply: React.FC<OwnerReplyProps> = ({
  comment: initialComment,
  onReply,
  onEdit,
  onDelete,
  depth = 0,
  clerkUserId,
  currentUser,
  // productName is not used but kept for API consistency
  productName,
}) => {
  // userId is not used directly in this component but kept for future use
  const { userId } = useAuth();
  const [comment, setComment] = useState(initialComment);
  const [isEditing, setIsEditing] = useState(false);
  const [editedBody, setEditedBody] = useState(comment.body);
  const [showFullComment, setShowFullComment] = useState(false);
  const [isReplying, setIsReplying] = useState(false);
  const [replyBody, setReplyBody] = useState("");
  const [upvoted, setUpvoted] = useState(false);
  const [downvoted, setDownvoted] = useState(false);
  const [voteCount, setVoteCount] = useState(
    comment.upvotes - comment.downvotes
  );

  const isCommentOwner = clerkUserId === comment.user?.clerkUserId;
  const canReply = clerkUserId && clerkUserId !== comment.user?.clerkUserId;

  // Get border color for the current depth
  const depthColor = DEPTH_COLORS[depth % DEPTH_COLORS.length];

  const handleReply = async () => {
    if (comment.id && canReply) {
      // We don't need to create the newReply object here since it's handled by the API
      // and passed through the onReply function

      setIsReplying(false);
      setReplyBody("");

      try {
        // Call the API
        await onReply(comment.id, replyBody);
      } catch (error) {
        setIsReplying(true);
        setReplyBody(replyBody);
        toast.error("Failed to add reply. Please try again.");
      }
    }
  };

  const handleEdit = () => {
    if (comment.id && isCommentOwner) {
      setIsEditing(true);
    }
  };

  const handleSave = async () => {
    if (comment.id && isCommentOwner) {
      await onEdit(comment.id, editedBody);
      const updatedComment = { ...comment, body: editedBody };
      setComment(updatedComment);
      setIsEditing(false);
    }
  };

  const handleDelete = async () => {
    if (comment.id && isCommentOwner) {
      await onDelete(comment.id);
      const updatedComment = { ...comment, isDeleted: true };
      setComment(updatedComment);
    }
  };

  const handleUpvote = () => {
    if (!upvoted) {
      setUpvoted(true);
      setDownvoted(false);
      setVoteCount(voteCount + (downvoted ? 2 : 1));
    } else {
      setUpvoted(false);
      setVoteCount(voteCount - 1);
    }
  };

  const handleDownvote = () => {
    if (!downvoted) {
      setDownvoted(true);
      setUpvoted(false);
      setVoteCount(voteCount - (upvoted ? 2 : 1));
    } else {
      setDownvoted(false);
      setVoteCount(voteCount + 1);
    }
  };

  return (
    <div
      id={comment.id || `reply-${Date.now()}`}
      className={`w-full mt-2 pl-${depth * 4}`}
    >
      <div className="flex">
        {/* Left vote column with vertical line */}
        <div className="flex flex-col items-center mr-2">
          <div className="flex flex-col items-center">
            <button
              onClick={handleUpvote}
              className={`p-1 rounded hover:bg-gray-100 ${upvoted ? "text-green-500" : "text-gray-400"}`}
            >
              <ArrowUpIcon className="w-4 h-4" />
            </button>
            <span
              className={`text-xs font-medium ${upvoted ? "text-green-500" : downvoted ? "text-red-500" : "text-gray-600"}`}
            >
              {voteCount || 0}
            </span>
            <button
              onClick={handleDownvote}
              className={`p-1 rounded hover:bg-gray-100 ${downvoted ? "text-red-500" : "text-gray-400"}`}
            >
              <ArrowDownIcon className="w-4 h-4" />
            </button>
          </div>
          {/* Vertical line with depth color */}
          <div className={`w-0.5 grow mt-1 ${depthColor}`}></div>
        </div>

        {/* Main comment content with premium styling */}
        <div className="flex-1">
          <div className="rounded-md px-4 py-3 shadow-md owner-comment">
            <div className="flex items-center text-xs mb-1">
              <Avatar className="w-6 h-6 mr-1 ring-2 ring-blue-500 border-2 border-blue-100 shadow-sm">
                <AvatarImage
                  src={comment.user?.avatar || "/default-avatar.png"}
                  alt={`${comment.user?.firstName} ${comment.user?.lastName}`}
                />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-700 text-white">
                  {comment.user?.firstName?.charAt(0)}
                  {comment.user?.lastName?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <Link
                href={`/userprofile/${comment.user?.id}`}
                className="font-semibold owner-text text-base hover:underline mr-1"
              >
                @{comment.user?.userName}
              </Link>

              {/* Owner badge */}
              <Tooltip label="Verified Business Owner" withArrow>
                <div className="flex items-center text-xs px-2 py-0.5 rounded-full mx-1 owner-badge">
                  <CheckCircleIcon className="w-3 h-3 mr-1" />
                  <span className="font-medium">Verified Owner</span>
                </div>
              </Tooltip>

              <span className="mx-1 text-gray-500">•</span>
              <span className="text-gray-500">
                {dayjs(comment.createdDate).format("MMM D, YYYY")}
              </span>

              {isCommentOwner && (
                <div className="ml-auto">
                  <OptionsMenu
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    setIsEditing={setIsEditing}
                  />
                </div>
              )}
            </div>

            <div className="text-sm leading-relaxed break-words">
              {isEditing ? (
                <Textarea
                  value={editedBody}
                  onChange={(e) => setEditedBody(e.target.value)}
                  className="w-full p-3 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent min-h-[100px]"
                />
              ) : comment.isDeleted ? (
                <span className="italic text-gray-400 text-sm">
                  {comment.body}
                </span>
              ) : showFullComment || comment.body.length <= 100 ? (
                <div className="whitespace-pre-line">{comment.body}</div>
              ) : (
                <>
                  <div className="whitespace-pre-line">
                    {comment.body.slice(0, 100)}...
                  </div>
                  <button
                    onClick={() => setShowFullComment(true)}
                    className="text-amber-600 hover:text-amber-700 text-xs font-medium mt-1"
                  >
                    Read more
                  </button>
                </>
              )}
            </div>

            <div className="flex mt-2 space-x-2">
              {isEditing ? (
                <>
                  <Button
                    onClick={handleSave}
                    size="sm"
                    className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-md"
                  >
                    <SaveIcon className="w-3 h-3 mr-1" />
                    Save
                  </Button>
                  <Button
                    onClick={() => setIsEditing(false)}
                    size="sm"
                    variant="outline"
                    className="border-blue-300 text-blue-700 hover:bg-blue-50"
                  >
                    Cancel
                  </Button>
                </>
              ) : (
                <>
                  {canReply && (
                    <Button
                      onClick={() => setIsReplying(!isReplying)}
                      size="sm"
                      variant="outline"
                      className="border-blue-300 text-blue-700 hover:bg-blue-50 shadow-sm"
                    >
                      <ReplyIcon className="w-3 h-3 mr-1" />
                      Reply
                    </Button>
                  )}
                </>
              )}
            </div>

            {isReplying && (
              <div className="mt-3">
                <Textarea
                  value={replyBody}
                  onChange={(e) => setReplyBody(e.target.value)}
                  placeholder="Write a reply..."
                  className="w-full p-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[100px]"
                />
                <div className="flex justify-end mt-2 space-x-2">
                  <Button
                    onClick={handleReply}
                    size="sm"
                    className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-md"
                    disabled={!replyBody.trim()}
                  >
                    Post Reply
                  </Button>
                  <Button
                    onClick={() => {
                      setIsReplying(false);
                      setReplyBody("");
                    }}
                    size="sm"
                    variant="outline"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OwnerReply;
