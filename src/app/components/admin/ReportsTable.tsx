'use client';

import { useState, useEffect, useCallback } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from '@/components/ui/table';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Flag, MoreHorizontal, ChevronDown, ChevronUp, AlertCircle, Clock, CheckCircle2 } from 'lucide-react';
import { iReviewReport } from '@/app/util/Interfaces';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistance, format } from 'date-fns';
import { Skeleton } from '@/components/ui/skeleton';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
    Pagination,
    PaginationContent,
    Pa<PERSON>ationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import ReportDetails from './ReportDetails';
import { toast } from 'sonner';

interface ReportsTableProps {
    initialReports?: iReviewReport[];
    onStatusChange?: (reportId: string, status: 'PENDING' | 'REVIEWED' | 'RESOLVED', reviewAction?: { action: string, reason?: string }) => Promise<void>;
    onViewDetails: (reportId: string) => void;
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
}

export default function ReportsTable({
    initialReports,
    onStatusChange,
    onViewDetails,
    currentPage,
    totalPages,
    onPageChange,
}: ReportsTableProps) {
    const [reports, setReports] = useState<iReviewReport[]>([]);
    const [loading, setLoading] = useState(true);
    const [statusFilter, setStatusFilter] = useState<string>('all');
    const [sortBy, setSortBy] = useState<string>('createdAt');
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
    const [selectedReport, setSelectedReport] = useState<iReviewReport | null>(null);
    const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});
    const [totalPagesState, setTotalPagesState] = useState<number>(totalPages);

    const fetchReports = useCallback(async () => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                page: currentPage.toString(),
                limit: '10',
                sortBy,
                sortOrder
            });

            if (statusFilter !== 'all') {
                params.append('status', statusFilter);
            }

            const response = await fetch(`/api/admin/reports?${params.toString()}`);

            if (!response.ok) {
                throw new Error('Failed to fetch reports');
            }

            const data = await response.json();

            if (data.success) {
                setReports(data.data.reports);
                setTotalPagesState(data.data.totalPages);
            } else {
                throw new Error(data.error || 'Failed to fetch reports');
            }
        } catch (error) {
            console.error('Error fetching reports:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to fetch reports');
        } finally {
            setLoading(false);
        }
    }, [currentPage, sortBy, sortOrder, statusFilter, setLoading, setReports, setTotalPagesState]);

    useEffect(() => {
        if (initialReports) {
            setReports(initialReports);
            setLoading(false);
        } else {
            fetchReports();
        }
    }, [currentPage, statusFilter, sortBy, sortOrder, initialReports, fetchReports]);

    const handleSort = (field: string) => {
        if (sortBy === field) {
            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
        } else {
            setSortBy(field);
            setSortOrder('desc');
        }
    };

    const handleStatusChange = async (reportId: string, status: 'PENDING' | 'REVIEWED' | 'RESOLVED', reviewAction?: { action: string, reason?: string }) => {
        try {
            setLoadingStates(prev => ({ ...prev, [reportId]: true }));
            if (onStatusChange) {
                await onStatusChange(reportId, status, reviewAction);
            } else {
                const response = await fetch(`/api/admin/reports/${reportId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ status, reviewAction }),
                });

                if (!response.ok) {
                    throw new Error('Failed to update report status');
                }

                const data = await response.json();

                if (data.success) {
                    // Update the local state
                    setReports(reports.map(report =>
                        report.id === reportId ? { ...report, status } : report
                    ));

                    toast.success('Report status updated successfully');
                } else {
                    throw new Error(data.error || 'Failed to update report status');
                }
            }
        } catch (error) {
            console.error('Error updating report status:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to update report status');
        } finally {
            setLoadingStates(prev => ({ ...prev, [reportId]: false }));
        }
    };

    const handleViewDetails = async (reportId: string) => {
        try {
            const response = await fetch(`/api/admin/reports/${reportId}`);
            if (!response.ok) {
                throw new Error('Failed to fetch report details');
            }

            const data = await response.json();
            if (data.success) {
                setSelectedReport(data.data);
            } else {
                throw new Error(data.error || 'Failed to fetch report details');
            }
        } catch (error) {
            console.error('Error fetching report details:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to fetch report details');
        }
    };

    const getStatusBadge = (status: 'PENDING' | 'REVIEWED' | 'RESOLVED') => {
        switch (status) {
            case 'PENDING':
                return (
                    <Badge variant="outline" className="flex gap-1 items-center text-amber-600 border-amber-200 bg-amber-50">
                        <Clock className="h-3 w-3" />
                        Pending
                    </Badge>
                );
            case 'REVIEWED':
                return (
                    <Badge variant="outline" className="flex gap-1 items-center text-blue-600 border-blue-200 bg-blue-50">
                        <AlertCircle className="h-3 w-3" />
                        Reviewed
                    </Badge>
                );
            case 'RESOLVED':
                return (
                    <Badge variant="outline" className="flex gap-1 items-center text-green-600 border-green-200 bg-green-50">
                        <CheckCircle2 className="h-3 w-3" />
                        Resolved
                    </Badge>
                );
            default:
                return (
                    <Badge variant="outline">{status}</Badge>
                );
        }
    };

    return (
        <div className="space-y-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                    <Flag className="h-5 w-5 text-destructive" />
                    Review Reports
                </h2>

                <div className="flex gap-2 flex-wrap">
                    <Select
                        value={statusFilter}
                        onValueChange={setStatusFilter}
                    >
                        <SelectTrigger className="w-32">
                            <SelectValue placeholder="Filter by status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="PENDING">Pending</SelectItem>
                            <SelectItem value="REVIEWED">Reviewed</SelectItem>
                            <SelectItem value="RESOLVED">Resolved</SelectItem>
                        </SelectContent>
                    </Select>

                    <Button variant="outline" size="sm" onClick={fetchReports}>
                        Refresh
                    </Button>
                </div>
            </div>

            {/* Desktop Table View */}
            <div className="border rounded-lg hidden md:block">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[250px]">
                                <button
                                    className="flex items-center gap-1 hover:text-primary"
                                    onClick={() => handleSort('createdAt')}
                                >
                                    Reported By
                                    {sortBy === 'createdAt' && (
                                        sortOrder === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                                    )}
                                </button>
                            </TableHead>
                            <TableHead>
                                <button
                                    className="flex items-center gap-1 hover:text-primary"
                                    onClick={() => handleSort('review.title')}
                                >
                                    Review
                                    {sortBy === 'review.title' && (
                                        sortOrder === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                                    )}
                                </button>
                            </TableHead>
                            <TableHead className="w-[120px]">
                                <button
                                    className="flex items-center gap-1 hover:text-primary"
                                    onClick={() => handleSort('status')}
                                >
                                    Status
                                    {sortBy === 'status' && (
                                        sortOrder === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                                    )}
                                </button>
                            </TableHead>
                            <TableHead className="w-[100px] text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {loading ? (
                            Array.from({ length: 5 }).map((_, i) => (
                                <TableRow key={i}>
                                    <TableCell>
                                        <div className="flex items-center gap-2">
                                            <Skeleton className="h-8 w-8 rounded-full" />
                                            <div className="space-y-1">
                                                <Skeleton className="h-4 w-24" />
                                                <Skeleton className="h-3 w-16" />
                                            </div>
                                        </div>
                                    </TableCell>
                                    <TableCell><Skeleton className="h-4 w-36" /></TableCell>
                                    <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                                    <TableCell><Skeleton className="h-8 w-16 ml-auto" /></TableCell>
                                </TableRow>
                            ))
                        ) : reports.length === 0 ? (
                            <TableRow>
                                <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                                    No reports found
                                </TableCell>
                            </TableRow>
                        ) : reports.map(report => (
                            <TableRow key={report.id}>
                                <TableCell>
                                    <div className="flex items-center gap-2">
                                        <Avatar className="h-8 w-8">
                                            <AvatarImage src={report.user?.avatar || ''} alt={`${report.user?.userName}&apos;s avatar`} />
                                            <AvatarFallback>{report.user?.firstName?.charAt(0)}{report.user?.lastName?.charAt(0)}</AvatarFallback>
                                        </Avatar>
                                        <div>
                                            <p className="text-sm font-medium">@{report.user?.userName}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {report.createdAt && formatDistance(new Date(report.createdAt), new Date(), { addSuffix: true })}
                                            </p>
                                        </div>
                                    </div>
                                </TableCell>
                                <TableCell>
                                    <div className="max-w-md">
                                        <p className="text-sm font-medium truncate">{report.review?.title}</p>
                                        <p className="text-xs text-muted-foreground line-clamp-1">
                                            Reason: {report.reason}
                                        </p>
                                    </div>
                                </TableCell>
                                <TableCell>
                                    {getStatusBadge(report.status)}
                                </TableCell>
                                <TableCell className="text-right">
                                    <div className="flex justify-end gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleViewDetails(report.id)}
                                        >
                                            View
                                        </Button>
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden space-y-4">
                {loading ? (
                    Array.from({ length: 5 }).map((_, i) => (
                        <div key={i} className="border rounded-lg p-4">
                            <div className="flex items-start gap-3 mb-3">
                                <Skeleton className="h-10 w-10 rounded-full" />
                                <div className="flex-1 space-y-2">
                                    <Skeleton className="h-4 w-24" />
                                    <Skeleton className="h-3 w-16" />
                                </div>
                                <Skeleton className="h-6 w-20" />
                            </div>
                            <Skeleton className="h-4 w-full mb-2" />
                            <Skeleton className="h-3 w-3/4 mb-3" />
                            <Skeleton className="h-8 w-16 ml-auto" />
                        </div>
                    ))
                ) : reports.length === 0 ? (
                    <div className="border rounded-lg p-8 text-center text-muted-foreground">
                        <Flag className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No reports found</p>
                    </div>
                ) : reports.map(report => (
                    <div key={report.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-start gap-3 mb-3">
                            <Avatar className="h-10 w-10">
                                <AvatarImage src={report.user?.avatar || ''} alt={`${report.user?.userName}&apos;s avatar`} />
                                <AvatarFallback>{report.user?.firstName?.charAt(0)}{report.user?.lastName?.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium">@{report.user?.userName}</p>
                                <p className="text-xs text-muted-foreground">
                                    {report.createdAt && formatDistance(new Date(report.createdAt), new Date(), { addSuffix: true })}
                                </p>
                            </div>
                            {getStatusBadge(report.status)}
                        </div>
                        
                        <div className="mb-3">
                            <p className="text-sm font-medium mb-1">{report.review?.title}</p>
                            <p className="text-xs text-muted-foreground">
                                <span className="font-medium">Reason:</span> {report.reason}
                            </p>
                        </div>
                        
                        <div className="flex justify-end">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewDetails(report.id)}
                                className="w-full sm:w-auto"
                            >
                                View Details
                            </Button>
                        </div>
                    </div>
                ))}
            </div>

            {totalPagesState > 1 && (
                <Pagination className="mt-4">
                    <PaginationContent>
                        <PaginationItem>
                            <PaginationPrevious
                                href="#"
                                onClick={(e) => {
                                    e.preventDefault();
                                    if (currentPage > 1) onPageChange(currentPage - 1);
                                }}
                                className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
                            />
                        </PaginationItem>

                        {Array.from({ length: totalPagesState }, (_, i) => i + 1).map((page) => (
                            <PaginationItem key={page}>
                                <PaginationLink
                                    href="#"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        onPageChange(page);
                                    }}
                                    isActive={currentPage === page}
                                >
                                    {page}
                                </PaginationLink>
                            </PaginationItem>
                        ))}

                        <PaginationItem>
                            <PaginationNext
                                href="#"
                                onClick={(e) => {
                                    e.preventDefault();
                                    if (currentPage < totalPagesState) onPageChange(currentPage + 1);
                                }}
                                className={currentPage === totalPagesState ? 'pointer-events-none opacity-50' : ''}
                            />
                        </PaginationItem>
                    </PaginationContent>
                </Pagination>
            )}

            {selectedReport && (
                <ReportDetails
                    report={selectedReport}
                    isOpen={!!selectedReport}
                    onClose={() => setSelectedReport(null)}
                    onStatusUpdate={async (status: 'PENDING' | 'REVIEWED' | 'RESOLVED', notes, reviewAction) => {
                        try {
                            const response = await fetch(`/api/admin/reports/${selectedReport.id}`, {
                                method: 'PUT',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({ status, notes, reviewAction }),
                            });

                            if (!response.ok) {
                                throw new Error('Failed to update report');
                            }

                            const data = await response.json();

                            if (data.success) {
                                // Update the local state
                                setReports(reports.map(report =>
                                    report.id === selectedReport.id ? { ...report, status, notes } : report
                                ));

                                toast.success('Report status updated successfully');

                                setSelectedReport(null);
                                fetchReports(); // Refresh the data
                            } else {
                                throw new Error(data.error || 'Failed to update report');
                            }
                        } catch (error) {
                            console.error('Error updating report:', error);
                            toast.error(error instanceof Error ? error.message : 'Failed to update report');
                        }
                    }}
                />
            )}
        </div>
    );
}