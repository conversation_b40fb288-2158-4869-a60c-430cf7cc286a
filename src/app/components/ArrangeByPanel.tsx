import { iProduct } from "../util/Interfaces";
import TagView from "@/app/components/TagView";
import { useEffect, useState } from "react";

const ArrangeByPanel = ({
  products,
  selectedRating,
  setSelectedRating,
  selectedTags,
  setSelectedTags,
  filteredProductsLength,
  availableTags,
}: {
  products: iProduct[];
  setSelectedRating: (rating: number | null) => void;
  selectedTags: string[];
  selectedRating: number | null;
  setSelectedTags: (tags: string[]) => void;
  filteredProductsLength: number;
  availableTags: string[];
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filterUniqueTags, setFilterUniqueTags] = useState<string[]>([]);

  const handleTagClick = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter((t) => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  const handleClearAll = () => {
    setSelectedTags([]);
    setSelectedRating(null);
    setSearchTerm("");
  };

  useEffect(() => {
    if (searchTerm.length > 0) {
      setFilterUniqueTags(
        availableTags.filter((tag) =>
          tag.toLowerCase().includes(searchTerm.toLowerCase()),
        ),
      );
    } else {
      setFilterUniqueTags(availableTags);
    }
  }, [searchTerm, availableTags]);

  return (
    <div className="flex flex-col h-full bg-myTheme-light rounded-2xl shadow-lg overflow-hidden border border-myTheme-lightbg">
      <div className="p-5 bg-myTheme-lightbg border-b border-myTheme-accent/10">
        <h2 className="text-xl font-bold text-myTheme-primary mb-5">
          Arrange by
        </h2>

        <div className="mb-6">
          <h3 className="text-lg font-medium text-myTheme-primary mb-3">Rating</h3>
          <div className="grid grid-cols-4 gap-2">
            {["Any", "3.0", "4.0", "4.5+"].map((rating, index) => (
              <button
                key={rating}
                className={`py-2 rounded-full transition-all duration-200 font-medium ${
                  (selectedRating === null && index === 0) ||
                  (selectedRating === 3 && index === 1) ||
                  (selectedRating === 4 && index === 2) ||
                  (selectedRating === 5 && index === 3)
                    ? "bg-myTheme-accent text-myTheme-light shadow-md"
                    : "bg-myTheme-light text-myTheme-lightTextBody hover:bg-myTheme-light/80 border border-myTheme-accent/20"
                }`}
                onClick={() =>
                  setSelectedRating(index === 0 ? null : index + 2)
                }
              >
                {rating}
              </button>
            ))}
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-medium text-myTheme-primary mb-3">
            Search Tags
          </h3>
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search tags..."
              className="w-full px-4 py-3 border border-myTheme-accent/20 rounded-full focus:outline-none focus:ring-2 focus:ring-myTheme-accent/30 focus:border-transparent text-myTheme-primary bg-myTheme-white placeholder-myTheme-lightTextBody/60"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm("")}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-myTheme-lightTextBody hover:text-myTheme-primary transition-colors"
              >
                ✕
              </button>
            )}
          </div>
        </div>

        {(searchTerm.length > 0 ||
          selectedRating !== null ||
          selectedTags.length > 0) && (
          <button
            className="px-4 py-2 bg-myTheme-light text-myTheme-lightTextBody rounded-full hover:bg-myTheme-light/80 transition-colors mb-2 flex items-center border border-myTheme-accent/20"
            onClick={handleClearAll}
          >
            <span className="mr-2 text-myTheme-accent">✕</span> Clear All Filters
          </button>
        )}
      </div>

      <div className="flex-1 overflow-y-auto p-5 bg-myTheme-white">
        <h3 className="text-lg font-medium text-myTheme-primary mb-4 flex items-center justify-between">
          <span>Available Tags</span>
          {filterUniqueTags.length > 0 && (
            <span className="text-sm bg-myTheme-lightbg text-myTheme-lightTextBody px-3 py-1 rounded-full">
              {filterUniqueTags.length}
            </span>
          )}
        </h3>
        <div className="flex flex-wrap gap-2">
          {filterUniqueTags.length > 0 ? (
            filterUniqueTags.map((tag) => (
              <div key={tag} className="flex-grow max-w-[calc(50%-0.25rem)]">
                <TagView
                  tag={tag}
                  onClick={() => handleTagClick(tag)}
                  isSelected={selectedTags.includes(tag)}
                  count={
                    selectedTags.includes(tag) ? filteredProductsLength : 0
                  }
                />
              </div>
            ))
          ) : (
            <p className="text-myTheme-lightTextBody/70 italic py-3">No tags available for the selected filters</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ArrangeByPanel;
