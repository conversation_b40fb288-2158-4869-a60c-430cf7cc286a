"use client";
import React, { useState } from "react";
import { iProduct } from "../util/Interfaces";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { X, Trash2, Plus } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import SmartTags from "@/app/components/SmartTags";
import LocationPicker from "./LocationPicker";
import ImageUpload from "./ImageUpload";
import { updateProduct } from "../util/api";

interface EditProductFormProps {
  initialProduct: iProduct;
  onSuccess?: () => void;
}

export default function EditProductForm({
  initialProduct,
  onSuccess,
}: EditProductFormProps) {
  const [product, setProduct] = useState<iProduct>(initialProduct);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [imageInputValue, setImageInputValue] = useState("");
  const [videoInputValue, setVideoInputValue] = useState("");
  const [linkInputValue, setLinkInputValue] = useState("");
  const [websiteInputValue, setWebsiteInputValue] = useState("");

  const router = useRouter();

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setProduct((prev) => ({ ...prev, [name]: value }));
  };

  const handleArrayInput = (field: keyof iProduct, value: string) => {
    setProduct((prev) => ({
      ...prev,
      [field]: [...(prev[field] as string[] || []), value],
    }));
  };

  const handleRemoveArrayItem = (field: keyof iProduct, index: number) => {
    setProduct((prev) => ({
      ...prev,
      [field]: (prev[field] as string[]).filter((_, i) => i !== index),
    }));
  };

  const mutation = useMutation({
    mutationFn: (product: iProduct) => updateProduct(product),
    onSuccess: (data) => {
      setIsLoading(false);
      setError(null);
      if (onSuccess) {
        onSuccess();
      } else {
        router.push(`/mybusinesses/productsuccess?product=${encodeURIComponent(JSON.stringify(data))}`);
      }
    },
    onError: (error: Error) => {
      setIsLoading(false);
      setError(error.message);
    },
  });

  const handleImageUploaded = (imageUrl: string) => {
    setProduct((prev) => ({ ...prev, display_image: imageUrl }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    if (!product.id) {
      setError("Product ID is missing");
      setIsLoading(false);
      return;
    }

    if (!product.openingDays?.length) {
      setError("Please select at least one opening day");
      setIsLoading(false);
      return;
    }

    try {
      await mutation.mutateAsync(product);
    } catch (error) {
      console.error("Error updating product:", error);
    }
  };

  const handleLocationSelect = (location: { lat: number; lng: number; address: string }) => {
    setProduct({
      ...product,
      latitude: location.lat,
      longitude: location.lng,
      address: location.address,
    });
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="max-w-4xl w-full mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden"
    >
      {/* Form Header */}
      <div className="bg-gradient-to-r from-myTheme-primary to-myTheme-secondary p-6 sm:p-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2">Edit Product</h1>
        <p className="text-white/80">Update your business details below</p>
      </div>

      {/* Form Content */}
      <div className="p-6 sm:p-8">
        {error && (
          <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-xl border border-red-200">
            {error}
          </div>
        )}

        <div className="space-y-8">
          {/* Two Column Layout for Main Sections */}
          <div className="space-y-4">
          {/* Left Column */}
          <div className="space-y-6">
            {/* Basic Information Section */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
              <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                <div className="w-2 h-2 bg-myTheme-primary rounded-full mr-3"></div>
                Basic Information
              </h2>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="name" className="text-myTheme-primary font-medium">Business Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={product.name}
                    onChange={handleChange}
                    required
                    className="mt-2 border-gray-200 focus:border-myTheme-primary focus:ring-myTheme-primary/20"
                    placeholder="Enter your business name"
                  />
                </div>

                <div>
                  <Label htmlFor="description" className="text-myTheme-primary font-medium">Description *</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={product.description}
                    onChange={handleChange}
                    required
                    className="mt-2 border-gray-200 focus:border-myTheme-primary focus:ring-myTheme-primary/20 min-h-[120px]"
                    placeholder="Describe your business, products, or services..."
                  />
                </div>
              </div>
            </div>

            {/* Media Section */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-100">
              <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                Media & Links
              </h2>

              <div className="space-y-6">

                <ImageUpload
                  initialImage={product.display_image}
                  onImageUploaded={handleImageUploaded}
                  label="Display Image"
                />

                <div>
                  <Label className="text-myTheme-primary font-medium">Additional Images</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Input
                      placeholder="Image URL"
                      value={imageInputValue}
                      onChange={(e) => setImageInputValue(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          if (imageInputValue.trim()) {
                            handleArrayInput("images", imageInputValue.trim());
                            setImageInputValue("");
                          }
                        }
                      }}
                      className="border-gray-200 focus:border-green-400 focus:ring-green-400/20"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        if (imageInputValue.trim()) {
                          handleArrayInput("images", imageInputValue.trim());
                          setImageInputValue("");
                        }
                      }}
                      className="border-green-200 hover:bg-green-50"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="mt-2 space-y-2">
                    {product.images?.map((image, index) => (
                      <div key={index} className="flex items-center space-x-2 p-2 bg-white rounded-lg border border-gray-100">
                        <Input value={image} readOnly className="bg-gray-50" />
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => handleRemoveArrayItem("images", index)}
                          className="border-red-200 hover:bg-red-50 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-myTheme-primary font-medium">Video Links</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Input
                      placeholder="Video URL (YouTube, Vimeo, etc.)"
                      value={videoInputValue}
                      onChange={(e) => setVideoInputValue(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          if (videoInputValue.trim()) {
                            handleArrayInput("videos", videoInputValue.trim());
                            setVideoInputValue("");
                          }
                        }
                      }}
                      className="border-gray-200 focus:border-green-400 focus:ring-green-400/20"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        if (videoInputValue.trim()) {
                          handleArrayInput("videos", videoInputValue.trim());
                          setVideoInputValue("");
                        }
                      }}
                      className="border-green-200 hover:bg-green-50"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="mt-2 space-y-2">
                    {product.videos?.map((video, index) => (
                      <div key={index} className="flex items-center space-x-2 p-2 bg-white rounded-lg border border-gray-100">
                        <Input value={video} readOnly className="bg-gray-50" />
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => handleRemoveArrayItem("videos", index)}
                          className="border-red-200 hover:bg-red-50 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-myTheme-primary font-medium">Additional Links</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Input
                      placeholder="Link URL"
                      value={linkInputValue}
                      onChange={(e) => setLinkInputValue(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          if (linkInputValue.trim()) {
                            handleArrayInput("links", linkInputValue.trim());
                            setLinkInputValue("");
                          }
                        }
                      }}
                      className="border-gray-200 focus:border-green-400 focus:ring-green-400/20"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        if (linkInputValue.trim()) {
                          handleArrayInput("links", linkInputValue.trim());
                          setLinkInputValue("");
                        }
                      }}
                      className="border-green-200 hover:bg-green-50"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="mt-2 space-y-2">
                    {product.links?.map((link, index) => (
                      <div key={index} className="flex items-center space-x-2 p-2 bg-white rounded-lg border border-gray-100">
                        <Input value={link} readOnly className="bg-gray-50" />
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => handleRemoveArrayItem("links", index)}
                          className="border-red-200 hover:bg-red-50 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-myTheme-primary font-medium">Website URLs</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Input
                      placeholder="Website URL (https://...)"
                      value={websiteInputValue}
                      onChange={(e) => setWebsiteInputValue(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          if (websiteInputValue.trim()) {
                            handleArrayInput("website", websiteInputValue.trim());
                            setWebsiteInputValue("");
                          }
                        }
                      }}
                      className="border-gray-200 focus:border-green-400 focus:ring-green-400/20"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        if (websiteInputValue.trim()) {
                          handleArrayInput("website", websiteInputValue.trim());
                          setWebsiteInputValue("");
                        }
                      }}
                      className="border-green-200 hover:bg-green-50"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="mt-2 space-y-2">
                    {product.website?.map((site, index) => (
                      <div key={index} className="flex items-center space-x-2 p-2 bg-white rounded-lg border border-gray-100">
                        <Input value={site} readOnly className="bg-gray-50" />
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => handleRemoveArrayItem("website", index)}
                          className="border-red-200 hover:bg-red-50 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">

              {/* Categories Section */}
              <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-100">
                <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                  Categories & Tags
                </h2>

                <div className="space-y-6">
                  <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg border border-blue-100">
                    <Label className="text-blue-700 font-medium flex items-center">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
                      AI-Suggested Categories
                    </Label>
                    <p className="text-sm text-blue-600 mb-3 mt-1">
                      Based on your product description
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <SmartTags
                        description={product.description}
                        handleArrayInput={handleArrayInput}
                        handleRemoveArrayItem={handleRemoveArrayItem}
                        field="tags"
                      />
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <Label className="text-myTheme-primary font-medium flex items-center">
                      <div className="w-1.5 h-1.5 bg-myTheme-primary rounded-full mr-2"></div>
                      Manual Categories
                    </Label>
                    <p className="text-sm text-gray-600 mb-3 mt-1">
                      Add your own categories
                    </p>
                    <div className="flex items-center space-x-2">
                      <Input
                        placeholder="Enter a category"
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            const value = e.currentTarget.value.trim();
                            if (value) {
                              handleArrayInput("tags", value);
                              e.currentTarget.value = "";
                            }
                          }
                        }}
                        className="border-gray-200 focus:border-purple-400 focus:ring-purple-400/20"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => {
                          const input = document.querySelector(
                            'input[placeholder="Enter a category"]',
                          ) as HTMLInputElement;
                          const value = input.value.trim();
                          if (value) {
                            handleArrayInput("tags", value);
                            input.value = "";
                          }
                        }}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="mt-2 flex flex-wrap md:gap-2 gap-4">
                      {product.tags?.map((tag, index) => (
                        <div
                          key={index}
                          className="flex items-center bg-gray-100 rounded-full px-3 py-1"
                        >
                          <span>{tag}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 ml-2"
                            onClick={() => handleRemoveArrayItem("tags", index)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Business Hours Section */}
              <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-6 rounded-xl border border-orange-100">
                <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                  Business Hours
                </h2>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="openingHrs" className="text-myTheme-primary font-medium">Opening Hours</Label>
                      <Input
                        id="openingHrs"
                        name="openingHrs"
                        type="time"
                        value={product.openingHrs || ""}
                        onChange={handleChange}
                        className="mt-2 border-gray-200 focus:border-orange-400 focus:ring-orange-400/20"
                      />
                    </div>
                    <div>
                      <Label htmlFor="closingHrs" className="text-myTheme-primary font-medium">Closing Hours</Label>
                      <Input
                        id="closingHrs"
                        name="closingHrs"
                        type="time"
                        value={product.closingHrs || ""}
                        onChange={handleChange}
                        className="mt-2 border-gray-200 focus:border-orange-400 focus:ring-orange-400/20"
                      />
                    </div>
                  </div>

                  <div>
                    <Label className="text-myTheme-primary font-medium">Opening Days</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                          setProduct({
                            ...product,
                            openingDays: allDays
                          });
                        }}
                        className="mb-2 border-orange-200 hover:bg-orange-50"
                      >
                        Select All Days
                      </Button>
                      {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                        <Button
                          key={day}
                          type="button"
                          variant={product.openingDays?.includes(day) ? "default" : "outline"}
                          size="sm"
                          onClick={() => {
                            const currentDays = product.openingDays || [];
                            if (currentDays.includes(day)) {
                              setProduct({
                                ...product,
                                openingDays: currentDays.filter(d => d !== day)
                              });
                            } else {
                              setProduct({
                                ...product,
                                openingDays: [...currentDays, day]
                              });
                            }
                          }}
                          className={product.openingDays?.includes(day)
                            ? "bg-gradient-to-r from-myTheme-primary to-myTheme-secondary text-white border-0 shadow-sm"
                            : "border-orange-200 hover:bg-orange-50 text-orange-700"
                          }
                        >
                          {day}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Information Section */}
              <div className="bg-gradient-to-r from-teal-50 to-cyan-50 p-6 rounded-xl border border-teal-100">
                <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                  <div className="w-2 h-2 bg-teal-500 rounded-full mr-3"></div>
                  Contact Information
                </h2>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="telephone" className="text-myTheme-primary font-medium">Phone Number</Label>
                    <Input
                      id="telephone"
                      name="telephone"
                      value={product.telephone || ""}
                      onChange={handleChange}
                      placeholder="+****************"
                      className="mt-2 border-gray-200 focus:border-teal-400 focus:ring-teal-400/20"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email" className="text-myTheme-primary font-medium">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={product.email || ""}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                      className="mt-2 border-gray-200 focus:border-teal-400 focus:ring-teal-400/20"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Location Section - Full Width */}
          <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-6 rounded-xl border border-indigo-100">
            <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
              <div className="w-2 h-2 bg-indigo-500 rounded-full mr-3"></div>
              Location & Address
            </h2>
            <LocationPicker
              onLocationSelect={handleLocationSelect}
            />
            {product.address && (
              <p className="mt-2 text-sm text-gray-600">{product.address}</p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-center">
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full max-w-md bg-gradient-to-r from-myTheme-primary to-myTheme-secondary hover:from-myTheme-primary/90 hover:to-myTheme-secondary/90 text-white font-semibold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
            >
              {isLoading ? "Updating Product..." : "Update Product"}
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
}