"use client";
import { iReview } from "../util/Interfaces";
import ReviewBox from "./ReviewBox";
import { useQuery } from "@tanstack/react-query";
import { getLatestReviews } from "../util/serverFunctions";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { Clock, Flame, Star, ThumbsUp, ExternalLink } from "lucide-react";
import { TopReviewsSkeleton } from "./skeletons";
import Link from "next/link";

type FilterType = "latest" | "popular" | "trending";

const TopReviews = () => {
  const [filter, setFilter] = useState<FilterType>("latest");
  const [visibleReviews, setVisibleReviews] = useState(6);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["latestReviews", filter],
    queryFn: async () => {
      const response = await getLatestReviews(filter);
      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to fetch latest reviews");
      }
      return response.data;
    },
    refetchOnWindowFocus: false,
  });

  if (isError)
    return (
      <div>
        Error loading reviews:{" "}
        {error instanceof Error ? error.message : "Unknown error"}
      </div>
    );
  if (isLoading) return <TopReviewsSkeleton />;

  // Ensure reviews is always an array with proper typing
  const reviews: iReview[] = Array.isArray(data) ? data : [];

  // No need for client-side filtering since endpoints now handle proper sorting
  const sortedReviews = reviews;

  if (sortedReviews.length === 0)
    return (
      <div className="text-center p-8">
        <p className="text-lg font-medium">No reviews yet</p>
        <p className="text-gray-500">Be the first to share your experience!</p>
      </div>
    );

  const loadMore = () => {
    setVisibleReviews((prev) => prev + 6);
  };

  const FilterButton = ({
    type,
    icon,
    label,
  }: {
    type: FilterType;
    icon: React.ReactNode;
    label: string;
  }) => (
    <button
      className={`flex items-center gap-2 relative px-3 py-2 font-medium text-base transition-all duration-200 ease-in-out ${
        filter === type
          ? "text-myTheme-primary after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-myTheme-primary after:rounded-full"
          : "text-myTheme-accent hover:text-myTheme-primary hover:underline"
      }`}
      onClick={() => setFilter(type)}
    >
      {icon}
      {label}
    </button>
  );

  return (
    <div className="flex flex-col w-full h-full justify-center items-center space-y-8 relative z-0">
      {/* Header Section */}
      <div className="w-full flex flex-col items-center space-y-6">
        <div className="text-center space-y-3">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 leading-tight">
            Latest Reviews from Guyana
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            See what fellow Guyanese are saying about local businesses and
            services
          </p>
        </div>

        <div className="flex gap-4 flex-wrap justify-center">
          <FilterButton
            type="latest"
            icon={<Clock size={18} />}
            label="Latest"
          />
          <FilterButton
            type="popular"
            icon={<ThumbsUp size={18} />}
            label="Most Helpful"
          />
          <FilterButton
            type="trending"
            icon={<Flame size={18} />}
            label="Trending"
          />
        </div>
      </div>

      <div className="w-full grid mx-auto items-start justify-center grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4">
        {sortedReviews
          .slice(0, visibleReviews)
          .map((review: iReview, index: number) => (
            <ReviewBox key={index} review={review} />
          ))}
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-center justify-center mt-8">
        {visibleReviews < sortedReviews.length && (
          <Button variant="outline" onClick={loadMore}>
            Load More Reviews
          </Button>
        )}

        <Link href="/reviews-explore">
          <Button
            variant="default"
            className="bg-myTheme-primary hover:bg-myTheme-secondary text-white font-medium px-6 py-2 rounded-full inline-flex items-center gap-2 transition-all duration-200 hover:shadow-lg"
          >
            <ExternalLink size={16} />
            Explore All Reviews
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default TopReviews;
