"use client";
import { iProduct, iUser } from "@/app/util/Interfaces";
import { getUser, getProduct } from "@/app/util/serverFunctions";
import { useQuery } from "@tanstack/react-query";
import LoadingSpinner from "../components/LoadingSpinner";
import { useAuth } from "@clerk/nextjs";
import Image from "next/legacy/image";
import { InteractiveRating } from "./RatingSystem";
import { useState, useEffect } from "react";
import { useRouter, useParams, useSearchParams } from "next/navigation";
import Quote from './Quote'; // Import the new Quote component

const WriteAReview = ({ hideRecentReviews = false }: { hideRecentReviews?: boolean }) => {
  const [rating, setRating] = useState(3); // Initial value
  const [mounted, setMounted] = useState(false);
  const auth = useAuth();
  const searchParams = useSearchParams();
  const [reviewEligibility, setReviewEligibility] = useState({
    isEligible: true,
    message: "",
    nextEligibleDate: null,
    daysRemaining: 0
  });
  const [isCheckingEligibility, setIsCheckingEligibility] = useState(false);
  const [productId, setProductId] = useState<string | null>(null);

  // Dummy recent reviews data - replace with actual data fetching later
  const recentReviews = [
    {
      userName: "Jane Doe",
      userImage: "/placeholder-user.jpg", // Make sure you have this image or use a valid path
      quoteText: "Absolutely loved this product! It exceeded all my expectations and has become a daily essential."
    },
    {
      userName: "John Smith",
      // userImage: "/another-user.jpg", // Example if you have another image
      quoteText: "Great value for the price. I've been using it for a week and I'm very satisfied with the performance."
    },
    {
      userName: "Alice Brown",
      userImage: "/placeholder-avatar.jpg",
      quoteText: "Highly recommend to anyone looking for a reliable and efficient solution. The customer service was also top-notch!"
    }
  ];

  useEffect(() => {
    setMounted(true);
    const id = searchParams.get("id");
    if (id) {
      setProductId(id);
    }
  }, [searchParams]);

  const id = productId;
  const router = useRouter();
  const ratingChanged = (newRating: number) => {
    if (id) {
      setRating(newRating);
      router.push(`/cr/?id=${id}&rating=${newRating}`);
    }
  };

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["user", auth.userId],
    queryFn: async () => await getUser(),
    refetchOnWindowFocus: false,
  }) as any;

  const { data: product, isLoading: productLoading } = useQuery({
    queryKey: ['product', id],
    queryFn: async () => {
      if (!id) {
        throw new Error('No product ID provided');
      }
      const response = await getProduct(id);
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to fetch product');
      }
      return response.data;
    },
    enabled: !!id,
  });

  // Check if the user is eligible to write a review
  useEffect(() => {
    const checkEligibility = async () => {
      // Make sure we have a valid user ID and product ID
      const userId = data?.data?.id;
      if (!userId || !productId) return;

      try {
        setIsCheckingEligibility(true);
        const response = await fetch('/api/check-review-eligibility', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            userId,
            productId
          })
        });

        if (!response.ok) {
          console.error("Error checking eligibility:", response.statusText);
          return;
        }

        const eligibilityData = await response.json();
        setReviewEligibility(eligibilityData);
      } catch (error) {
        console.error("Error checking review eligibility:", error);
      } finally {
        setIsCheckingEligibility(false);
      }
    };

    if (data?.data?.id && productId) {
      checkEligibility();
    }
  }, [data?.data?.id, productId]);

  if (isLoading || isCheckingEligibility || productLoading) return <div className="min-h-screen flex items-center justify-center"><LoadingSpinner /></div>;
  if (isError) return <div className="min-h-screen flex items-center justify-center"><p className="text-red-500">Error: {error?.toString()}</p></div>;
  // if (productLoading) return <LoadingSpinner />; // Already handled above
  const user: iUser | undefined = data?.data as iUser;

  // If the user is not eligible to review, show a message
  if (!reviewEligibility.isEligible) {
    return (
      <div className=" flex flex-col items-center justify-center bg-gradient-to-br from-slate-100 to-sky-100 dark:from-slate-900 dark:to-sky-900 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-8 max-w-lg w-full text-center">
          <div className="flex flex-col gap-2 items-center mb-6">
            {mounted && auth.isSignedIn && user?.avatar && (
              <Image
                src={user.avatar}
                width={64} // Increased size
                height={64} // Increased size
                alt="avatar"
                className="rounded-full border-4 border-sky-500 shadow-lg"
              />
            )}
            <p className="text-lg font-semibold text-gray-800 dark:text-white">{user?.userName || 'User'}</p>
          </div>
          <h2 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-4">Review Not Allowed</h2>
          <p className="text-gray-700 dark:text-gray-300 mb-3">{reviewEligibility.message}</p>
          {reviewEligibility.nextEligibleDate && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              You can review this product again in <span className="font-semibold text-sky-600 dark:text-sky-400">{reviewEligibility.daysRemaining} day{reviewEligibility.daysRemaining !== 1 ? 's' : ''}</span>
              (after {new Date(reviewEligibility.nextEligibleDate).toLocaleDateString()})
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-2xl w-full">
        <div className="flex flex-col items-center mb-4">
          {mounted && auth.isSignedIn && user?.avatar && (
            <Image
              src={user.avatar}
              width={80} // Increased size
              height={80} // Increased size
              alt="avatar"
              className="rounded-full border-4 border-pink-500 shadow-lg mb-3"
            />
          )}
          <p className="text-xl font-semibold text-gray-800 dark:text-white">
            {user?.userName || 'Sign in to'} write a review now{id ? ` for ${product?.name}` : ''}!
          </p>
        </div>

        {id && product && (
          <div className="mb-4 text-center">
            <h2 className="text-3xl font-bold text-purple-700 dark:text-purple-400 mb-2">{product.name}</h2>
            <p className="text-md text-gray-600 dark:text-gray-400 italic">{product.description}</p>
          </div>
        )}

        <p className="text-lg font-medium text-gray-700 dark:text-gray-300 text-center mb-3">How many stars would you give it?</p>
        <InteractiveRating
          name="rating"
          rating={rating}
          onRatingChange={setRating}
          size="lg"
        />
      </div>

      {/* Recent Reviews Section */}
      {!hideRecentReviews && recentReviews.length > 0 && (
        <div className="mt-12 w-full max-w-4xl px-4">
          <h3 className="text-2xl font-semibold text-center text-gray-800 dark:text-white mb-6">What others are saying...</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentReviews.map((review, index) => (
              <Quote
                key={index}
                userName={review.userName}
                userImage={review.userImage}
                quoteText={review.quoteText}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WriteAReview;
