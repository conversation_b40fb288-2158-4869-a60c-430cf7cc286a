import {
  MdFastfood,
  MdDeliveryDining,
  MdOutlineElectricalServices,
  MdBusinessCenter,
  MdOtherHouses,
  MdOutlineLocalTaxi,
  MdOutlineHomeRepairService,
} from "react-icons/md";
import { IoMdCar } from "react-icons/io";
import { Gi<PERSON>lothes } from "react-icons/gi";
import Link from "next/link";
import { LuExternalLink } from "react-icons/lu";

const QuickTabs = () => {
  const categories = [
    {
      name: "Fast Food",
      icon: <MdFastfood />,
      link: `/browse?tags=${encodeURIComponent("Fast Food".toLowerCase())}`,
    },
    {
      name: "Car Rental",
      icon: <IoMdCar />,
      link: `/browse?tags=${encodeURIComponent("Car Rental".toLowerCase())}`,
    },
    {
      name: "Delivery Service",
      icon: <MdDeliveryDining />,
      link: `/browse?tags=${encodeURIComponent("Delivery Service".toLowerCase())}`,
    },
    {
      name: "Electronics",
      icon: <MdOutlineElectricalServices />,
      link: `/browse?tags=${encodeURIComponent("Electronics".toLowerCase())}`,
    },
    {
      name: "Insurance Agency",
      icon: <MdBusinessCenter />,
      link: `/browse?tags=${encodeURIComponent("Insurance Agency".toLowerCase())}`,
    },
    {
      name: "Real Estate",
      icon: <MdOtherHouses />,
      link: `/browse?tags=${encodeURIComponent("Real Estate".toLowerCase())}`,
    },
    {
      name: "Clothing Store",
      icon: <GiClothes />,
      link: `/browse?tags=${encodeURIComponent("Clothing Store".toLowerCase())}`,
    },
    {
      name: "Taxi Service",
      icon: <MdOutlineLocalTaxi />,
      link: `/browse?tags=${encodeURIComponent("Taxi Service".toLowerCase())}`,
    },
    {
      name: "Home Services",
      icon: <MdOutlineHomeRepairService />,
      link: `/browse?tags=${encodeURIComponent("Home Services".toLowerCase())}`,
    },
    {
      name: "See All",
      icon: <LuExternalLink />,
      link: "/browse",
    },
  ];

  // this component will be quick search little clickable cards categories for the home page
  return (
    <div className="flex flex-col justify-center items-center w-full bg-white/50 backdrop-blur-sm py-6 sm:py-8 relative z-[1] border-y border-gray-200/50">
      <div className="flex flex-col sm:flex-row items-center sm:items-center gap-2 sm:gap-3 mb-8 px-4">
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-800 leading-tight">Quick Categories</h2>
        <span className="text-base sm:text-lg text-gray-500 font-medium">Find what you are looking for</span>
      </div>
      <div className="max-w-5xl mx-auto w-full px-4 relative">
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-10 gap-3 sm:gap-4">
          {categories.map((category, index) => (
            <div key={index} className="aspect-square">
              <Link
                href={category.link}
                className="flex flex-col items-center justify-center h-full w-full gap-2 sm:gap-3 p-3 sm:p-4 rounded-xl bg-white/80 hover:bg-white hover:shadow-lg hover:scale-105 transition-all duration-200 group"
              >
                <span className="text-2xl sm:text-3xl text-gray-600 group-hover:text-myTheme-primary transition-colors">
                  {category.icon}
                </span>
                <p className="text-xs sm:text-sm font-medium text-gray-700 group-hover:text-myTheme-primary transition-colors text-center">
                  {category.name}
                </p>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default QuickTabs;
