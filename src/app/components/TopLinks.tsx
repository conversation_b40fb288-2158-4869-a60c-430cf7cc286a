import { FC, useState, useEffect } from "react";
import Link from "next/link";
import { topLinks } from "@/app/util/links";
import { usePathname } from "next/navigation";
import { useUser } from "@/app/hooks/useUser";
import { iUser } from "@/app/util/Interfaces";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  FiChevronDown,
  FiPackage,
  FiPlusSquare,
  FiFileText,
  FiBarChart,
  FiTrendingUp,
} from "react-icons/fi";
import { IoPricetagOutline } from "react-icons/io5";
import { NavSkeleton } from "./Skeleton";

interface LinksProps {
  showHome?: boolean;
}

const Links: FC<LinksProps> = ({ showHome }) => {
  const pathname = usePathname();
  const [isAdmin, setIsAdmin] = useState(false);
  const {
    user,
    isLoading: userIsLoading,
    isLoggedIn,
    isNotLoggedIn,
  } = useUser();
  const links = showHome
    ? [{ name: "Home", link: "/" }, ...topLinks]
    : topLinks;

  useEffect(() => {
    const checkAdminPermission = async () => {
      try {
        const response = await fetch("/api/admin/check-permission");
        const data = await response.json();
        setIsAdmin(data.isAdmin);
      } catch (error) {
        console.error("Error checking admin permission:", error);
        setIsAdmin(false);
      }
    };

    checkAdminPermission();
  }, []);

  return (
    <nav className="flex flex-row items-center justify-center">
      {links.map((link, index) => {
        // Skip admin link if user is not admin
        if (link.adminOnly && !isAdmin) return null;

        const isActive = pathname === link.link;

        if (link.name === "My Businesses") {
          // Show skeleton only while actually loading user data
          if (userIsLoading) {
            return (
              <div
                key={index}
                className="inline-flex items-center px-2 md:px-3 lg:px-4 py-2 text-sm font-medium rounded-md"
              >
                <span className="animate-pulse text-blue-600 font-medium">
                  Checking Biz...
                </span>
              </div>
            );
          }

          // Hide completely if user is not logged in
          if (isNotLoggedIn) {
            return null;
          }

          return (
            <DropdownMenu key={index}>
              <DropdownMenuTrigger
                className={`
                inline-flex items-center px-2 md:px-3 lg:px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ease-in-out whitespace-nowrap group
                ${
                  isActive
                    ? "text-myTheme-accent bg-gray-50 font-semibold"
                    : "text-gray-700 hover:text-myTheme-accent hover:bg-gray-50"
                }
              `}
              >
                {link.name}
                <FiChevronDown className="ml-1 w-4 h-4 text-gray-400 transition-transform duration-200 group-data-[state=open]:rotate-180" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="min-w-[200px] bg-white rounded-md shadow-lg">
                <DropdownMenuItem className="hover:bg-gray-50">
                  <Link
                    href="/business"
                    className="w-full flex items-center"
                    onClick={() =>
                      document.dispatchEvent(
                        new KeyboardEvent("keydown", { key: "Escape" }),
                      )
                    }
                  >
                    <FiTrendingUp className="mr-3 w-4 h-4" />
                    Business Solutions
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem className="hover:bg-gray-50">
                  <Link
                    href={link.link}
                    className="w-full flex items-center"
                    onClick={() =>
                      document.dispatchEvent(
                        new KeyboardEvent("keydown", { key: "Escape" }),
                      )
                    }
                  >
                    <IoPricetagOutline className="mr-3 w-4 h-4" />
                    My Businesses
                  </Link>
                </DropdownMenuItem>
                {user?.businesses && user.businesses.length > 0 && (
                  <DropdownMenuItem className="hover:bg-gray-50">
                    <Link
                      href="/owner-admin"
                      className="w-full flex items-center"
                      onClick={() =>
                        document.dispatchEvent(
                          new KeyboardEvent("keydown", { key: "Escape" }),
                        )
                      }
                    >
                      <FiBarChart className="mr-3 w-4 h-4" />
                      Advanced Dashboard
                    </Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem className="hover:bg-gray-50">
                  <Link
                    href="/claim-product"
                    className="w-full flex items-center"
                    onClick={() =>
                      document.dispatchEvent(
                        new KeyboardEvent("keydown", { key: "Escape" }),
                      )
                    }
                  >
                    <FiPackage className="mr-3 w-4 h-4" />
                    Claim Product
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem className="hover:bg-gray-50">
                  <Link
                    href="/claims"
                    className="w-full flex items-center"
                    onClick={() =>
                      document.dispatchEvent(
                        new KeyboardEvent("keydown", { key: "Escape" }),
                      )
                    }
                  >
                    <FiFileText className="mr-3 w-4 h-4" />
                    My Claims
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem className="hover:bg-gray-50">
                  <Link
                    href="/submit"
                    className="w-full flex items-center"
                    onClick={() =>
                      document.dispatchEvent(
                        new KeyboardEvent("keydown", { key: "Escape" }),
                      )
                    }
                  >
                    <FiPlusSquare className="mr-3 w-4 h-4" />
                    Add Product/Business
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        }

        return (
          <Link
            key={index}
            href={link.link}
            className={`
              inline-flex items-center px-2 md:px-3 lg:px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ease-in-out whitespace-nowrap
              ${
                isActive
                  ? "text-myTheme-accent bg-gray-50 font-semibold"
                  : "text-gray-700 hover:text-myTheme-accent hover:bg-gray-50"
              }
            `}
          >
            {link.name}
          </Link>
        );
      })}
    </nav>
  );
};

export default Links;
