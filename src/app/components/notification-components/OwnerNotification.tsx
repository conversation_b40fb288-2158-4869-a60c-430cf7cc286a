import Link from "next/link";
import { BellIcon } from "lucide-react";
import { iProductOwnerNotification } from "@/app/util/Interfaces";
import { useAtom } from "jotai";
import { ownerNotificationsAtom } from "@/app/store/store";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface NotificationsPageProps {
  notifications: iProductOwnerNotification[] | [];
}

export default function NotificationBell({
  notifications,
}: NotificationsPageProps) {
  const [notiAtoms, setNotiAtoms] = useAtom(ownerNotificationsAtom);
  const [isHovered, setIsHovered] = useState(false);

  const count = notifications?.length || 0;
  const unreadCount = notifications?.filter(n => !n.read).length || 0;

  const handleClick = () => {
    console.log("running set atoms");
    setNotiAtoms(notifications);
  };

  return (
    <Link
      href={{ pathname: "/notifications" }}
      className={cn(
        "flex items-center transition-all duration-200 rounded-md p-1",
        isHovered ? "bg-gray-100" : ""
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative flex items-center">
        <BellIcon className={cn(
          "w-5 h-5 transition-colors duration-200",
          isHovered ? "text-myTheme-accent" : "text-gray-600"
        )} />
        {count > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full w-4 h-4 flex items-center justify-center animate-pulse">
            {count > 99 ? "99+" : count}
          </span>
        )}
        <span className={cn(
          "ml-2 text-sm transition-colors duration-200",
          isHovered ? "text-myTheme-accent" : "text-gray-600"
        )}>
          Notifications
        </span>
        {count > 0 && (
          <span className="ml-1 text-sm">
            <span className="font-medium">{unreadCount}</span>
            <span className="text-gray-500">/{count}</span>
          </span>
        )}
      </div>
    </Link>
  );
}
