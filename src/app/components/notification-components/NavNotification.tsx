import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  BellIcon,
  Check,
  CheckCircleIcon,
  Clock,
  MessageSquare,
  Star,
  User,
} from "lucide-react";
import { useAtom, useSet<PERSON>tom } from "jotai";
import { useAuth } from "@clerk/nextjs";
import { useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

import {
  iProductOwnerNotification,
  iUserNotification,
  FetchedNotificationsData,
} from "@/app/util/Interfaces";
import {
  ownerNotifications<PERSON>tom,
  userNotifications<PERSON>tom,
} from "@/app/store/store";
import { getNotifications, getUser } from "@/app/util/serverFunctions";
import LoadingSpinner from "../LoadingSpinner";
import { handleMarkAsRead } from "@/app/util/markAsReadFunc";
import {
  isOwnerNotification,
  shouldHavePremiumStyling,
} from "@/app/util/notificationHelpers";
import { Tooltip } from "@mantine/core";

dayjs.extend(relativeTime);

type NotificationType = iUserNotification | iProductOwnerNotification;

export default function NotificationDropdown() {
  const router = useRouter();
  const auth = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const setUserNotificationsAtom = useSetAtom(userNotificationsAtom);
  const setOwnerNotificationsAtom = useSetAtom(ownerNotificationsAtom);
  const [unReadNotifications, setUnReadNotifications] =
    useState<FetchedNotificationsData>();

  // Handle click on the bell icon when there are no notifications
  const handleBellClick = () => {
    if (totalCount === 0) {
      router.push("/notifications");
      return;
    }
    // If there are notifications, just toggle the dropdown
    setIsOpen(!isOpen);
  };

  // const { data: userData } = useQuery({
  //   queryKey: ["user", auth.userId],
  //   queryFn: getUser,
  //   refetchOnWindowFocus: false,
  // });

  function getUnreadNotifications(
    notifications: FetchedNotificationsData
  ): FetchedNotificationsData {
    if (!notifications) {
      return {
        userNotifications: [],
        ownerNotifications: []
      };
    }
    return {
      userNotifications: (notifications.userNotifications || []).filter(
        (notification) => notification?.read === false
      ),
      ownerNotifications: (notifications.ownerNotifications || []).filter(
        (notification) => notification?.read === false
      ),
    };
  }

  const {
    data: notificationsData,
    isLoading: isLoadingNotifications,
    isError: isErrorNotifications,
    error: errorNotifications,
  } = useQuery<FetchedNotificationsData, Error>({
    queryKey: ["notifications", auth.userId],
    queryFn: async () => {
      if (!auth.userId) {
        throw new Error("User ID is required");
      }
      const response = await getNotifications(auth.userId);
      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to fetch notifications");
      }
      return {
        userNotifications: response.data.userNotifications || [],
        ownerNotifications: response.data.ownerNotifications || []
      };
    },
    refetchOnWindowFocus: true,
    refetchInterval: 5000,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60, // Consider data fresh for 1 minute
  });

  useEffect(() => {
    if (notificationsData) {
      const unread = getUnreadNotifications(notificationsData);
      setUnReadNotifications(unread);
      setUserNotificationsAtom(notificationsData.userNotifications || []);
      setOwnerNotificationsAtom(notificationsData.ownerNotifications || []);
    }
  }, [notificationsData, setUserNotificationsAtom, setOwnerNotificationsAtom]);

  // Show error state in a more user-friendly way
  if (isErrorNotifications) {
    console.error("Notification fetch error:", errorNotifications);
    return (
      <Button
        variant="ghost"
        size="icon"
        className="relative"
        onClick={() => router.push("/notifications")}
      >
        <BellIcon className="h-5 w-5 text-gray-400" />
      </Button>
    );
  }

  const userCount = unReadNotifications?.userNotifications?.length || 0;
  const ownerCount = unReadNotifications?.ownerNotifications?.length || 0;
  const totalCount = userCount + ownerCount;

  const latestUserNotifications =
    unReadNotifications?.userNotifications?.slice(0, 3) || [];
  const latestOwnerNotifications =
    unReadNotifications?.ownerNotifications?.slice(0, 3) || [];

  const handleOwnerNotiClick = (notification: iProductOwnerNotification) => {
    const params = new URLSearchParams({
      id: notification.review_id.toString(),
      productid: notification.product_id.toString(),
    });
    handleMarkAsRead(
      notification.id,
      "owner",
      setOwnerNotificationsAtom,
      setUserNotificationsAtom
    );

    router.push(`/fr?${params.toString()}`);
    setIsOpen(false);
  };

  const handleUserNotiClick = (notification: iUserNotification) => {
    const params = new URLSearchParams({
      id: notification.review_id.toString(),
      productid: notification.product_id!.toString(),
      cid: notification.id.toString(),
    });
    handleMarkAsRead(
      notification.id,
      "user",
      setOwnerNotificationsAtom,
      setUserNotificationsAtom
    );

    router.push(`/fr?${params.toString()}`);
    setIsOpen(false);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "relative transition-all duration-200",
            isHovered ? "bg-gray-100" : "",
            isLoadingNotifications ? "animate-pulse-subtle" : ""
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onClick={(e) => {
            if (totalCount === 0) {
              e.preventDefault();
              handleBellClick();
            }
          }}
        >
          <BellIcon
            className={cn(
              "h-5 w-5 transition-transform duration-200",
              isHovered ? "text-myTheme-accent" : "",
              isLoadingNotifications ? "animate-spin-subtle" : ""
            )}
          />
          {totalCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 px-1 min-w-[1.25rem] h-5 animate-pulse"
            >
              {totalCount > 99 ? "99+" : totalCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-80 max-h-[80vh] overflow-y-auto p-0 rounded-lg shadow-lg border border-gray-100"
        sideOffset={8}
      >
        <div className="p-3 bg-gradient-to-r from-gray-50 to-white border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-gray-900">Notifications</h3>
            {totalCount > 0 && (
              <Badge variant="outline" className="bg-gray-100 text-gray-700">
                {totalCount} new
              </Badge>
            )}
          </div>
        </div>

        {isLoadingNotifications ? (
          <div className="p-4 flex justify-center">
            <LoadingSpinner />
          </div>
        ) : totalCount === 0 ? (
          <>
            <div className="p-6 text-center">
              <BellIcon className="h-10 w-10 text-gray-300 mx-auto mb-2" />
              <p className="text-gray-500 font-medium">No new notifications</p>
              <p className="text-sm text-gray-400 mt-1">
                We will notify you when something new arrives
              </p>
            </div>
            <DropdownMenuSeparator className="my-1" />
            <DropdownMenuItem
              asChild
              className="px-3 py-2 cursor-pointer hover:bg-gray-50 focus:bg-gray-50"
            >
              <Link
                href="/notifications"
                className="flex items-center justify-between w-full text-myTheme-accent hover:text-myTheme-accent/80"
              >
                <span>See All Notifications</span>
                <MessageSquare className="h-4 w-4" />
              </Link>
            </DropdownMenuItem>
          </>
        ) : (
          <>
            {renderNotificationSection(
              "User Notifications",
              latestUserNotifications,
              handleUserNotiClick,
              <User className="h-4 w-4 text-blue-500" />
            )}
            {userCount > 0 && ownerCount > 0 && (
              <DropdownMenuSeparator className="my-1" />
            )}
            {renderNotificationSection(
              "Product Reviews",
              latestOwnerNotifications,
              handleOwnerNotiClick,
              <Star className="h-4 w-4 text-amber-500" />
            )}
            <DropdownMenuSeparator className="my-1" />
            <DropdownMenuItem
              asChild
              className="px-3 py-2 cursor-pointer hover:bg-gray-50 focus:bg-gray-50"
            >
              <Link
                href="/notifications"
                className="flex items-center justify-between w-full text-myTheme-accent hover:text-myTheme-accent/80"
              >
                <span>See All Notifications</span>
                <MessageSquare className="h-4 w-4" />
              </Link>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function renderNotificationSection(
  title: string,
  notifications: any[],
  onClick: (notification: any) => void,
  icon: React.ReactNode
) {
  if (notifications.length === 0) return null;

  return (
    <>
      <div className="px-3 py-2 bg-gray-50 border-b border-gray-100">
        <div className="flex items-center">
          {icon}
          <span className="ml-2 font-medium text-sm text-gray-700">
            {title}
          </span>
        </div>
      </div>
      {notifications.map((notification, index) => {
        // Check if this notification is from a business owner
        const isPremium = shouldHavePremiumStyling(notification);
        const isRead = notification.read === true;

        return (
          <DropdownMenuItem
            key={`${title.toLowerCase()}-${index}`}
            className={cn(
              "flex flex-col items-start py-2 px-3 cursor-pointer focus:bg-gray-50 relative overflow-hidden border-l-4",
              isPremium
                ? "owner-comment hover:bg-blue-50 border-l-blue-500"
                : "hover:bg-gray-50 border-l-transparent",
              !isRead && "bg-blue-50 font-semibold"
            )}
            onClick={() => onClick(notification)}
          >
            {!isRead && (
              <div className="absolute top-2 right-2">
                <div className="unread-dot"></div>
              </div>
            )}
            <div className="flex items-start w-full">
              <div className="flex-1 min-w-0">
                <p
                  className={cn(
                    "font-medium text-sm truncate",
                    isPremium ? "owner-text" : "text-gray-900",
                    !isRead && "font-bold"
                  )}
                >
                  {notification.content || notification.review_title}
                </p>
                <div className="flex items-center mt-1 text-xs text-gray-500">
                  {isPremium ? (
                    <>
                      <Tooltip label="Business Owner" withArrow>
                        <div className="flex items-center mr-1">
                          <CheckCircleIcon className="h-3 w-3 mr-1 text-blue-500" />
                          <span className="truncate font-medium text-blue-700">
                            {notification.from_name}
                          </span>
                        </div>
                      </Tooltip>
                      <span className="notification-owner-badge">
                        Verified Owner
                      </span>
                    </>
                  ) : (
                    <>
                      <User className="h-3 w-3 mr-1" />
                      <span className="truncate">{notification.from_name}</span>
                    </>
                  )}
                </div>
              </div>
              <div className="flex items-center ml-2 text-xs text-gray-400">
                <Clock className="h-3 w-3 mr-1" />
                <span>{dayjs(notification.created_at).fromNow()}</span>
              </div>
            </div>
          </DropdownMenuItem>
        );
      })}
    </>
  );
}
