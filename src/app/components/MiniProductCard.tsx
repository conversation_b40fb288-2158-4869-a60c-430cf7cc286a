"use client";

import { iProduct, iReview, iCalculatedRating } from "@/app/util/Interfaces";
import Image from "next/legacy/image";
import Link from "next/link";
import { calculateAverageReviewRatingSync } from "../util/calculateAverageReviewRating";
import RatingDisplayWithThreshold from "./RatingDisplayWithThreshold";
import { WeightedRatingResult } from "../util/calculateWeightedRating";
import { ReadOnlyRating } from "./RatingSystem";
import { Button } from "@/components/ui/button";
import { MdEdit } from "react-icons/md";
import { Eye } from "lucide-react";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";

interface MiniProductCardProps {
  product: iProduct;
  options: {
    size: string; // For rating stars
    showWriteReview: boolean;
  };
  // currentUserId is not strictly needed for this minimal card if claim/owner features are omitted
}

const ratingColors = {
  1: "bg-red-500",
  2: "bg-orange-500",
  3: "bg-yellow-500",
  4: "bg-green-400",
  5: "bg-green-600",
};

const MiniProductCard: React.FC<MiniProductCardProps> = ({ product, options }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!product) return null;

  // Use product.reviews directly if available, or an empty array.
  const allReviews = product.reviews || [];
  
  // Check if product already has weighted rating from API
  const hasWeightedRating = product.weightedRating && typeof product.weightedRating === 'object';
  const ratingResult = hasWeightedRating 
    ? product.weightedRating 
    : calculateAverageReviewRatingSync(allReviews, true) as iCalculatedRating;

  function isCalculatedRating(result: any): result is iCalculatedRating {
    return (
      typeof result === "object" &&
      result !== null &&
      "roundedRating" in result &&
      "roundedRatingOneDecimalPlace" in result &&
      "numberOfReviews" in result
    );
  }

  let roundedRating = 0;
  let roundedRatingOneDecimalPlace = "0";
  let numberOfReviews = product._count?.reviews || 0; // Prefer _count.reviews

  if (isCalculatedRating(ratingResult)) {
    roundedRating = ratingResult.roundedRating;
    roundedRatingOneDecimalPlace = ratingResult.roundedRatingOneDecimalPlace.toString();
    // If ratingResult.numberOfReviews is more reliable or different from _count, prioritize it
    numberOfReviews = ratingResult.numberOfReviews > 0 ? ratingResult.numberOfReviews : numberOfReviews;
  } else if (typeof ratingResult === "number") {
    roundedRating = ratingResult as number;
    roundedRatingOneDecimalPlace = (ratingResult as number).toFixed(1);
    numberOfReviews = allReviews.length > 0 ? allReviews.length : numberOfReviews;
  }


  const cardVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.2, ease: "easeOut" } },
    hover: { y: -2, transition: { duration: 0.15, ease: "easeInOut" } },
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      className="w-full h-full flex flex-col"
    >
      <div className="w-full max-w-sm bg-white rounded-xl border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col flex-grow overflow-hidden">
        <Link href={`/reviews?id=${product.id}`} className="block p-6 flex-grow">
          <div className="flex flex-col items-center text-center">
            {product.display_image && (
              <div className="relative w-32 h-32 sm:w-36 sm:h-36 rounded-xl overflow-hidden mb-4 bg-gradient-to-br from-gray-50 to-gray-100 shadow-inner">
                <Image
                  src={product.display_image}
                  alt={`${product.name} Image`}
                  layout="fill"
                  objectFit="contain"
                  className="p-2 transition-transform duration-300 hover:scale-105"
                />
              </div>
            )}
            <h2 className="text-lg font-bold text-gray-900 break-words line-clamp-2 mb-3 leading-tight">
              {product.name}
            </h2>
            
            {/* Rating Section */}
            <div className="flex flex-col items-center gap-2 mb-2">
              {numberOfReviews > 0 ? (
                hasWeightedRating && ratingResult && 'hasMinimumReviews' in ratingResult && ratingResult.hasMinimumReviews !== undefined ? (
                  <div className="flex flex-col items-center">
                    <RatingDisplayWithThreshold
                      ratingData={ratingResult as WeightedRatingResult}
                      size={options.size as any}
                      showReviewCount={true}
                      showConfidence={true}
                      className="flex-col items-center text-center"
                    />
                  </div>
                ) : (
                  <>
                    <div className="flex items-center gap-2">
                      <span
                        className={`px-2.5 py-1 rounded-full text-sm font-bold text-white shadow-sm ${ratingColors[roundedRating as keyof typeof ratingColors] || 'bg-gray-400'}`}
                      >
                        {roundedRatingOneDecimalPlace}
                      </span>
                      <ReadOnlyRating
                        name={product.id!}
                        rating={roundedRating}
                        size={options.size === "rating-sm" ? "sm" : options.size === "rating-lg" ? "lg" : "md"}
                      />
                    </div>
                    <span className="text-sm text-gray-600 font-medium">
                      {numberOfReviews} review{numberOfReviews !== 1 ? 's' : ''}
                    </span>
                  </>
                )
              ) : (
                <div className="flex flex-col items-center gap-1">
                  <div className="flex items-center gap-2">
                    <span className="px-2.5 py-1 rounded-full text-sm font-bold text-white bg-gray-400">
                      0.0
                    </span>
                    <ReadOnlyRating
                      name={product.id!}
                      rating={0}
                      size={options.size === "rating-sm" ? "sm" : options.size === "rating-lg" ? "lg" : "md"}
                    />
                  </div>
                  <span className="text-sm text-gray-500 font-medium">No reviews yet</span>
                </div>
              )}
            </div>
          </div>
        </Link>

        {/* Footer Actions */}
        <div className="border-t border-gray-100 p-4 bg-gradient-to-r from-gray-50 to-gray-100 mt-auto">
          <div className="flex gap-2">
            <Link href={`/product/${product.id}`} className="flex-1">
              <Button
                variant="outline"
                size="sm"
                className="w-full flex items-center justify-center gap-2 font-semibold border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200"
              >
                <Eye size={16} />
                <span>View</span>
              </Button>
            </Link>
            {mounted && options.showWriteReview && (
              <Link href={`/cr/?id=${product.id}&rating=3`} className="flex-1">
                <Button
                  variant="default"
                  size="sm"
                  className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-gray-900 to-black hover:from-black hover:to-gray-800 text-white font-semibold shadow-md hover:shadow-lg transition-all duration-200"
                >
                  <MdEdit size={16} />
                  <span>Review</span>
                </Button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default MiniProductCard;