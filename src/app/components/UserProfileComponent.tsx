"use client";
import React, { useState, useEffect } from "react";
import { iUser } from "@/app/util/Interfaces";
import { updateUser } from "@/app/util/api";
import { getUser, getUserWithId } from "@/app/util/serverFunctions";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import LoadingSpinner from "../components/LoadingSpinner";
import { useAuth } from "@clerk/nextjs";
import UserInfo from "@/app/components/UserInfo";
import { toast } from "sonner";

interface UserProfileComponentProps {
  userIdFromParams?: string;
}

// Number of items to display per page
const ITEMS_PER_PAGE = 10;

// Function to update user in the backend
async function updateUserInBackend(
  userId: string,
  updatedFields: Partial<iUser>,
): Promise<iUser> {
  const response = await fetch(`/api/update/user/${userId}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(updatedFields),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || "Failed to update user");
  }

  return response.json();
}

const UserProfileComponent: React.FC<UserProfileComponentProps> = ({
  userIdFromParams,
}) => {
  const { userId: clerkUserId, isLoaded: isAuthLoaded } = useAuth();
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [isInitialized, setIsInitialized] = useState(false);

  // Wait for auth to be loaded before making queries
  useEffect(() => {
    if (isAuthLoaded) {
      setIsInitialized(true);
    }
  }, [isAuthLoaded]);

  // Only fetch user data if userIdFromParams is not provided
  const { data, isLoading, error } = useQuery({
    queryKey: ['user', userIdFromParams],
    queryFn: async () => {
      if (userIdFromParams) {
        const response = await getUserWithId(userIdFromParams);
        if (!response.success) {
          throw new Error(response.error || 'Failed to fetch user');
        }
        return response.data;
      }
      const response = await getUser();
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch user');
      }
      return response.data;
    },
  });

  const user = data as iUser | undefined;
  const userId = userIdFromParams || user?.id;

  const updateUserMutation = useMutation({
    mutationFn: updateUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user", "current"] });
      toast.success("Profile updated successfully");
    },
    onError: (error) => {
      toast.error(`Failed to update profile: ${error.message}`);
    },
  });

  const handleUpdateUser = async (updatedUser: Partial<iUser>) => {
    if (!userId) {
      toast.error("Cannot update profile: User ID is not available");
      return;
    }
    updateUserMutation.mutate({ userId, userData: updatedUser });
  };

  // Show loading state while auth is loading or during data fetching
  if (!isInitialized || isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Error loading user profile: {error instanceof Error ? error.message : 'Unknown error'}</p>
        <button
          onClick={() => queryClient.invalidateQueries({ queryKey: ["user", "current"] })}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  // Handle case where user is not found
  if (!user) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">User profile not found</p>
        <p className="text-sm text-gray-500 mt-2">Please try again later or contact support if the issue persists.</p>
      </div>
    );
  }

  const totalPages = Math.ceil((user.reviews?.length || 0) / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentReviews = user.reviews?.slice(startIndex, endIndex) || [];

  return (
    <div className="space-y-8">
      <UserInfo user={user} onUpdateUser={handleUpdateUser} />
    </div>
  );
};

export default UserProfileComponent;
