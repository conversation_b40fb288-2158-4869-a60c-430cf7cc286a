interface iTagViewProps {
  tag: string;
  isSelected: boolean;
  onClick: () => void;
  count: number;
}

const TagView = ({
  tag,
  onClick,
  isSelected,
  count,
}: {
  tag: string;
  onClick: () => void;
  isSelected: boolean;
  count: number;
}) => {
  return (
    <button
      onClick={onClick}
      className={`w-full px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
        isSelected
          ? "bg-myTheme-accent text-myTheme-light shadow-sm"
          : "bg-myTheme-light text-myTheme-lightTextBody hover:bg-myTheme-light/80 border border-myTheme-accent/20"
      }`}
    >
      <div className="flex items-center justify-between">
        <span className="text-xs sm:text-sm break-all overflow-hidden text-left">
          {tag}
        </span>
        {isSelected && count > 0 && (
          <span className="ml-2 text-xs bg-myTheme-light/30 text-myTheme-light px-2 py-0.5 rounded-full">
            {count}
          </span>
        )}
      </div>
    </button>
  );
};

export default TagView;
