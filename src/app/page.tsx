import QuickTabs from "./components/QuickTabs";
import TopReviews from "./components/TopReviews";
import HeroSection from "./components/HeroSection";
import ReviewCategories from "./components/CompanyCategories";
import ValueProposition from "./components/ValueProposition";
import { Metadata } from "next";
import AdvertCard from "./components/advert-components/AdvertCard";
// import FullWidthAdvert from "./components/advert-components/FullWidthAdvert";
// import FeaturedProducts from "./components/FeaturedProducts";
import TopReviewers from "./components/TopReviewers";
import Link from "next/link";

// Override any noindex directives with explicit indexing instructions
export const metadata: Metadata = {
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
    },
  },
};

export default function Home() {
  return (
    <div className="flex h-full w-full flex-col justify-start bg-gradient-to-b from-myTheme-lightbg via-myTheme-light to-white">
      {/* Hero Section Container with higher stacking context */}
      <div className="relative z-[50] overflow-visible">
        <div className="w-full relative">
          <HeroSection />
        </div>
      </div>

      {/* Write a Review Link - Clean text link */}
      <div className="flex justify-center py-6 md:py-8 px-4">
        <Link
          href="/write-review"
          className="text-myTheme-primary hover:text-myTheme-secondary font-semibold text-lg hover:underline transition-all duration-200 ease-in-out"
        >
          ✍️ Write a Review
        </Link>
      </div>

      {/* Recent Reviews Section */}
      <div className="flex w-full flex-col justify-center relative z-[4] mb-8">
        <div className="flex w-full h-full flex-row flex-wrap sm:px-4 px-4 md:px-2 pb-4 space-y-2">
          <TopReviews />
        </div>
      </div>

      {/* Quick Tabs with lower stacking context */}
      <div className="relative z-[1]">
        <QuickTabs />
      </div>

      <div className="">
        {/* <FullWidthAdvert
          title="Showcase Your Brand Here!"
          description="Reach thousands of users with our prominent full-width banner. Ideal for brand awareness campaigns."
          imageUrl="/your-brand-here.jpg"
          link="#your-landing-page"
        /> */}
      </div>
      <div className="flex w-full flex-col justify-center relative z-[4]">
        {/* Top Reviewers Section */}
        <div className="mb-12">
          <TopReviewers />
        </div>
        {/* Featured Products Section */}
        {/* <div className="mb-12">
          <FeaturedProducts />
        </div> */}
        <div className="mb-12">
          <ReviewCategories />
        </div>
        <div className="mb-12">
          <ValueProposition />
        </div>
      </div>
    </div>
  );
}
