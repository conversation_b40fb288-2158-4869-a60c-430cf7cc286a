// Export all interfaces
export interface ProductAnalytics {
  id: string;
  productId: string;
  totalViews: number;
  uniqueVisitors: number;
  averageViewDuration: number;
  peakHours: Record<string, number>;
  weekdayStats: Record<string, number>;
  lastUpdated: Date;
}

export interface ProductViewEvent {
  id: string;
  productId: string;
  userId: string | null;
  sessionId: string;
  timestamp: Date;
  duration: number | null;
  source: string | null;
  deviceType: string | null;
  isNewUser: boolean;
  isThrottled: boolean;
}

// Analytics period interface
export interface iAnalyticsPeriod {
  startDate: Date;
  endDate: Date;
}

// Business analytics interface
export interface iBusinessAnalytics {
  id: string;
  businessId: string;
  totalViews: number;
  uniqueVisitors: number;
  totalReviews: number;
  averageRating: number;
  viewsPerDay: Array<{ date: string; views: number }>;
  trafficSources: Array<{ source: string; count: number }>;
  deviceTypes: Array<{ type: string; count: number }>;
  conversionRate: number;
  topProducts: Array<{
    id: string;
    name: string;
    views: number;
    conversion: number;
  }>;
  lastUpdated: Date;
}

// Traffic source interface
export interface iTrafficSource {
  id: string;
  businessId: string;
  source: string;
  medium: string;
  visitors: number;
  sessions: number;
  bounceRate: number;
  conversionRate: number;
  lastUpdated: Date;
}

// Formatting interfaces
export interface ViewsPerDay {
  date: string;
  views: number;
}

export interface DeviceTypeStats {
  type: string;
  count: number;
}

export interface TopProductStats {
  id: string;
  name: string;
  views: number;
  conversion: number;
}

// Traffic metrics interface
export interface TrafficSourceMetrics {
  source: string;
  medium: string;
  visitors: number;
  sessions: number;
  bounceRate: number;
  conversionRate: number;
  lastUpdated: Date;
}

// Type aliases
export type ProductAnalyticsData = ProductAnalytics;