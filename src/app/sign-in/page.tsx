"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";

const SIGN_IN_URL = "https://accounts.reviewit.gy/sign-in";
const SIGN_UP_URL = "https://accounts.reviewit.gy/sign-up";
const REDIRECT_DELAY = 3; // seconds

export default function SignInPage() {
    const router = useRouter();
    const { isSignedIn, isLoaded } = useUser();
    const [showSSOCallback, setShowSSOCallback] = useState(false);

    useEffect(() => {
        if (!isLoaded) return;

        if (isSignedIn) {
            router.replace("/");
            return;
        }

        // Check if the URL contains #/sso-callback
        if (window.location.hash === "#/sso-callback") {
            setShowSSOCallback(true);
            const timer = setTimeout(() => {
                window.location.href = SIGN_UP_URL;
            }, REDIRECT_DELAY * 1000);
            return () => clearTimeout(timer);
        }

        // If just /sign-in, redirect to account portal sign-in
        window.location.href = SIGN_IN_URL;
    }, [isSignedIn, isLoaded, router]);

    if (showSSOCallback) {
        return (
            <div style={{ textAlign: "center", marginTop: "4rem" }}>
                <h1>This account hasn&apos;t signed up yet.</h1>
                <p>
                    Redirecting you to the sign up page in {REDIRECT_DELAY} seconds...
                </p>
                <p>
                    If you are not redirected, <a href={SIGN_UP_URL}>click here</a>.
                </p>
            </div>
        );
    }

    // Optionally, show a loading spinner or nothing
    return null;
} 