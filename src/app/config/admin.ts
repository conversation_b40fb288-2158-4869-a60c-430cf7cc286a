/**
 * Admin configuration
 * This file contains admin email list used ONLY for automatic role assignment during user signup.
 * For admin route protection, use database role-based authentication instead.
 */

// Array of authorized admin emails - loaded from environment variables for security
// Format: ADMIN_EMAILS=<EMAIL>,<EMAIL>,<EMAIL>
export const authorizedAdminEmails = process.env.ADMIN_EMAILS?.split(',').map(email => email.trim()) || [];

/**
 * Check if a user's email is in the admin list for automatic role assignment during signup
 * NOTE: This should NOT be used for route protection - use database roles instead
 * @param email The user's email to check
 * @returns boolean indicating if the user should be assigned admin role during signup
 */
export function isAdmin(email: string | undefined | null): boolean {
    if (!email) return false;
    
    // Normalize email to lowercase for comparison
    const normalizedEmail = email.toLowerCase().trim();
    const normalizedAdminEmails = authorizedAdminEmails.map(adminEmail => adminEmail.toLowerCase().trim());
    
    const isAdminUser = normalizedAdminEmails.includes(normalizedEmail);
    
    // Log admin role assignments for security monitoring
    if (isAdminUser) {
        console.log(`[SECURITY] Admin role assigned to email: ${normalizedEmail}`);
    }
    
    return isAdminUser;
}

/**
 * Get the list of authorized admin emails (for debugging/monitoring)
 * @returns Array of admin emails from environment variables
 */
export function getAdminEmailsList(): string[] {
    return authorizedAdminEmails;
}

/**
 * Validate that admin emails are properly configured
 * @returns boolean indicating if admin emails are configured
 */
export function validateAdminConfig(): boolean {
    if (authorizedAdminEmails.length === 0) {
        console.warn('[SECURITY] No admin emails configured in ADMIN_EMAILS environment variable');
        return false;
    }
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = authorizedAdminEmails.filter(email => !emailRegex.test(email));
    
    if (invalidEmails.length > 0) {
        console.error('[SECURITY] Invalid admin emails detected:', invalidEmails);
        return false;
    }
    
    console.log(`[SECURITY] Admin configuration validated. ${authorizedAdminEmails.length} admin emails configured.`);
    return true;
} 