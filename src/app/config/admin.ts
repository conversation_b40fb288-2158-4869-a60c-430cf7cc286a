/**
 * Admin configuration
 * This file contains admin email list used ONLY for automatic role assignment during user signup.
 * For admin route protection, use database role-based authentication instead.
 */

// Array of authorized admin emails - used ONLY for automatic role assignment during signup
export const authorizedAdminEmails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
];

/**
 * Check if a user's email is in the admin list for automatic role assignment during signup
 * NOTE: This should NOT be used for route protection - use database roles instead
 * @param email The user's email to check
 * @returns boolean indicating if the user should be assigned admin role during signup
 */
export function isAdmin(email: string | undefined | null): boolean {
    if (!email) return false;
    return authorizedAdminEmails.includes(email);
} 