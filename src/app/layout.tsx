import "@/app/globals.css";
import "@/app/styles/owner-badge.css";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { Poppins } from "next/font/google";
import Navbar from "./components/Navbar";
import { <PERSON><PERSON>rovider } from "@clerk/nextjs";
import QueryProvider from "./util/QueryProvider";
import JotaiProvider from "./util/JotaiProvider";
import type { Metadata, Viewport } from "next";
import Script from "next/script";

const APP_NAME = "ReviewIt";
const APP_DEFAULT_TITLE = "ReviewIt - Your Voice, Your Choice | Review Platform";
const APP_TITLE_TEMPLATE = "%s - ReviewIt";
const DEFAULT_OG_IMAGE =
  "https://res.cloudinary.com/dhglzlaqf/image/upload/v1724077586/reviewit/logo_eqake5.png";
const APP_DESCRIPTION =
  "ReviewIt (ReviewIt.gy) - The leading review platform in Guyana. Share and read reviews on businesses, products, and services. Find trusted reviews in Guyana from real customers. Your Voice, Your Choice.";

const poppins = Poppins({
  weight: ["400", "500", "600", "700", "800", "900"],
  subsets: ["latin"],
  style: "normal",
});

// Default metadata that can be overridden by pages
export const metadata: Metadata = {
  metadataBase: new URL("https://reviewit.gy"),
  applicationName: APP_NAME,
  title: {
    default: APP_DEFAULT_TITLE,
    template: APP_TITLE_TEMPLATE,
  },
  description: APP_DESCRIPTION,
  manifest: "/manifest.json",
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
    },
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: APP_DEFAULT_TITLE,
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: "website",
    siteName: APP_NAME,
    title: {
      default: APP_DEFAULT_TITLE,
      template: APP_TITLE_TEMPLATE,
    },
    description: APP_DESCRIPTION,
    images: [DEFAULT_OG_IMAGE],
  },
  twitter: {
    card: "summary_large_image",
    title: {
      default: APP_DEFAULT_TITLE,
      template: APP_TITLE_TEMPLATE,
    },
    description: APP_DESCRIPTION,
    images: [DEFAULT_OG_IMAGE],
  },
  other: {
    'mobile-web-app-capable': 'yes',
  },
};

export const viewport: Viewport = {
  themeColor: "#FFFFFF",
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {/* Google Tag Manager Script */}
        <Script
          id="google-analytics"
          async
          src="https://www.googletagmanager.com/gtag/js?id=G-D99RMSQKN0" // Replace with your Measurement ID
        />
        <Script id="google-analytics-init" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', 'G-D99RMSQKN0'); // Replace with your Measurement ID
          `}
        </Script>
      </head>
      <body className={`${poppins.className} bg-myTheme-lightbg  `}>
        <ClerkProvider>
          <QueryProvider>
            <JotaiProvider>
              <Navbar>{children}</Navbar>
              <ReactQueryDevtools initialIsOpen={false} />
            </JotaiProvider>
          </QueryProvider>
        </ClerkProvider>
      </body>
    </html>
  );
}
