import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';
import { getAuth } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
    try {
        const { userId } = getAuth(request);
        const { productId, duration, scrollDepth } = await request.json();

        if (!productId) {
            return NextResponse.json(
                { error: 'Product ID is required' },
                { status: 400 }
            );
        }

        // Update the view event with duration and scroll depth
        const updateQuery = `
            UPDATE "ProductViewEvent"
            SET "duration" = ${duration || 0}, "scrollDepth" = ${scrollDepth || 0}
            WHERE "productId" = '${productId}' AND "userId" = '${userId || 'anonymous'}'
            ORDER BY "timestamp" DESC
            LIMIT 1
            RETURNING *
        `;

        const updatedEvent = await prisma.$queryRawUnsafe(updateQuery) as any[];

        // Update user interaction with average time spent
        if (userId) {
            const interactionQuery = `
                UPDATE "UserProductInteraction"
                SET "averageTimeSpent" = (
                    "averageTimeSpent" * "viewCount" + ${duration || 0}
                ) / ("viewCount" + 1)
                WHERE "userId" = '${userId}' AND "productId" = '${productId}'
                RETURNING *
            `;

            await prisma.$queryRawUnsafe(interactionQuery);
        }

        // Update product analytics with new average duration
        const analyticsQuery = `
            UPDATE "ProductAnalytics"
            SET "averageViewDuration" = (
                "averageViewDuration" * "totalViews" + ${duration || 0}
            ) / ("totalViews" + 1)
            WHERE "productId" = '${productId}'
            RETURNING *
        `;

        await prisma.$queryRawUnsafe(analyticsQuery);

        return NextResponse.json({ success: true, event: updatedEvent[0] });
    } catch (error) {
        console.error('[Analytics API] Error updating view:', error);
        return NextResponse.json(
            { error: 'Failed to update view' },
            { status: 500 }
        );
    }
} 