import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
    try {
        // Log the available models
        console.log('Available models:', Object.keys(prisma));

        // Try to access the ProductClaim model
        const claims = await prisma.productClaim.findMany({
            take: 1,
        });
        console.log('Claims:', claims);
    } catch (error) {
        console.error('Error:', error);
    } finally {
        await prisma.$disconnect();
    }
}

main(); 