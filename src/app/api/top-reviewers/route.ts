export const dynamic = 'force-dynamic';
import { NextResponse } from 'next/server';
import { getTopReviewersFromCache, getCacheStats } from '@/app/util/databaseAnalytics';

export async function GET() {
    try {
        console.log("getTopReviewersFromCache endpoint called");
        // Log current cache stats before fetch
        const beforeStats = getCacheStats();
        console.log("Cache stats before fetch:", beforeStats);

        const topReviewersResponse = await getTopReviewersFromCache(6);
        console.log("topReviewersResponse: ", topReviewersResponse);

        // Log cache stats after fetch
        const afterStats = getCacheStats();
        console.log("Cache stats after fetch:", afterStats);

        // Create response with headers to prevent service worker caching
        const response = NextResponse.json({
            success: topReviewersResponse.success,
            data: topReviewersResponse.data,
        });

        // Add headers to prevent service worker caching
        response.headers.set('Service-Worker-Allowed', 'none');
        response.headers.set('Cache-Control', 'no-store, must-revalidate');
        response.headers.set('Pragma', 'no-cache');
        response.headers.set('Expires', '0');

        return response;
    } catch (error) {
        console.error('Error fetching top reviewers:', error);
        return NextResponse.json(
            {
                success: false,
                error: 'Failed to fetch top reviewers',
            },
            { status: 500 }
        );
    }
}