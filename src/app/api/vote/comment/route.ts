import { prisma } from "@/app/util/prismaClient";
import { NextResponse, NextRequest } from "next/server";
import { getAuth, clerkClient } from "@clerk/nextjs/server";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { invalidateCachesOnComment } from "@/app/util/analytics/cache";

export async function POST(request: NextRequest) {
    try {
        const { commentId, voteType } = await request.json();
        const { sessionClaims } = getAuth(request as any);
        const clerkClaimsData = sessionClaims as any;

        console.log('Vote API - Request data:', { commentId, voteType });

        // Ensure user exists in database
        if (!(await userInDb(clerkClaimsData.userId))) {
            await addUserToDb(clerkClaimsData);
        }

        // Get user's database ID from Clerk metadata
        const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
        const userId = clerkUserData.publicMetadata.id as string;

        console.log('Vote API - User info:', {
            clerkId: clerkClaimsData.userId,
            userId,
            publicMetadata: clerkUserData.publicMetadata
        });

        // Check if user has already voted
        const existingVote = await prisma.commentVote.findUnique({
            where: {
                commentId_clerkUserId: {
                    commentId,
                    clerkUserId: clerkClaimsData.userId,
                },
            },
        });

        console.log('Vote API - Existing vote:', existingVote);

        // Start a transaction to handle the vote
        const result = await prisma.$transaction(async (tx) => {
            if (existingVote) {
                // If user has already voted, update or remove the vote
                if (existingVote.voteType === voteType) {
                    // Remove vote if clicking the same button again
                    await tx.commentVote.delete({
                        where: {
                            id: existingVote.id,
                        },
                    });

                    // Update comment vote counts
                    await tx.comment.update({
                        where: { id: commentId },
                        data: {
                            upvotes: voteType === 'UP' ? { decrement: 1 } : undefined,
                            downvotes: voteType === 'DOWN' ? { decrement: 1 } : undefined,
                        },
                    });

                    console.log('Vote API - Vote removed:', { voteType });
                    return { action: 'removed' };
                } else {
                    // Change vote type
                    await tx.commentVote.update({
                        where: {
                            id: existingVote.id,
                        },
                        data: {
                            voteType,
                        },
                    });

                    // Update comment vote counts
                    await tx.comment.update({
                        where: { id: commentId },
                        data: {
                            upvotes: voteType === 'UP' ? { increment: 1 } : { decrement: 1 },
                            downvotes: voteType === 'DOWN' ? { increment: 1 } : { decrement: 1 },
                        },
                    });

                    console.log('Vote API - Vote changed:', {
                        from: existingVote.voteType,
                        to: voteType
                    });
                    return { action: 'changed' };
                }
            } else {
                // Create new vote
                await tx.commentVote.create({
                    data: {
                        commentId,
                        userId,
                        clerkUserId: clerkClaimsData.userId,
                        voteType,
                    },
                });

                // Update comment vote counts
                await tx.comment.update({
                    where: { id: commentId },
                    data: {
                        upvotes: voteType === 'UP' ? { increment: 1 } : undefined,
                        downvotes: voteType === 'DOWN' ? { increment: 1 } : undefined,
                    },
                });

                console.log('Vote API - New vote added:', { voteType });
                return { action: 'added' };
            }
        });

        // Fetch the updated comment with its votes
        const updatedComment = await prisma.comment.findUnique({
            where: { id: commentId },
            include: {
                review: {
                    select: {
                        productId: true,
                    },
                },
                votes: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                userName: true,
                                firstName: true,
                                lastName: true,
                                avatar: true,
                                clerkUserId: true,
                            }
                        }
                    }
                },
                user: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                        clerkUserId: true,
                        email: true,
                        createdDate: true,
                        isDeleted: true,
                    }
                },
                replies: {
                    include: {
                        votes: {
                            include: {
                                user: {
                                    select: {
                                        id: true,
                                        userName: true,
                                        firstName: true,
                                        lastName: true,
                                        avatar: true,
                                        clerkUserId: true,
                                    }
                                }
                            }
                        },
                        user: true,
                    }
                }
            },
        });

        console.log('Vote API - Updated comment:', {
            id: updatedComment?.id,
            upvotes: updatedComment?.upvotes,
            downvotes: updatedComment?.downvotes,
            votesCount: updatedComment?.votes?.length
        });

        // Look for the user's vote in the updated comment
        const userVoteInUpdatedComment = updatedComment?.votes?.find(vote => vote.clerkUserId === clerkClaimsData.userId);
        console.log('Vote API - User vote in updated comment:', userVoteInUpdatedComment);

        if (updatedComment) {
            await invalidateCachesOnComment(updatedComment.review.productId);
        }

        return NextResponse.json({
            success: true,
            status: 200,
            data: {
                ...result,
                comment: updatedComment,
            },
        });

    } catch (error) {
        console.error('Error processing comment vote:', error);
        return NextResponse.json({
            success: false,
            status: 500,
            error: 'Failed to process comment vote',
        });
    }
} 