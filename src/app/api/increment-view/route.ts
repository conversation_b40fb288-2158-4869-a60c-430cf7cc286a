import { prisma } from "@/app/util/prismaClient";
import { NextRequest, NextResponse } from "next/server";
import { incrementViewCountInRedis, getCurrentViewCount } from '@/app/util/databaseAnalytics';

export async function POST(request: NextRequest) {
    interface Body {
        productId: string;
    }

    const body: Body = await request.json();
    console.log(`[Increment View] Received request for productId: ${body.productId}`);

    try {
        // Increment view count in Redis (high-performance counter)
        const redisIncrement = await incrementViewCountInRedis(body.productId);
        console.log(`[Increment View] Redis increment result: ${redisIncrement}`);

        // Get current total view count (database + Redis)
        const totalViewCount = await getCurrentViewCount(body.productId);
        console.log(`[Increment View] Total view count: ${totalViewCount}`);

        return NextResponse.json({
            success: true,
            status: 200,
            data: {
                id: body.productId,
                viewCount: totalViewCount,
            },
        });
    } catch (error) {
        console.error(`[Increment View] Error incrementing view count: ${error}`);
        return NextResponse.json({
            success: false,
            status: 500,
            message: "Failed to increment view count"
        });
    } finally {
        await prisma.$disconnect();
    }
}