import { ImageResponse } from 'next/og'
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/app/util/prismaClient'

export const runtime = 'edge'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const reviewId = searchParams.get('id')
    const productId = searchParams.get('productid')

    // Debug logging
    console.log('[OG Debug] Request URL:', request.url);
    console.log('[OG Debug] Review ID:', reviewId);
    console.log('[OG Debug] Product ID:', productId);

    let title = 'Review It'
    let description = 'Share and read reviews on anything'
    let rating = '0'
    let type = 'product'
    let imageUrl = undefined

    // If we have a review ID, fetch the review with product data
    if (reviewId) {
      const review = await prisma.review.findFirst({
        where: { id: reviewId },
        include: {
          product: true,
          user: true
        }
      });

      if (review) {
        title = review.title;
        description = `Review of ${review.product?.name} by @${review.user?.userName}`;
        rating = review.rating.toString();
        type = 'review';
        imageUrl = review.product?.display_image;
      }
    }

    // If image URL is provided and it's a social media crawler, include the image
    const userAgent = request.headers.get('user-agent') || '';
    const isSocialMediaCrawler =
      userAgent.includes('facebookexternalhit') ||
      userAgent.includes('Twitterbot') ||
      userAgent.includes('LinkedInBot') ||
      userAgent.includes('WhatsApp');

    console.log('[OG Debug] Is Social Crawler:', isSocialMediaCrawler);
    console.log('[OG Debug] Image URL:', imageUrl);

    // If image URL is provided and not a social crawler, redirect to the image
    if (imageUrl && !isSocialMediaCrawler) {
      console.log('[OG Debug] Redirecting to:', decodeURIComponent(imageUrl));
      return NextResponse.redirect(decodeURIComponent(imageUrl), 302);
    }

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#fff',
            padding: '40px',
            position: 'relative',
          }}
        >
          {/* If we have an image URL and it's a social crawler, use it as background */}
          {imageUrl && isSocialMediaCrawler && (
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundImage: `url(${decodeURIComponent(imageUrl)})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                opacity: 0.3,
              }}
            />
          )}

          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '20px',
              position: 'relative',
            }}
          >
            {/* Logo placeholder - replace URL with actual logo */}
            <img
              src="https://res.cloudinary.com/dhglzlaqf/image/upload/v1724077586/reviewit/logo_eqake5.png"
              alt="Review It Logo"
              width="64"
              height="64"
            />
            <h1
              style={{
                fontSize: '60px',
                fontWeight: 'bold',
                color: '#000',
                marginLeft: '20px',
              }}
            >
              Review It
            </h1>
          </div>
          <h2
            style={{
              fontSize: '48px',
              fontWeight: 'bold',
              color: '#000',
              textAlign: 'center',
              marginBottom: '20px',
              position: 'relative',
            }}
          >
            {title}
          </h2>
          {type === 'review' && (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '20px',
              }}
            >
              <span
                style={{
                  fontSize: '36px',
                  fontWeight: 'bold',
                  color: '#fbbf24',
                }}
              >
                {'★'.repeat(Math.round(Number(rating)))}
                {'☆'.repeat(5 - Math.round(Number(rating)))}
              </span>
            </div>
          )}
          <p
            style={{
              fontSize: '24px',
              color: '#666',
              textAlign: 'center',
              maxWidth: '800px',
            }}
          >
            {description}
          </p>
        </div>
      ),
      {
        width: 800,
        height: 420,
      }
    )
  } catch (e) {
    console.error(e)
    return new Response('Failed to generate OG image', { status: 500 })
  }
}
