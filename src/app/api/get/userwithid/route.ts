import { prisma } from "@/app/util/prismaClient";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("[API] Request body:", body);

    if (!body || typeof body !== 'object') {
      console.log("[API] Invalid request body");
      return NextResponse.json({
        success: false,
        status: 400,
        error: "Invalid request body",
      });
    }

    const { userId } = body;
    console.log("[API] Received request for userId:", userId);

    if (!userId) {
      console.log("[API] No userId provided");
      return NextResponse.json({
        success: false,
        status: 400,
        error: "User ID is required",
      });
    }

    console.log("[API] Looking up user in database...");
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
      },
      include: {
        comments: {
          include: {
            review: true,
          },
        },
        reviews: {
          include: {
            product: true,
          },
        },
        likedReviews: true,
      },
    });

    if (!user) {
      console.log("[API] User not found in database");
      return NextResponse.json({
        success: false,
        status: 404,
        error: "User not found",
      });
    }

    console.log("[API] User found:", user.id);
    return NextResponse.json({
      success: true,
      status: 200,
      data: user,
    });
  } catch (error) {
    console.error("[API] Error fetching user:", error);
    let errorMessage = "An unknown error occurred";

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      success: false,
      status: 500,
      error: errorMessage,
    });
  }
}
