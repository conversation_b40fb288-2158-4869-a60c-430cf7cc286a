import { NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';
import { auth } from '@clerk/nextjs/server';

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    // Get the current user (admin)
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    // Get all products
    const products = await prisma.product.findMany({
      where: {
        isDeleted: false,
      },
      include: {
        business: true,
        createdBy: true,
        reviews: {
          include: {
            user: true,
          },
        },
      },
      orderBy: {
        createdDate: 'desc',
      },
    });

    return NextResponse.json({
      success: true,
      data: products,
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
} 