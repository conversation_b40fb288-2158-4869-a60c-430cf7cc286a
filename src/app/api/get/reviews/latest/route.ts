import { iReview } from "@/app/util/Interfaces";
import { prisma } from "@/app/util/prismaClient";
import { getLatestReviewsFromCache } from "@/app/util/databaseAnalytics";
import { sanitizeDeletedCommentsInReviews } from "@/app/util/sanitizeDeletedComments";
import { NextRequest, NextResponse } from "next/server";
import { cleanReview, cleanReviews, createFilter } from "@/app/store/badWordsFilter";
const filter = createFilter();

export async function POST(request: NextRequest) {
  try {
    // Use cached latest reviews function
    const result = await getLatestReviewsFromCache();

    if (!result.success) {
      throw new Error(result.data || 'Failed to fetch latest reviews');
    }

    const filter = await createFilter();

    // Apply bad words filter to the reviews
    const treatedReviews = sanitizeDeletedCommentsInReviews(result.data as iReview[]);
    let cleanedReviews;
    try {
      cleanedReviews = cleanReviews(filter, treatedReviews);
    } catch {
      cleanedReviews = treatedReviews;
    }

    return NextResponse.json({
      success: true,
      status: 200,
      data: cleanedReviews,
    });
  } catch (error) {
    let e = error as Error;
    return NextResponse.json({
      success: false,
      status: 500,
      data: e.message.slice(0, 500) + "...",
    });
  }
}
