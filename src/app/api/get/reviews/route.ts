import { iReview } from "@/app/util/Interfaces";
import { prisma } from "@/app/util/prismaClient";
import { getProductReviewsFromCache } from "@/app/util/databaseAnalytics";
import { sanitizeDeletedCommentsInReviews } from "@/app/util/sanitizeDeletedComments";
import { NextRequest, NextResponse } from "next/server";
import {
  cleanReview,
  cleanReviews,
  createFilter,
} from "@/app/store/badWordsFilter";
const filter = createFilter();

export async function POST(request: NextRequest) {
  interface Body {
    id: string;
    isPublic: boolean;
    user: boolean;
    product: boolean;
    comments: boolean;
  }

  const body: Body = await request.json();

  try {
    // Use cached reviews with complex nested data
    const { reviews: cachedReviews, product } = await getProductReviewsFromCache(
      body.id,
      body.isPublic,
      body.user,
      body.product,
      body.comments
    );
    
    let reviews = cachedReviews as iReview[];
    let treatedReviews = sanitizeDeletedCommentsInReviews(reviews as iReview[]);
    try {
      treatedReviews = cleanReviews(await filter, treatedReviews);
    } catch (error) {
      treatedReviews = treatedReviews;
    }
    reviews = treatedReviews as iReview[];
    return NextResponse.json({
      success: true,
      status: 200,
      data: reviews,
    });
  } catch (error) {
    let e = error as Error;
    return NextResponse.json({
      success: false,
      status: 500,
      data: e.message.slice(0, 500) + "...",
    });
  }
}
