// Importing necessary modules and packages
import { prisma } from "@/app/util/prismaClient";
import { NextResponse, NextRequest } from "next/server";
import { clerkClient, getAuth } from "@clerk/nextjs/server";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { iProduct, UserDATA, iProductClaim } from "@/app/util/Interfaces";
import { invalidateAdminCache } from "@/app/util/databaseAnalytics";

// Exporting the POST function that handles the API request
export async function POST(request: NextRequest) {
  try {
    // Get the claim data from the request body
    const { product, contactInfo, additionalInfo, images } = await request.json() as {
      product: iProduct;
      contactInfo: string | { name: string; email: string; phone: string };
      additionalInfo: string;
      images: string[];
    };

    // Validate product exists
    if (!product || !product.id) {
      return NextResponse.json({
        success: false,
        status: 400,
        message: 'Invalid product data',
        errorCode: 'INVALID_PRODUCT',
      });
    }

    // Validate required fields
    if (!contactInfo) {
      return NextResponse.json({
        success: false,
        status: 400,
        message: 'Contact information is required',
        errorCode: 'MISSING_CONTACT_INFO',
      });
    }

    // Parse contactInfo if it's a string
    let parsedContactInfo = contactInfo;
    if (typeof contactInfo === 'string') {
      try {
        parsedContactInfo = JSON.parse(contactInfo);
      } catch (e) {
        // If parsing fails, use the string as is
        parsedContactInfo = contactInfo;
      }
    }

    // Validate contact info fields if it's an object
    if (typeof parsedContactInfo === 'object' && parsedContactInfo !== null) {
      const contactObj = parsedContactInfo as { name?: string; email?: string; phone?: string };
      if (!contactObj.name || !contactObj.email || !contactObj.phone) {
        return NextResponse.json({
          success: false,
          status: 400,
          message: 'Contact information must include name, email, and phone',
          errorCode: 'INVALID_CONTACT_INFO',
        });
      }
    }

    if (!additionalInfo) {
      return NextResponse.json({
        success: false,
        status: 400,
        message: 'Additional information is required',
        errorCode: 'MISSING_ADDITIONAL_INFO',
      });
    }

    // Validate images array
    const validImages = Array.isArray(images) ? images.filter(img => typeof img === 'string' && img.length > 0) : [];

    // Get the current product state from database to verify it can be claimed
    const existingProduct = await prisma.product.findUnique({
      where: { id: product.id },
      include: { business: true, claims: true }
    });

    if (!existingProduct) {
      return NextResponse.json({
        success: false,
        status: 404,
        message: 'Product not found',
        errorCode: 'PRODUCT_NOT_FOUND',
      });
    }

    // Check if product is already claimed
    if (existingProduct.businessId || existingProduct.hasOwner) {
      return NextResponse.json({
        success: false,
        status: 400,
        message: 'This product has already been claimed',
        errorCode: 'PRODUCT_ALREADY_CLAIMED',
      });
    }

    // Check if there's an existing pending claim
    const pendingClaim = existingProduct.claims &&
      Array.isArray(existingProduct.claims) &&
      existingProduct.claims.length > 0 &&
      existingProduct.claims.some((claim: iProductClaim) => claim.status === 'PENDING');

    if (pendingClaim) {
      return NextResponse.json({
        success: false,
        status: 400,
        message: 'There is already a pending claim for this product',
        errorCode: 'EXISTING_CLAIM',
      });
    }

    // Initialize variables for Clerk user data
    let clerkUserData = null;
    let userIdFromClerk = null;

    // Extract the session claims from the request
    const { sessionClaims } = getAuth(request as any);
    // Cast the session claims to the `UserDATA` type
    const clerkClaimsData = sessionClaims as unknown as UserDATA;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        message: 'Unauthorized',
        errorCode: 'UNAUTHORIZED',
      });
    }

    // Check if the user already exists in the database
    if (!(await userInDb(clerkClaimsData.userId))) {
      // If the user doesn't exist, create them
      clerkUserData = await addUserToDb(clerkClaimsData);
    } else {
      // If the user already exists, retrieve their data from the database
      clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
      // then add publicMetaData.id to the object
      if (clerkUserData.publicMetadata.id !== undefined) {
        userIdFromClerk = clerkUserData.publicMetadata.id as string;
      }
    }

    // Create the product claim
    const newClaim = await prisma.productClaim.create({
      data: {
        productId: product.id,
        userId: clerkClaimsData.userId,
        status: 'PENDING',
        contactInfo: typeof parsedContactInfo === 'object' ? JSON.stringify(parsedContactInfo) : parsedContactInfo,
        additionalInfo,
        images: validImages, // Use the validated images array
      },
      include: {
        product: true,
        user: true,
      }
    });

    // Invalidate admin cache since new claim needs review
    try {
      await invalidateAdminCache();
      console.log('Admin cache invalidated after new claim submission');
    } catch (cacheError) {
      console.warn('Failed to invalidate admin cache after claim submission:', cacheError);
      // Don't fail the request if cache invalidation fails
    }

    return NextResponse.json({
      success: true,
      status: 200,
      message: 'Claim submitted successfully. An administrator will review your claim.',
      data: {
        claim: newClaim
      },
    });

  } catch (error) {
    console.error("Error in claim-product:", error);
    const e = error as Error;
    return NextResponse.json({
      success: false,
      status: 500,
      message: e.message || 'An unexpected error occurred',
      errorCode: 'SERVER_ERROR',
    });
  }
}
