// Importing necessary modules and packages
import { prisma } from "@/app/util/prismaClient";
import { NextResponse, NextRequest } from "next/server";
import { clerkClient, auth } from "@clerk/nextjs/server";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { iComment, UserDATA, iUserNotification } from "@/app/util/Interfaces";
import { createUserNotification } from "@/app/util/NotificationFunctions";
import { invalidateCachesOnComment } from "@/app/util/analytics/cache";

// Exporting the POST function that handles the API request
export async function POST(request: NextRequest) {
  try {
    const comment = await request.json();
    const { userId: userIdFromClerk } = await auth();
    const clerkUserData = await (
      await clerkClient()
    ).users.getUser(userIdFromClerk as string);

    const createdComment = await prisma.comment.create({
      data: {
        body: comment.body,
        createdDate: new Date(),
        reviewId: comment.reviewId,
        userId: userIdFromClerk as string,
      },
      include: {
        review: {
          include: {
            product: {
              include: {
                business: true,
              },
            },
          },
        },
      },
    });

    if (
      createdComment.review.product?.business?.ownerId === userIdFromClerk &&
      !createdComment.review.ownerRespondedAt
    ) {
      await prisma.review.update({
        where: {
          id: createdComment.reviewId,
        },
        data: {
          ownerRespondedAt: new Date(),
        },
      });
    }

    // Create notification for the review owner
    const userNotification: iUserNotification = {
      id: createdComment.id,
      user_id: createdComment.review.userId, // Add the review owner's ID
      content: createdComment.body,
      read: false,
      notification_type: "comment",
      comment_id: createdComment.id,
      review_id: createdComment.reviewId,
      from_name: clerkUserData?.firstName || "Anonymous",
      from_id: userIdFromClerk as string,
      created_at: new Date(),
      parent_id: "", // Empty string since this is a direct comment, not a reply
      product_id: createdComment.review.productId,
    };

    await createUserNotification(userNotification);

    await invalidateCachesOnComment(createdComment.review.productId);

    return NextResponse.json({
      success: true,
      status: 200,
      data: createdComment,
    });
  } catch (error) {
    let e = error as Error;
    return NextResponse.json({
      success: false,
      status: 500,
      data: e,
    });
  }
}
