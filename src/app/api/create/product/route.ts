import { prisma } from "@/app/util/prismaClient";
import { NextResponse, NextRequest } from "next/server";
import { auth, clerkClient, getAuth } from "@clerk/nextjs/server";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { iProduct } from "@/app/util/Interfaces";
import { invalidateAllProductsCache, invalidateSearchCache } from "@/app/util/databaseAnalytics";

// Interface representing user data
interface UserDATA {
  avatar?: string;
  azp: string;
  email: string;
  exp: number;
  firstName: string;
  lastName: string;
  fullName: string;
  iat: number;
  iss: string;
  jti: string;
  nbf: number;
  sub: string;
  userId: string;
  userName: string;
  metadata: {
    id: string;
    userInDb: boolean;
  };
}

// Exporting the POST function that handles the API request
export async function POST(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  // Get the review data from the request body
  const product: iProduct = await request.json();
  product.tags = product.tags.map((tag) => tag.trim().toLowerCase());
  // Normalize URLs in arrays
  product.images = product.images.map((url) => url.trim());
  product.videos = product.videos.map((url) => url.trim());
  product.links = product.links.map((url) => url.trim());
  product.website = product.website.map((url) => url.trim());

  // Initialize a variable to store the Clerk user data
  let clerkUserData = null;
  let userIdFromClerk = null;
  try {
    // Extract the session claims from the request
    const { sessionClaims } = getAuth(request as any);
    // Cast the session claims to the `UserDATA` type
    const clerkClaimsData = sessionClaims as unknown as UserDATA;

    // Check if the user already exists in the database
    if (!(await userInDb(clerkClaimsData.userId))) {
      // If the user doesn't exist, create them
      clerkUserData = await addUserToDb(clerkClaimsData);
    } else {
      // If the user already exists, retrieve their data from the database
      clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
      // then add publicMetaData.id to the object
      if (clerkUserData.publicMetadata.id !== undefined) {
        userIdFromClerk = clerkUserData.publicMetadata.id as string;
      } else {
        return NextResponse.json(
          { success: false, data: "publicMetadata.id not found" },
          { status: 401 }
        );
      }
    }

    try {
      const clerkUserId = clerkUserData!.publicMetadata.id as string;
      const now = new Date();
      const createdProduct: iProduct = await prisma.product.create({
        data: {
          name: product.name,
          description: product.description,
          display_image: product.display_image,
          createdDate: product.createdDate,
          images: product.images,
          videos: product.videos,
          links: product.links,
          tags: product.tags,
          openingHrs: product.openingHrs,
          closingHrs: product.closingHrs,
          openingDays: product.openingDays || [],
          address: product.address,
          latitude: product.latitude,
          longitude: product.longitude,
          telephone: product.telephone,
          website: product.website,
          email: product.email,
          createdById: clerkUserData?.username ? clerkUserId : "",
          // Set timestamps for content tracking
          descriptionLastUpdatedAt: now,
          imagesLastUpdatedAt: product.images && product.images.length > 0 ? now : null,
        },
      });

      // Invalidate relevant caches after successful product creation
      try {
        await invalidateAllProductsCache();
        await invalidateSearchCache();
        console.log(`Cache invalidated for new product: ${createdProduct.id}`);
      } catch (cacheError) {
        console.warn('Failed to invalidate cache after product creation:', cacheError);
        // Don't fail the request if cache invalidation fails
      }

      return NextResponse.json({ success: true, data: createdProduct }, { status: 200 });
    } catch (error) {
      return NextResponse.json({ success: false, data: error }, { status: 500 });
    }
  } catch (error) {
    let e = error as Error;
    // Return an error response
    return NextResponse.json({ success: false, data: e }, { status: 500 });
  }
  // return NextResponse.json({
  //   success: false,
  //   status: 500,
  //   data: { "error": "something failed" },
  // });
}
