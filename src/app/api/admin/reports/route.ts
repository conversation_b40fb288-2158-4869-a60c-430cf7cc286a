export const dynamic = "force-dynamic";
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { getAuth } from '@clerk/nextjs/server';
import { PrismaClient } from '@prisma/client';

const prismaClient = new PrismaClient();

// Function to check if the user is an admin
async function isAdmin(userId: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
        where: { clerkUserId: userId },
        select: { role: true },
    });

    return user?.role === "ADMIN";
}

export async function GET(request: NextRequest) {
    try {
        // Check authentication
        const { userId } = getAuth(request);
        if (!userId) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        // Check admin authorization
        if (!(await isAdmin(userId))) {
            return NextResponse.json(
                { error: 'Forbidden: Admin access required' },
                { status: 403 }
            );
        }

        // Get query parameters
        const { searchParams } = request.nextUrl;
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const status = searchParams.get('status');
        const sortBy = searchParams.get('sortBy') || 'createdAt';
        const sortOrder = searchParams.get('sortOrder') || 'desc';

        // Calculate pagination
        const skip = (page - 1) * limit;

        // Build where clause
        const where = {
            ...(status && { status }),
        };

        // Get total count for pagination
        const total = await prismaClient.reviewReport.count({ where });

        // Get reports with pagination and sorting
        const reports = await prismaClient.reviewReport.findMany({
            where,
            include: {
                review: {
                    select: {
                        id: true,
                        title: true,
                        rating: true,
                        body: true,
                        user: {
                            select: {
                                id: true,
                                userName: true,
                                firstName: true,
                                lastName: true,
                            },
                        },
                    },
                },
                user: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                    },
                },
                resolver: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
            orderBy: {
                [sortBy]: sortOrder,
            },
            skip,
            take: limit,
        });

        return NextResponse.json({
            success: true,
            data: {
                reports,
                pagination: {
                    total,
                    page,
                    limit,
                    totalPages: Math.ceil(total / limit),
                },
            },
        });
    } catch (error) {
        console.error('Error fetching reports:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}