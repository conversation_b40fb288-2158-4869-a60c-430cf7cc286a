export const dynamic = "force-dynamic";
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { getAuth } from '@clerk/nextjs/server';

// Function to check if the user is an admin
async function isAdmin(userId: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
        where: { clerkUserId: userId },
        select: { role: true },
    });

    return user?.role === "ADMIN";
}

export async function GET(request: NextRequest) {
    try {
        // Check authentication
        const { userId } = getAuth(request);
        if (!userId) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        // Authorization check
        if (!(await isAdmin(userId))) {
            return NextResponse.json(
                { success: false, error: "Unauthorized access" },
                { status: 403 }
            );
        }

        // Get report counts by status
        const reportCounts = await prisma.reviewReport.groupBy({
            by: ['status'],
            _count: {
                status: true,
            },
        });

        // Get recent reports (last 7 days)
        const recentReports = await prisma.reviewReport.findMany({
            where: {
                createdAt: {
                    gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                },
            },
            include: {
                review: {
                    select: {
                        id: true,
                        title: true,
                        rating: true,
                    },
                },
                user: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
            take: 5,
        });

        // Get reports by day for the last 7 days
        const reportsByDay = await prisma.reviewReport.groupBy({
            by: ['createdAt'],
            where: {
                createdAt: {
                    gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                },
            },
            _count: {
                createdAt: true,
            },
            orderBy: {
                createdAt: 'asc',
            },
        });

        // Format the data
        const stats = {
            counts: reportCounts.reduce((acc, curr) => ({
                ...acc,
                [curr.status]: curr._count.status,
            }), {}),
            recentReports,
            reportsByDay: reportsByDay.map(day => ({
                date: day.createdAt.toISOString().split('T')[0],
                count: day._count.createdAt,
            })),
        };

        return NextResponse.json({
            success: true,
            data: stats,
        });
    } catch (error) {
        console.error('Error fetching report stats:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}