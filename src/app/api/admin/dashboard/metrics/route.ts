export const dynamic = "force-dynamic";
import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from "@/app/util/prismaClient";
import { getAdminDashboardMetricsFromCache } from "@/app/util/databaseAnalytics";
import { Prisma } from '@prisma/client';

// Function to check if the user is an admin
async function isAdmin(userId: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
        where: { clerkUserId: userId },
        select: { role: true },
    });

    return user?.role === "ADMIN";
}

export async function GET(request: NextRequest) {
    try {
        // Check authentication
        const { userId } = getAuth(request);
        if (!userId) {
            return NextResponse.json(
                { error: 'Unauthorized: Please sign in to access this resource' },
                { status: 401 }
            );
        }

        // Authorization check
        if (!(await isAdmin(userId))) {
            return NextResponse.json(
                { success: false, error: "Unauthorized access" },
                { status: 403 }
            );
        }

        // Get metrics data from cache
        const result = await getAdminDashboardMetricsFromCache();
        return NextResponse.json(result);
    } catch (error) {
        console.error('Error fetching dashboard metrics:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}
