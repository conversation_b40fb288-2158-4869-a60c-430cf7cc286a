import { NextResponse } from 'next/server';
import { withAdminAuth } from '@/app/middleware/adminAuth';
import { Prisma } from '@prisma/client';
import { prisma } from '@/app/util/prismaClient';
import { auth } from '@clerk/nextjs/server';

export const dynamic = 'force-dynamic';

async function handler(request: Request) {
    try {
        const { reviewIds, action, reason } = await request.json();

        console.log(`[REVIEW-MODERATE] Received request:`, { reviewIds, action, reason });

        if (!Array.isArray(reviewIds) || reviewIds.length === 0 || !action) {
            return NextResponse.json(
                { error: 'Invalid request parameters' },
                { status: 400 }
            );
        }

        // Validate action type
        if (!['APPROVE', 'REJECT', 'FLAG'].includes(action)) {
            return NextResponse.json(
                { error: 'Invalid action type' },
                { status: 400 }
            );
        }

        // Get the admin user from Clerk auth
        const { userId } = await auth();

        if (!userId) {
            return NextResponse.json(
                { error: 'Admin ID not found' },
                { status: 401 }
            );
        }

        console.log(`[REVIEW-MODERATE] Clerk user ID: ${userId}, Action: ${action}`);

        // Get the actual database user ID from the Clerk ID
        const adminUser = await prisma.user.findFirst({
            where: { clerkUserId: userId },
            select: { id: true, userName: true }
        });

        if (!adminUser) {
            console.error(`[REVIEW-MODERATE] Could not find admin user with Clerk ID ${userId}`);
            return NextResponse.json(
                { error: 'Admin user not found in database' },
                { status: 404 }
            );
        }

        console.log(`[REVIEW-MODERATE] Found admin user:`, adminUser);

        // Process each review WITHOUT using a transaction (to avoid rollback issues)
        const results = [];

        for (const reviewId of reviewIds) {
            try {
                // Log the values we're about to set
                const isApprove = action === 'APPROVE';
                const isFlag = action === 'FLAG';
                const shouldVerify = !isFlag;

                // First update the review separately to ensure it gets updated even if the event creation fails
                const updateData = {
                    isPublic: isApprove,
                    isVerified: shouldVerify,
                    verifiedBy: shouldVerify ? adminUser.id : undefined,
                    verifiedAt: shouldVerify ? new Date() : undefined,
                };

                console.log(`[REVIEW-MODERATE] Action: ${action}, isApprove: ${isApprove}, shouldVerify: ${shouldVerify}`);
                console.log(`[REVIEW-MODERATE] Updating review ${reviewId} with:`, JSON.stringify(updateData));

                // Get the review before update
                const reviewBefore = await prisma.review.findUnique({
                    where: { id: reviewId },
                    select: { id: true, isPublic: true, isVerified: true, verifiedBy: true, verifiedAt: true }
                });

                console.log(`[REVIEW-MODERATE] Review ${reviewId} before update:`, JSON.stringify(reviewBefore));

                if (!reviewBefore) {
                    throw new Error(`Review with ID ${reviewId} not found`);
                }

                // Update review status first as a separate operation
                const updatedReview = await prisma.review.update({
                    where: { id: reviewId },
                    data: updateData,
                    select: {
                        id: true,
                        isPublic: true,
                        isVerified: true,
                        verifiedBy: true,
                        verifiedAt: true
                    }
                });

                console.log(`[REVIEW-MODERATE] Review ${reviewId} after update:`, JSON.stringify(updatedReview));

                let moderationEvent = null;
                let moderationError = null;

                // Try to create the moderation event as a separate operation
                try {
                    moderationEvent = await prisma.moderationEvent.create({
                        data: {
                            reviewId,
                            adminId: adminUser.id,
                            action: action === 'APPROVE' ? 'APPROVED' :
                                action === 'REJECT' ? 'REJECTED' : 'FLAGGED',
                            reason,
                            createdAt: new Date()
                        },
                        select: { id: true, action: true, createdAt: true }
                    });

                    console.log(`[REVIEW-MODERATE] Created moderation event:`, JSON.stringify(moderationEvent));
                } catch (eventError) {
                    console.error(`[REVIEW-MODERATE] Error creating moderation event:`, eventError);
                    moderationError = eventError instanceof Error ? eventError.message : 'Unknown error';
                }

                // Double-check that the review was actually updated
                const finalCheck = await prisma.review.findUnique({
                    where: { id: reviewId },
                    select: { isVerified: true, isPublic: true }
                });

                console.log(`[REVIEW-MODERATE] Final verification:`, JSON.stringify(finalCheck));

                results.push({
                    reviewId,
                    success: true,
                    reviewData: updatedReview,
                    moderationEvent,
                    moderationError
                });
            } catch (error) {
                console.error(`[REVIEW-MODERATE] Error processing review ${reviewId}:`, error);
                results.push({
                    reviewId,
                    success: false,
                    error: error instanceof Error ? error.message : 'An unknown error occurred',
                });
            }
        }

        console.log(`[REVIEW-MODERATE] Processing completed:`, results);

        // Calculate summary
        const summary = {
            total: results.length,
            successCount: results.filter((r) => r.success).length,
            failureCount: results.filter((r) => !r.success).length,
        };

        return NextResponse.json({
            success: true,
            data: {
                summary,
                results,
            },
        });
    } catch (error) {
        console.error('[REVIEW-MODERATE] Error in bulk review processing:', error);
        return NextResponse.json(
            { success: false, error: 'Failed to process bulk action' },
            { status: 500 }
        );
    }
}

export const POST = withAdminAuth(handler); 