import { Prisma } from '@prisma/client';
import { prisma } from "@/app/util/prismaClient";
import { NextResponse, NextRequest } from "next/server";
import { auth } from "@clerk/nextjs/server";

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
    try {
        const { userId } = await auth();
        console.log('Auth check:', { userId });

        if (!userId) {
            return NextResponse.json({
                success: false,
                message: 'Unauthorized',
                status: 401,
            });
        }

        // Check if user has admin role in database
        const user = await prisma.user.findFirst({
            where: {
                clerkUserId: userId
            },
            select: {
                role: true,
                status: true
            }
        });

        if (!user || user.role !== "ADMIN" || user.status !== "ACTIVE") {
            console.log('User is not an admin');
            return NextResponse.json({
                success: false,
                message: 'Forbidden',
                status: 403,
            });
        }

        // Get query parameters
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const status = searchParams.get('status');
        const search = searchParams.get('search');

        console.log('Query params:', { page, limit, status, search });

        // Calculate skip value for pagination
        const skip = (page - 1) * limit;

        // Build where clause for filtering
        let where: Prisma.ProductClaimWhereInput = {};

        if (status) {
            where.status = status;
        }

        if (search) {
            where.OR = [
                {
                    product: {
                        name: {
                            contains: search,
                            mode: 'insensitive',
                        },
                    },
                },
                {
                    user: {
                        userName: {
                            contains: search,
                            mode: 'insensitive',
                        },
                    },
                },
                {
                    additionalInfo: {
                        contains: search,
                        mode: 'insensitive',
                    },
                },
            ];
        }

        console.log('Where clause:', where);

        // Get claims with pagination and filtering
        const [claims, total] = await Promise.all([
            prisma.productClaim.findMany({
                where,
                include: {
                    product: true,
                    user: {
                        select: {
                            id: true,
                            userName: true,
                            email: true,
                        },
                    },
                },
                orderBy: { createdAt: 'desc' },
                skip,
                take: limit,
            }),
            prisma.productClaim.count({ where }),
        ]);

        console.log('Found claims:', { count: claims.length, total });

        // Calculate pagination metadata
        const totalPages = Math.ceil(total / limit);
        const hasNextPage = page < totalPages;
        const hasPreviousPage = page > 1;

        return NextResponse.json({
            success: true,
            claims,
            pagination: {
                total,
                totalPages,
                currentPage: page,
                hasNextPage,
                hasPreviousPage,
            },
        });
    } catch (error) {
        console.error('Error fetching claims:', error);
        return NextResponse.json({
            success: false,
            message: 'Failed to fetch claims',
            status: 500,
        });
    }
} 
