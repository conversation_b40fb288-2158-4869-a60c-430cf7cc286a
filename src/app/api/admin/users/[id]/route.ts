import { NextResponse } from 'next/server';
import { validateUserUpdate } from '@/middleware/validation';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: Request, { params }: { params: { id: string } }) {
    const userId = params.id;
    
    try {
        const user = await prisma.user.findUnique({
            where: {
                id: userId,
            },
            // Include any relations you need
        });
        
        if (!user) {
            return NextResponse.json(
                { error: 'User not found' },
                { status: 404 }
            );
        }
        
        return NextResponse.json(user);
    } catch (error) {
        console.error('Error fetching user:', error);
        return NextResponse.json(
            { error: 'Failed to fetch user' },
            { status: 500 }
        );
    }
}

export async function PATCH(request: Request, { params }: { params: { id: string } }) {
    const userId = params.id;
    const data = await request.json();
    
    try {
        const updatedUser = await prisma.user.update({
            where: {
                id: userId,
            },
            data,
        });
        
        return NextResponse.json(updatedUser);
    } catch (error) {
        console.error('Error updating user:', error);
        return NextResponse.json(
            { error: 'Failed to update user' },
            { status: 500 }
        );
    }
}
