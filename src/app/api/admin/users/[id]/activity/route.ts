import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

export const dynamic = 'force-dynamic';

const prisma = new PrismaClient();

export async function GET(request: Request, { params }: { params: { id: string } }) {
    const userId = params.id;
    
    try {
        // Get user's reviews, comments, and products
        const userActivity = await prisma.user.findUnique({
            where: {
                id: userId,
            },
            include: {
                reviews: {
                    orderBy: {
                        createdDate: 'desc',
                    },
                    take: 10,
                },
                comments: {
                    orderBy: {
                        createdDate: 'desc',
                    },
                    take: 10,
                },
                product: {
                    orderBy: {
                        createdDate: 'desc',
                    },
                    take: 10,
                },
                adminActions: {
                    orderBy: {
                        createdAt: 'desc',
                    },
                    take: 10,
                },
                moderationEvents: {
                    orderBy: {
                        createdAt: 'desc',
                    },
                    take: 10,
                },
            },
        });

        if (!userActivity) {
            return NextResponse.json(
                { error: 'User not found' },
                { status: 404 }
            );
        }

        return NextResponse.json({
            success: true,
            data: userActivity,
        });
    } catch (error) {
        console.error('Error fetching user activity:', error);
        return NextResponse.json(
            { error: 'Failed to fetch user activity' },
            { status: 500 }
        );
    }
}
