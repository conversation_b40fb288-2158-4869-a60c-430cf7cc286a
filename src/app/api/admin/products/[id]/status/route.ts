import { NextResponse } from 'next/server';
import { withAdminAuth } from '@/app/middleware/adminAuth';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function handler(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const { id } = params;

        if (request.method === 'PATCH') {
            const data = await request.json();
            const { isDeleted, featuredPosition } = data;

            const product = await prisma.product.update({
                where: { id },
                data: {
                    isDeleted,
                    featuredPosition,
                },
                include: {
                    createdBy: {
                        select: {
                            id: true,
                            userName: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    business: {
                        select: {
                            id: true,
                            ownerName: true,
                        },
                    },
                },
            });

            return NextResponse.json(product);
        }

        return NextResponse.json(
            { error: 'Method not allowed' },
            { status: 405 }
        );
    } catch (error) {
        console.error('Error in product status route:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

export const PATCH = withAdminAuth(handler); 