import { NextResponse } from 'next/server';
import { withAdminAuth } from '@/app/middleware/adminAuth';
import { PrismaClient } from '@prisma/client';

export const dynamic = 'force-dynamic';

const prisma = new PrismaClient();

async function handler(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const { id } = params;

        if (request.method === 'GET') {
            const product = await prisma.product.findUnique({
                where: { id },
                include: {
                    createdBy: {
                        select: {
                            id: true,
                            userName: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    business: {
                        select: {
                            id: true,
                            ownerName: true,
                            isVerified: true,
                            subscriptionStatus: true,
                        },
                    },
                    reviews: {
                        include: {
                            user: {
                                select: {
                                    id: true,
                                    userName: true,
                                    firstName: true,
                                    lastName: true,
                                },
                            },
                        },
                        orderBy: {
                            createdDate: 'desc',
                        },
                    },
                    _count: {
                        select: {
                            reviews: true,
                        },
                    },
                },
            });

            if (!product) {
                return NextResponse.json(
                    { error: 'Product not found' },
                    { status: 404 }
                );
            }

            return NextResponse.json(product);
        }

        if (request.method === 'PATCH') {
            const data = await request.json();
            const {
                name,
                description,
                tags,
                openingHrs,
                closingHrs,
                telephone,
                website,
                isDeleted,
            } = data;

            const product = await prisma.product.update({
                where: { id },
                data: {
                    name,
                    description,
                    tags,
                    openingHrs,
                    closingHrs,
                    telephone,
                    website,
                    isDeleted,
                },
                include: {
                    createdBy: {
                        select: {
                            id: true,
                            userName: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    business: {
                        select: {
                            id: true,
                            ownerName: true,
                            isVerified: true,
                            subscriptionStatus: true,
                        },
                    },
                },
            });

            return NextResponse.json(product);
        }

        if (request.method === 'DELETE') {
            await prisma.product.update({
                where: { id },
                data: { isDeleted: true },
            });

            return NextResponse.json({ success: true });
        }

        return NextResponse.json(
            { error: 'Method not allowed' },
            { status: 405 }
        );
    } catch (error) {
        console.error('Error in product route:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

export const GET = withAdminAuth(handler);
export const PATCH = withAdminAuth(handler);
export const DELETE = withAdminAuth(handler); 