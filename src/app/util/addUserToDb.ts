// Importing necessary modules and packages
import { PrismaClient } from "@prisma/client";
import { clerk<PERSON>lient } from "@clerk/nextjs/server";
import { prisma } from "./prismaClient";
import { createUserForNotification } from "./NotificationFunctions";
import { UserDATA } from "./Interfaces";
import { isAdmin } from "../config/admin";

// Initializing Prisma client
// const prisma = new PrismaClient(); //toDO: import this from the prisma wrapper // did it

export const addUserToDb = async (clerkUserData: UserDATA) => {
  // If the user doesn't exist, create a new user entry in the database
  try {
    // Check if user should be admin based on email
    const userRole = isAdmin(clerkUserData.email) ? 'ADMIN' : 'USER';
    
    const user = await prisma.user.upsert({
      where: { email: clerkUserData.email },
      update: {},
      create: {
        id: clerkUserData.userId,
        userName: clerkUserData.userName,
        avatar: clerkUserData.avatar,
        email: clerkUserData.email,
        firstName: clerkUserData.firstName,
        lastName: clerkUserData.lastName || "",
        createdDate: new Date(),
        clerkUserId: clerkUserData.userId,
        isDeleted: false,
        role: userRole, // Set role based on admin email check
      },
    });

    // Create notification user with only the required fields
    createUserForNotification({
      id: user.id,
      userName: user.userName,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      createdDate: user.createdDate,
      clerkUserId: user.clerkUserId,
      isDeleted: user.isDeleted,
      avatar: user.avatar,
      createdById: user.id, // Set to self for new users
    });

    // Update the user metadata in the Clerk user object
    const clerkUser = await (await clerkClient()).users.updateUser(clerkUserData.userId, {
      publicMetadata: { userInDb: true, id: user.id }, // this is mongodb id
    });
    return clerkUser;

  } catch (error) {
    console.error("Error in addUserToDb:", error);
    return
  }

  return null;
};
