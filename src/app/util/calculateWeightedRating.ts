import { iReview } from "@/app/util/Interfaces";
import { MINIMUM_REVIEWS } from "@/app/config/rating";

export interface WeightedRatingResult {
  displayRating: number;
  roundedRating: number;
  roundedRatingOneDecimalPlace: string;
  numberOfReviews: number;
  hasMinimumReviews: boolean;
  confidence: 'low' | 'medium' | 'high';
  sortingScore: number; // Used for ranking/sorting
}

export function calculateWeightedRating(
  reviews: iReview[],
  options: {
    minimumReviews?: number;
    globalAverageRating?: number;
    confidenceWeight?: number;
  } = {}
): WeightedRatingResult {
  const {
    minimumReviews = MINIMUM_REVIEWS,
    globalAverageRating = 3.5,
    confidenceWeight = 10
  } = options;

  // Handle empty or invalid reviews
  if (!reviews || reviews.length === 0) {
    return {
      displayRating: 0,
      roundedRating: 0,
      roundedRatingOneDecimalPlace: "0.0",
      numberOfReviews: 0,
      hasMinimumReviews: false,
      confidence: 'low',
      sortingScore: 0
    };
  }

  const reviewCount = reviews.length;
  const actualAverage = reviews.reduce((acc, review) => acc + review.rating, 0) / reviewCount;

  // Calculate Bayesian Average: ((C × m) + (R × v)) / (C + v)
  // C = confidence weight, m = global average, R = actual average, v = number of reviews
  const bayesianAverage = (
    (confidenceWeight * globalAverageRating) + (actualAverage * reviewCount)
  ) / (confidenceWeight + reviewCount);

  // Determine confidence level
  let confidence: 'low' | 'medium' | 'high' = 'low';
  if (reviewCount >= minimumReviews * 3) confidence = 'high';
  else if (reviewCount >= minimumReviews) confidence = 'medium';

  const displayRating = reviewCount >= minimumReviews ? actualAverage : bayesianAverage;
  const sortingScore = bayesianAverage;

  return {
    displayRating,
    roundedRating: Math.round(displayRating),
    roundedRatingOneDecimalPlace: displayRating.toFixed(1),
    numberOfReviews: reviewCount,
    hasMinimumReviews: reviewCount >= minimumReviews,
    confidence,
    sortingScore
  };
}