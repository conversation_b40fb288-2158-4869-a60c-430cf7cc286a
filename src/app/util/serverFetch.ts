import { auth } from "@clerk/nextjs/server";
import { iComment, iProduct, iReview, iUser } from "@/app/util/Interfaces";

export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    status?: number;
    retryCount?: number;
}

interface FetchOptions extends RequestInit {
    retries?: number;
    timeout?: number;
    retryDelay?: number;
}

const DEFAULT_RETRIES = 3;
const DEFAULT_TIMEOUT = 10000; // 10 seconds
const DEFAULT_RETRY_DELAY = 1000; // 1 second

const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

/**
 * Delays execution for specified milliseconds
 */
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Checks if an error is retryable
 */
const isRetryableError = (error: any): boolean => {
    if (error instanceof TypeError && error.message === 'Failed to fetch') return true;
    if (error.status >= 500) return true;
    if (error.status === 429) return true; // Rate limit
    return false;
};

/**
 * Enhanced server-side fetch wrapper with Clerk authentication, retries, and timeout
 */
async function serverFetch<T>(
    endpoint: string,
    options: FetchOptions = {}
): Promise<ApiResponse<T>> {
    const {
        retries = DEFAULT_RETRIES,
        timeout = DEFAULT_TIMEOUT,
        retryDelay = DEFAULT_RETRY_DELAY,
        ...fetchOptions
    } = options;

    let retryCount = 0;

    while (retryCount <= retries) {
        try {
            console.log(`[serverFetch] Attempt ${retryCount + 1}/${retries + 1} for ${endpoint}`);

            // Get auth token from Clerk
            const { getToken } = await auth();
            const token = await getToken();

            if (!token) {
                console.warn("[serverFetch] No authentication token available");
                return {
                    success: false,
                    error: "Authentication required",
                    status: 401,
                    retryCount
                };
            }

            // Ensure endpoint starts with a slash and baseUrl doesn't end with one
            const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
            const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
            const url = `${cleanBaseUrl}${cleanEndpoint}`;

            console.log(`[serverFetch] Making request to: ${url}`);

            // Create abort controller for timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            const response = await fetch(url, {
                ...fetchOptions,
                signal: controller.signal,
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`,
                    ...fetchOptions.headers,
                },
                cache: "no-store",
            });

            clearTimeout(timeoutId);

            const data = await response.json();
            console.log(`[serverFetch] Response from ${endpoint}:`, data);

            if (!response.ok) {
                throw {
                    status: response.status,
                    message: data.error || `HTTP error! status: ${response.status}`
                };
            }

            if (data.success === false) {
                throw {
                    status: response.status,
                    message: data.error || 'API request failed'
                };
            }

            return {
                success: true,
                data: data.data || data,
                status: response.status,
                retryCount
            };

        } catch (error: any) {
            console.error(`[serverFetch] Error (${endpoint}):`, error);

            // Check if we should retry
            if (retryCount < retries && isRetryableError(error)) {
                retryCount++;
                console.log(`[serverFetch] Retrying in ${retryDelay}ms...`);
                await delay(retryDelay);
                continue;
            }

            return {
                success: false,
                error: error.message || "Unknown error occurred",
                status: error.status || 500,
                retryCount
            };
        }
    }

    return {
        success: false,
        error: "Max retries exceeded",
        status: 500,
        retryCount
    };
}

// Server-side API functions with improved typing and error handling
export const getServerUser = async (): Promise<ApiResponse<iUser>> => {
    return serverFetch<iUser>("/api/get/user", {
        method: "GET",
        retries: 2, // Custom retry count for this endpoint
    });
};

export const getServerReview = async (id: string): Promise<ApiResponse<iReview>> => {
    if (!id) {
        return {
            success: false,
            error: "Review ID is required",
            status: 400
        };
    }
    return serverFetch<iReview>("/api/get/review", {
        method: "POST",
        body: JSON.stringify({ id }),
    });
};

export const getServerProduct = async (id: string): Promise<ApiResponse<iProduct>> => {
    if (!id) {
        return {
            success: false,
            error: "Product ID is required",
            status: 400
        };
    }
    return serverFetch<iProduct>("/api/get/product", {
        method: "POST",
        body: JSON.stringify({ id }),
    });
};

export const createServerComment = async (comment: iComment): Promise<ApiResponse<iComment>> => {
    if (!comment.body?.trim()) {
        return {
            success: false,
            error: "Comment body cannot be empty",
            status: 400
        };
    }
    return serverFetch<iComment>("/api/create/comment", {
        method: "POST",
        body: JSON.stringify(comment),
    });
};

// Add more server-side API functions as needed 