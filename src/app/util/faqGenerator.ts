import { iProduct } from './Interfaces';
import dayjs from 'dayjs';

interface FAQ {
    question: string;
    answer: string;
}

// Cache for formatted business hours
const businessHoursCache = new Map<string, string>();

// Helper function to format business hours
function formatBusinessHours(product: iProduct): string {
    if (!product.openingHrs || !product.closingHrs || !product.openingDays?.length) {
        return 'Business hours are not available.';
    }

    // Create a cache key
    const cacheKey = `${product.openingHrs}-${product.closingHrs}-${product.openingDays.join(',')}`;

    // Check cache first
    if (businessHoursCache.has(cacheKey)) {
        return businessHoursCache.get(cacheKey)!;
    }

    const days = product.openingDays.join(', ');
    const formatted = `${product.openingHrs} - ${product.closingHrs} on ${days}`;

    // Store in cache
    businessHoursCache.set(cacheKey, formatted);

    return formatted;
}

// Helper function to format contact information
function formatContactInfo(product: iProduct): string {
    const contactMethods = [];

    if (product.telephone) {
        contactMethods.push(`Phone: ${product.telephone}`);
    }
    if (product.email) {
        contactMethods.push(`Email: ${product.email}`);
    }
    if (product.website?.length) {
        contactMethods.push(`Website: ${product.website[0]}`);
    }

    return contactMethods.length > 0
        ? contactMethods.join(', ')
        : 'Contact information is not available.';
}

// Cache for product age calculations
const productAgeCache = new Map<string, string>();

// Helper function to calculate product age
function calculateProductAge(createdDate: Date): string {
    const cacheKey = createdDate.toISOString();

    // Check cache first
    if (productAgeCache.has(cacheKey)) {
        return productAgeCache.get(cacheKey)!;
    }

    const days = dayjs().diff(dayjs(createdDate), 'day');
    let result: string;

    if (days < 30) {
        result = `${days} days`;
    } else {
        const months = Math.floor(days / 30);
        if (months < 12) {
            result = `${months} months`;
        } else {
            const years = Math.floor(months / 12);
            result = `${years} years`;
        }
    }

    // Store in cache
    productAgeCache.set(cacheKey, result);

    return result;
}

// Cache for FAQ generation
const faqCache = new Map<string, FAQ[]>();

// Main function to generate product-specific FAQs
export function generateProductFAQs(product: iProduct): FAQ[] {
    // Create a cache key based on product data
    const cacheKey = JSON.stringify({
        id: product.id,
        address: product.address,
        openingHrs: product.openingHrs,
        closingHrs: product.closingHrs,
        openingDays: product.openingDays,
        telephone: product.telephone,
        email: product.email,
        website: product.website,
        reviewCount: product._count?.reviews,
        rating: product.rating,
        hasOwner: product.hasOwner,
        isVerified: product.business?.isVerified,
        createdDate: product.createdDate,
        ratingDistribution: {
            rating5Stars: product.rating5Stars,
            rating4Stars: product.rating4Stars,
            rating3Stars: product.rating3Stars,
            rating2Stars: product.rating2Stars,
            rating1Star: product.rating1Star
        }
    });

    // Check cache first
    if (faqCache.has(cacheKey)) {
        return faqCache.get(cacheKey)!;
    }

    const faqs: FAQ[] = [];

    // Location FAQ
    if (product.address) {
        faqs.push({
            question: `Where is ${product.name} located?`,
            answer: product.address
        });
    }

    // Business Hours FAQ
    if (product.openingHrs && product.closingHrs && product.openingDays?.length) {
        faqs.push({
            question: `What are ${product.name}'s business hours?`,
            answer: formatBusinessHours(product)
        });
    }

    // Contact Information FAQ
    if (product.telephone || product.email || product.website?.length) {
        faqs.push({
            question: `How can I contact ${product.name}?`,
            answer: formatContactInfo(product)
        });
    }

    // Review Statistics FAQ
    if (product._count?.reviews) {
        faqs.push({
            question: `How many reviews does ${product.name} have?`,
            answer: `${product._count.reviews} reviews with an average rating of ${product.rating.toFixed(1)} stars.`
        });
    }

    // Business Verification FAQ
    if (product.hasOwner !== null) {
        faqs.push({
            question: `Is ${product.name} a verified business?`,
            answer: product.hasOwner && product.business?.isVerified
                ? 'Yes, this is a verified business.'
                : 'This business is not verified.'
        });
    }

    // Product Age FAQ
    if (product.createdDate) {
        faqs.push({
            question: `How long has ${product.name} been listed?`,
            answer: `This product has been listed for ${calculateProductAge(product.createdDate)}.`
        });
    }

    // Rating Distribution FAQ
    if (product.rating5Stars || product.rating4Stars || product.rating3Stars ||
        product.rating2Stars || product.rating1Star) {
        const totalReviews = (product.rating5Stars || 0) + (product.rating4Stars || 0) +
            (product.rating3Stars || 0) + (product.rating2Stars || 0) +
            (product.rating1Star || 0);

        if (totalReviews > 0) {
            faqs.push({
                question: `What is the rating distribution for ${product.name}?`,
                answer: `Out of ${totalReviews} reviews: ${product.rating5Stars || 0} five-star, ${product.rating4Stars || 0} four-star, ${product.rating3Stars || 0} three-star, ${product.rating2Stars || 0} two-star, and ${product.rating1Star || 0} one-star reviews.`
            });
        }
    }

    // Store in cache
    faqCache.set(cacheKey, faqs);

    return faqs;
}

// Note: Cache clearing is now handled by TTL expiration
// Removed setInterval to prevent setTimeout violations in browser context