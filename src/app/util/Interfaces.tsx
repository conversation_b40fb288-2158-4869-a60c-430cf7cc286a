import { WeightedRatingResult } from './calculateWeightedRating';

export interface ReviewSummaryData {
  percentage5Stars: number;
  percentage4Stars: number;
  percentage3Stars: number;
  percentage2Stars: number;
  percentage1Star: number;
  totalCount: number;
  counts: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

export interface iNotification {
  id: string;
  receiver_id: string;
  business_id: string;
  review_title: string;
  created_at?: Date;
  from_name: string;
  from_id: string;
  read: boolean;
  product_name: string;
  product_id: string;
  comment_id: string;
  review_id: string;
}

export interface iProductOwnerNotification {
  id: string;
  owner_id: string;
  business_id: string;
  review_title: string;
  created_at?: Date;
  from_name: string;
  from_id: string;
  read: boolean;
  product_id: string;
  product_name: string;
  review_id: string;
  comment_id: string | null;
  notification_type: string;
}

export interface iUserNotification {
  id: string;
  user_id?: string;
  content: string;
  read: boolean;
  notification_type: string;
  comment_id: string;
  review_id: string;
  from_name: string;
  from_id: string;
  created_at?: Date;
  parent_id: string;
  product_id?: string;
}

export interface FetchedNotificationsData {
  userNotifications: iUserNotification[];
  ownerNotifications: iProductOwnerNotification[];
}

export interface ReviewLike {
  reviewId: string;
  userId: string;

  review: iReview;
  user: iUser;
}

export interface iCalculatedRating {
  roundedRating: number;
  roundedRatingOneDecimalPlace: string;
  numberOfReviews: number;
  // New fields for weighted rating
  hasMinimumReviews?: boolean;
  confidence?: 'low' | 'medium' | 'high';
  sortingScore?: number;
  displayRating?: number;
}

export interface iPromotion {
  id?: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  discountPercentage?: number;
  discountAmount?: number;
  promotionCode?: string;
  isActive: boolean;
  productId: string;
  product?: iProduct;
  businessId: string;
  business?: iBusiness;
  createdAt: Date;
  updatedAt?: Date;
  image?: string;
  viewCount?: number;
  clickCount?: number;
  conversionCount?: number;
}

export interface TopReviewer {
  userId: string;
  reviewCount: number;
  user: {
    id: string;
    userName: string;
    firstName: string;
    lastName: string;
    avatar: string | null;
  };
}

export interface TopReviewersResponse {
  success: boolean;
  status: number;
  data: TopReviewer[];
}

export interface iProduct {
  id?: string;
  address?: string | null;
  latitude?: number | null;
  longitude?: number | null;
  createdDate: Date;
  description: string;
  display_image: string;
  images: string[];
  videos: string[];
  links: string[];
  name: string;
  tags: string[];
  openingHrs?: string | null;
  closingHrs?: string | null;
  openingDays?: string[]; // Array of days the business is open
  telephone?: string | null;
  website: string[];
  rating: number;
  hasOwner: boolean | null;
  ownerId: string | null;
  createdById: string;
  isDeleted: boolean;
  email?: string | null;
  businessId?: string | null;
  updatedAt?: Date | null;
  viewCount?: number;
  rating5Stars?: number;
  rating4Stars?: number;
  rating3Stars?: number;
  rating2Stars?: number;
  rating1Star?: number;
  featuredPosition?: number | null;
  business?: iBusiness | null;
  createdBy?: iUser | null;
  reviews?: iReview[];
  promotions?: iPromotion[];
  relatedProducts?: iProduct[];
  _count?: {
    reviews?: number;
    promotions?: number;
  };
  analytics?: iProductAnalytics;
  viewEvents?: iProductViewEvent[];
  userInteractions?: iUserProductInteraction[];
  claims?: iProductClaim[];
  weightedRating?: WeightedRatingResult;
}

export interface iVoteCount {
  id: string;
  reviewId: string;
  review: iReview;
  helpfulVotes: number;
  unhelpfulVotes: number;
}

// Minimal user info for review context
export interface iReviewUser {
  id: string;
  firstName: string;
  lastName: string;
  avatar: string | null;
  userName: string;
}

export interface iReview {
  id?: string;
  body: string;
  createdDate?: Date;
  helpfulVotes?: number;
  unhelpfulVotes?: number;
  rating: number;
  title: string;
  productId: string;
  userId: string;
  isVerified?: boolean | null;
  verifiedBy?: string | null;
  createdBy?: string | null;
  isPublic: boolean;
  images: string[];
  videos: string[];
  links: string[];
  isDeleted?: boolean | null;
  verifiedAt?: Date | null;
  moderationHistory?: iModerationEvent[];
  product?: iProduct | null;
  user?: iReviewUser | null;
  comments?: iComment[];
  voteCount?: iVoteCount | null;
  likedBy?: iUser[];
  reports?: iReviewReport[];
}

export interface iComment {
  id?: string;
  body: string;
  createdDate: Date;
  review?: iReview;
  user?: iUser;
  reviewId: string;
  userId: string;
  isDeleted: boolean | false;
  parentId?: string | null;
  replies?: iComment[];
  parent?: iComment;
  parentUserId?: string;
  upvotes: number;
  downvotes: number;
  votes?: iCommentVote[];
}

export interface iCommentVote {
  id: string;
  commentId: string;
  userId: string;
  clerkUserId: string;
  voteType: "UP" | "DOWN";
  createdAt: Date;
  user?: iUser;
}

export interface iUser {
  id: string;
  bio?: string | null;
  userName: string;
  avatar: string | null;
  createdDate: Date;
  email: string;
  firstName: string;
  lastName: string;
  clerkUserId: string;
  isDeleted: boolean | null;
  role?: "USER" | "ADMIN";
  status?: "ACTIVE" | "SUSPENDED" | "BANNED";
  lastLoginAt?: Date;
  loginCount?: number;
  suspendedUntil?: Date;
  suspendedReason?: string;
  reviews?: iReview[];
  product?: iProduct[];
  createdBy?: iUser | null;
  createdById?: string;
  comments?: iComment[];
  likedReviews?: iReview[];
  businesses?: iBusiness[];
  _count?: {
    reviews?: number;
    comments?: number;
    product?: number;
  };
  adminActions?: iAdminAction[];
  moderationEvents?: iModerationEvent[];
  commentVotes?: iCommentVote[];
  productClaims?: iProductClaim[];
  reviewedClaims?: iProductClaim[];
  // Notification settings
  emailNotifications?: boolean;
  pushNotifications?: boolean;
  // Privacy settings
  profileVisibility?: "public" | "private" | "friends";
  showEmail?: boolean;
}

export interface iBusiness {
  id: string;
  owner: {
    id: string;
    firstName: string;
    lastName: string;
    avatar: string | null;
  };
  ownerId: string;
  businessDescription?: string | null;
  subscriptionStatus: string;
  subscriptionExpiry: Date | null;
  products?: iProduct[];
  createdDate: Date | null;
  isVerified: boolean | null;
  ownerName?: string | null;
}

export interface iService {
  id?: string;
  name: string;
  description: string;
  images?: string[];
  createdDate?: Date;
  address?: string;
}

export interface iImage {
  id?: string;
  url: string;
}

export interface SentDataReviewAndProduct {
  userId: string; // identifier for the user who wrote the review
  rating: number; // a number between 1 and 5 indicating the rating for the product
  title: string; // the title of the review
  body: string; // the main text of the review
  helpfulVotes?: number; // the number of helpful votes the review has received
  unhelpfulVotes?: number; // the number of unhelpful votes the review has received
  comments: string[]; // an array of comments on the review
  createdDate?: Date;
  images?: string[]; // an array of images on the review
  confirmed?: boolean; // a boolean indicating whether the review has been confirmed
  deleted?: boolean; // a boolean indicating whether the review has been deleted
  deletedDate?: Date; // the date the review was deleted
  deletedBy?: string; // the user who deleted the review
  deletedReason?: string; // the reason the review was deleted
  productId?: string;
  links?: string[];
  videos?: string[];
  publicMetadata?: { userInDb: boolean; id: string };
  product: {
    display_image: string;
    productSelected: boolean;
    productId?: string;
    name: string;
    description: string;
    images?: string[];
    createdDate?: Date;
    address?: string;
    tags?: string[];
    openingHrs?: string;
    closingHrs?: string;
    telephone?: string;
    website?: string[];
    hasOwner?: boolean;
    owner?: string;
    ownerId?: string;
    isService?: boolean;
    isProduct?: boolean;
    videos?: string[];
    links?: string[];
  };
}

export interface ReviewUserAndproduct {
  id: string;
  body: string;
  comments: Comment[];
  createdDate: string;
  helpfulVotes: number;
  rating: number;
  title: string;
  unhelpfulVotes: number;
  productId: string;
  userId: string;
  isVerified: boolean | null;
  verifiedBy: string | null;
  isPublic: boolean;
  images: string[];
  videos: string[];
  links: string[];
  createdBy: string;
  isDeleted: boolean;
  user: {
    id: string;
    userName: string;
    avatar: string;
    createdDate: string;
    email: string;
    firstName: string;
    lastName: string;
    clerkUserId: string;
    isDeleted: boolean;
  };
  product: {
    id: string;
    address: string | null;
    createdDate: string;
    description: string;
    images: string[];
    videos: string[];
    links: string[];
    name: string;
    tags: string[];
    openingHrs: string | null;
    closingHrs: string | null;
    telephone: string | null;
    website: string[];
    rating: number;
    hasOwner: boolean | null;
    ownerId: string | null;
    createdById: string;
    isDeleted: boolean;
  };
}

// Interface representing user data
export interface UserDATA {
  avatar?: string | null;
  azp: string;
  email: string;
  exp: number;
  firstName: string;
  lastName: string;
  fullName: string;
  iat: number;
  iss: string;
  jti: string;
  nbf: number;
  sub: string;
  userId: string;
  userName: string;
  metadata?: {
    id: string;
    userInDb: boolean;
  };
}

export interface UserCreatedEvent {
  object: string;
  type: string;

  data: {
    birthday: string;
    created_at: number;
    email_addresses: Array<{
      email_address: string;
      id: string;
      linked_to: Array<string>;
      object: string;
      verification: {
        status: string;
        strategy: string;
      };
    }>;
    external_accounts: Array<string>;
    external_id: string;
    first_name: string;
    gender: string;
    id: string;
    image_url: string;
    last_name: string;
    last_sign_in_at: number;
    object: string;
    password_enabled: boolean;
    phone_numbers: Array<string>;
    primary_email_address_id: string;
    primary_phone_number_id: string | null;
    primary_web3_wallet_id: string | null;
    private_metadata: {};
    profile_image_url: string;
    public_metadata: {};
    two_factor_enabled: boolean;
    unsafe_metadata: {};
    updated_at: number;
    username: string | null;
    web3_wallets: Array<string>;
  };
}

export interface iTag {
  tags: string[];
}

export interface iAdminAction {
  id?: string;
  adminId: string;
  actionType: string;
  targetId: string;
  targetType: string;
  description: string;
  createdAt?: Date;
  admin?: iUser;
}

export interface iModerationEvent {
  id?: string;
  reviewId: string;
  adminId: string;
  action: "APPROVED" | "REJECTED" | "FLAGGED";
  reason?: string;
  createdAt?: Date;
  review?: iReview;
  admin?: iUser;
}

export interface OGImage {
  url: string;
  width?: number;
  height?: number;
  alt?: string;
  type?: string;
  secure_url?: string;
}

// Analytics Interfaces
export interface iProductViewEvent {
  id?: string;
  productId: string;
  userId?: string; // Optional, for authenticated users
  sessionId: string; // For anonymous tracking
  timestamp: Date;
  duration?: number; // Time spent viewing in seconds
  source?: string; // Where the user came from
  deviceType?: string; // Mobile/Desktop/Tablet
  isNewUser: boolean;
  isThrottled: boolean; // Whether this view was counted in stats
}

export interface iProductAnalytics {
  id?: string;
  productId: string;
  totalViews: number;
  uniqueVisitors: number;
  averageViewDuration: number;
  peakHours: Record<number, number>; // Hour -> View count
  weekdayStats: Record<string, number>; // Day -> View count
  lastUpdated: Date;
}

export interface iUserProductInteraction {
  id?: string;
  userId: string;
  productId: string;
  lastViewed: Date;
  viewCount: number;
  hasReviewed: boolean;
  hasLiked: boolean;
  averageTimeSpent: number;
}

// Subscription Interfaces
export interface iSubscriptionTier {
  id: string;
  name: string;
  description: string;
  price: number;
  billingCycle: "monthly" | "yearly" | "quarterly";
  features: string[];
  maxProducts: number;
  maxPromotions: number;
  maxReviews: number;
  analyticsAccess: boolean;
  apiAccess: boolean;
  priority: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface iSubscriptionUsage {
  id?: string;
  businessId: string;
  productCount: number;
  promotionCount: number;
  reviewCount: number;
  apiCalls: number;
  monthlyViews: number;
  lastUpdated: Date;
  business?: iBusiness;
}

export interface iSubscriptionBilling {
  id?: string;
  businessId: string;
  tierId: string;
  customerId?: string;
  paymentMethod?: string;
  lastFour?: string;
  nextBillingDate: Date;
  amount: number;
  status: "active" | "past_due" | "canceled" | "inactive";
  createdAt: Date;
  updatedAt?: Date;
  tier?: iSubscriptionTier;
  business?: iBusiness;
}

export interface iSubscriptionTransaction {
  id?: string;
  businessId: string;
  subscriptionId: string;
  amount: number;
  currency: string;
  status: "succeeded" | "failed" | "pending";
  paymentMethod: string;
  receiptUrl?: string;
  description: string;
  createdAt: Date;
  billing?: iSubscriptionBilling;
  business?: iBusiness;
}

// Analytics Dashboard Interfaces
export interface iBusinessAnalytics {
  id?: string;
  businessId: string;
  totalViews: number;
  uniqueVisitors: number;
  totalReviews: number;
  averageRating: number;
  viewsPerDay: Record<string, number>; // Date string -> count
  trafficSources: Record<string, number>; // Source -> count
  deviceTypes: Record<string, number>; // Device type -> count
  conversionRate: number;
  topProducts: Array<{
    id: string;
    name: string;
    views: number;
    conversion: number;
  }>;
  lastUpdated: Date;
  business?: iBusiness;
}

export interface iProductPerformance {
  id?: string;
  productId: string;
  businessId: string;
  viewsTrend: Array<{
    date: string;
    views: number;
  }>;
  reviewsTrend: Array<{
    date: string;
    count: number;
    rating: number;
  }>;
  conversionTrend: Array<{
    date: string;
    rate: number;
  }>;
  engagementMetrics: {
    averageTimeOnPage: number;
    bounceRate: number;
    clickThroughRate: number;
  };
  competitorComparison?: Array<{
    name: string;
    views: number;
    rating: number;
  }>;
  lastUpdated: Date;
  product?: iProduct;
}

export interface iTrafficSource {
  id?: string;
  businessId: string;
  source: string;
  medium: string;
  campaign?: string;
  visitors: number;
  sessions: number;
  bounceRate: number;
  conversionRate: number;
  lastUpdated: Date;
  business?: iBusiness;
}

export interface iAnalyticsPeriod {
  startDate: Date;
  endDate: Date;
  comparison?: "previous_period" | "previous_year" | "none";
}

export interface iProductClaim {
  id: string;
  productId: string;
  userId: string;
  status: string;
  contactInfo: string;
  additionalInfo: string;
  images: string[];
  createdAt: Date;
  updatedAt: Date;
  reviewedAt: Date | null;
  reviewedBy: string | null;
  rejectionReason: string | null;
  product: {
    id: string;
    name: string;
    description: string;
    display_image: string;
    address?: string | null;
  } | null;
  user?: iUser;
  reviewer?: iUser;
}

export interface iBugReport {
  id: string;
  title: string;
  description: string;
  browser?: string;
  device?: string;
  mobile_os?: string;
  status: "OPEN" | "IN_PROGRESS" | "RESOLVED" | "CLOSED" | "WONT_FIX";
  resolved_at?: Date;
  resolved_by?: string;
  resolution_notes?: string;
  created_at: Date;
  updated_at: Date;
  reporter?: iUser;
  reporterId?: string;
  resolver?: iUser;
  adminActions?: iAdminAction[];
}

export interface iReviewReport {
  id: string;
  reviewId: string;
  userId: string;
  reason: string;
  status: "PENDING" | "REVIEWED" | "RESOLVED";
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  resolvedBy?: string;
  notes?: string;
  review?: iReview;
  user?: iUser;
  resolver?: iUser;
}

export interface ApiResponse<T> {
  success: boolean;
  status: number;
  data: T;
  error?: string;
}
