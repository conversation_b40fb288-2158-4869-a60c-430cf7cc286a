import { prisma } from "@/app/util/prismaClient";
import { auth } from "@clerk/nextjs/server";

/**
 * Role-based admin authentication system
 * Checks if a user has admin privileges based on their database role
 */

/**
 * Checks if a user is authorized as an admin using role verification
 * @param userId User's Clerk ID
 * @returns Promise<boolean> indicating if the user is authorized as an admin
 */
export async function isUserAuthorizedAdmin(userId: string): Promise<boolean> {
    try {
        // Check role-based auth
        const user = await prisma.user.findFirst({
            where: { clerkUserId: userId },
            select: {
                role: true,
                status: true
            }
        });

        const isRoleAdmin = user?.role === 'ADMIN';
        const isActive = user?.status === 'ACTIVE';

        return isRoleAdmin && isActive;
    } catch (error) {
        console.error('Error in admin check:', error);
        return false;
    }
}

/**
 * Higher-order function that wraps route handlers with admin authentication
 * This is used to protect specific API routes or page handlers
 */
export function withAdminAuth<T = { [key: string]: string | string[] }>(
    handler: (request: Request, { params }: { params: T }) => Promise<Response>
) {
    return async (request: Request, { params }: { params: T }) => {
        try {
            const { userId } = await auth();

            if (!userId) {
                return new Response("Unauthorized", { status: 401 });
            }

            const isAuthorized = await isUserAuthorizedAdmin(userId);
            if (!isAuthorized) {
                return new Response("Forbidden: Admin access required", { status: 403 });
            }

            return handler(request, { params });
        } catch (error) {
            console.error("Admin auth wrapper error:", error);
            return new Response("Internal Server Error", { status: 500 });
        }
    };
}

/**
 * Middleware function for admin authentication
 * This can be used in Next.js middleware or API routes
 */
export async function adminAuthMiddleware(request: Request) {
    try {
        const { userId } = await auth();

        if (!userId) {
            return new Response("Unauthorized", { status: 401 });
        }

        const isAuthorized = await isUserAuthorizedAdmin(userId);
        if (!isAuthorized) {
            return new Response("Forbidden: Admin access required", { status: 403 });
        }

        return null; // Continue to the next middleware/handler
    } catch (error) {
        console.error("Admin middleware error:", error);
        return new Response("Internal Server Error", { status: 500 });
    }
} 