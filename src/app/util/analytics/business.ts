import { prisma } from '../prismaClient';
import { redisService } from '../../lib/redis';
import { iAnalyticsPeriod, iBusinessAnalytics, iTrafficSource } from './types';
import { formatViewsPerDay, formatTrafficSources, formatDeviceTypes, calculateUniqueVisitorConversionRate, formatTopProducts } from './formatters';



export async function getBusinessAnalyticsFromDB(
  businessId: string,
  period: iAnalyticsPeriod
): Promise<iBusinessAnalytics> {
  // Normalize dates to remove time component for consistent cache keys
  const startDateKey = period.startDate.toISOString().split('T')[0];
  const endDateKey = period.endDate.toISOString().split('T')[0];
  const cacheKey = `business_analytics:${businessId}:${startDateKey}:${endDateKey}`;

  // Check cache first with Redis singleton
  const cached = await redisService.getFromCache<iBusinessAnalytics>(cacheKey);
  if (cached) {
    return cached;
  }

  const startDate = period.startDate.toISOString();
  const endDate = period.endDate.toISOString();

  // Get all products for this business
  const products = await prisma.product.findMany({
    where: { businessId, isDeleted: false },
    select: { id: true, name: true }
  });

  const productIds = products.map(p => p.id);

  // Total views from ProductViewEvent
  const totalViewsResult = await prisma.productViewEvent.aggregate({
    where: {
      productId: { in: productIds },
      timestamp: { gte: new Date(startDate), lte: new Date(endDate) }
    },
    _count: { id: true }
  });

  // Unique visitors - count distinct sessionIds (more reliable than userId which can be null)
  const uniqueVisitorsResult = await prisma.productViewEvent.groupBy({
    by: ['sessionId'],
    where: {
      productId: { in: productIds },
      timestamp: { gte: new Date(startDate), lte: new Date(endDate) }
    }
  });

  // Views per day aggregation
  const viewsPerDayRaw = await prisma.$queryRaw`
    SELECT DATE("timestamp") as date, COUNT(*) as views
    FROM "ProductViewEvent"
    WHERE "productId" = ANY(${productIds}::text[])
    AND "timestamp" BETWEEN ${startDate}::timestamp AND ${endDate}::timestamp
    GROUP BY DATE("timestamp")
    ORDER BY date
  `;

  // Device type distribution
  const deviceTypesRaw = await prisma.productViewEvent.groupBy({
    by: ['deviceType'],
    where: {
      productId: { in: productIds },
      timestamp: { gte: new Date(startDate), lte: new Date(endDate) }
    },
    _count: { deviceType: true }
  });

  // Traffic sources
  const trafficSourcesRaw = await prisma.productViewEvent.groupBy({
    by: ['source'],
    where: {
      productId: { in: productIds },
      timestamp: { gte: new Date(startDate), lte: new Date(endDate) }
    },
    _count: { source: true }
  });

  // Reviews data
  const reviewsData = await prisma.review.aggregate({
    where: {
      productId: { in: productIds },
      createdDate: { gte: new Date(startDate), lte: new Date(endDate) },
      isDeleted: false,
      isPublic: true
    },
    _count: { id: true },
    _avg: { rating: true }
  });

  // Top products by views
  const topProductsRaw = await prisma.$queryRaw`
    SELECT p.id, p.name, COUNT(pve.id) as views,
           CASE 
             WHEN COUNT(pve.id) > 0 THEN (COUNT(r.id)::float / COUNT(pve.id)::float * 100)
             ELSE 0
           END as conversion
    FROM "Product" p
    LEFT JOIN "ProductViewEvent" pve ON p.id = pve."productId"
      AND pve."timestamp" BETWEEN ${startDate}::timestamp AND ${endDate}::timestamp
    LEFT JOIN "Review" r ON p.id = r."productId"
      AND r."createdDate" BETWEEN ${startDate}::timestamp AND ${endDate}::timestamp
      AND r."isDeleted" = false AND r."isPublic" = true
    WHERE p."businessId" = ${businessId}
    AND p."isDeleted" = false
    GROUP BY p.id, p.name
    HAVING COUNT(pve.id) > 0
    ORDER BY views DESC
    LIMIT 5
  `;

  const analyticsData = {
    id: `ba-${businessId}`,
    businessId,
    totalViews: totalViewsResult._count.id,
    uniqueVisitors: uniqueVisitorsResult.length,
    totalReviews: reviewsData._count.id,
    averageRating: reviewsData._avg.rating || 0,
    viewsPerDay: formatViewsPerDay(viewsPerDayRaw as any),
    trafficSources: formatTrafficSources(trafficSourcesRaw as any),
    deviceTypes: formatDeviceTypes(deviceTypesRaw as any),
    conversionRate: calculateUniqueVisitorConversionRate(uniqueVisitorsResult.length, reviewsData._count.id),
    topProducts: formatTopProducts(topProductsRaw as any),
    lastUpdated: new Date()
  };

  // Cache the result with safe Redis operations
  await redisService.setInCache(cacheKey, analyticsData, 1200); // 20 minute cache
  console.log(`Redis CACHE SET for key: ${cacheKey}`);

  return analyticsData;
}

export async function getTrafficSourcesFromDB(
  businessId: string,
  period: iAnalyticsPeriod
): Promise<iTrafficSource[]> {
  // Normalize dates to remove time component for consistent cache keys
  const startDateKey = period.startDate.toISOString().split('T')[0];
  const endDateKey = period.endDate.toISOString().split('T')[0];
  const cacheKey = `traffic_sources:${businessId}:${startDateKey}:${endDateKey}`;

  // Check cache first with Redis singleton
  const cached = await redisService.getFromCache<iTrafficSource[]>(cacheKey);
  if (cached) {
    return cached;
  }

  const startDate = period.startDate.toISOString();
  const endDate = period.endDate.toISOString();

  const products = await prisma.product.findMany({
    where: { businessId, isDeleted: false },
    select: { id: true }
  });

  const productIds = products.map(p => p.id);

  const trafficData = await prisma.$queryRaw`
    SELECT
      source,
      'organic' as medium,
      COUNT(DISTINCT "sessionId") as visitors,
      COUNT(*) as sessions,
      AVG(CASE WHEN duration > 0 THEN duration ELSE 30 END) as avg_duration,
      CASE 
        WHEN COUNT(DISTINCT "sessionId") > 0 THEN
          COUNT(DISTINCT CASE WHEN duration < 10 THEN "sessionId" END)::float /
          COUNT(DISTINCT "sessionId")::float * 100
        ELSE 0
      END as bounce_rate
    FROM "ProductViewEvent"
    WHERE "productId" = ANY(${productIds}::text[])
    AND "timestamp" BETWEEN ${startDate}::timestamp AND ${endDate}::timestamp
    AND source IS NOT NULL
    GROUP BY source
    ORDER BY visitors DESC
  `;

  const trafficSourcesData = (trafficData as Array<any>).map((row: any, index: number): iTrafficSource => ({
    id: `ts-${index}`,
    businessId: row.businessId || '',
    source: row.source || 'Direct',
    medium: row.medium || '',
    visitors: parseInt(row.visitors) || 0,
    sessions: parseInt(row.sessions) || 0,
    bounceRate: parseFloat(row.bounce_rate) || 0,
    conversionRate: 0, // TODO: Implement async conversion rate calculation
    lastUpdated: new Date(row.lastUpdated || new Date())
  }));

  // Cache the result with safe Redis operations
  await redisService.setInCache(cacheKey, trafficSourcesData, 1200); // 20 minute cache
  console.log(`Redis CACHE SET for key: ${cacheKey}`);

  return trafficSourcesData;
}