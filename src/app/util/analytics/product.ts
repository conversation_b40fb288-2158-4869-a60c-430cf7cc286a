import { prisma } from '../prismaClient';
import { StatusCodes } from 'http-status-codes';
import { register, Counter, Histogram } from 'prom-client';
import { redisService } from '../../lib/redis';
import { iProductAnalytics, iTrafficSource } from './types';
import { CacheKeys, trackCacheHit, trackCacheMiss } from './cache';

// Prometheus metrics setup
const httpRequestCounter = new Counter({
  name: 'database_analytics_requests_total',
  help: 'Total number of requests to database analytics',
  labelNames: ['endpoint', 'status']
});
const httpRequestDuration = new Histogram({
  name: 'database_analytics_request_duration_seconds',
  help: 'Duration of database analytics requests in seconds',
  labelNames: ['endpoint']
});

class DatabaseAnalytics {
  constructor(
    private prisma: import('@prisma/client').PrismaClient,
    private cacheTtl: number = 300,
    private redis = redisService // Inject Redis service
  ) { }

  async getProductAnalytics(productId: string): Promise<iProductAnalytics> {
    const end = httpRequestDuration.startTimer({ endpoint: 'getProductAnalytics' });

    try {
      // Check cache first
      const cached = await this.getFromCache<iProductAnalytics>(`product:${productId}`);
      if (cached) {
        httpRequestCounter.inc({ endpoint: 'getProductAnalytics', status: StatusCodes.OK });
        end();
        return cached;
      }

      // Get base analytics
      const [views, uniqueVisitors, durationStats] = await Promise.all([
        this.prisma.productViewEvent.count({ where: { productId } }),
        this.prisma.productViewEvent.groupBy({
          by: ['userId'],
          where: { productId, userId: { not: null } },
          _count: { userId: true }
        }),
        this.prisma.productViewEvent.aggregate({
          where: { productId, duration: { not: null } },
          _avg: { duration: true }
        })
      ]);

      // Calculate peak hours and weekday stats
      const viewEvents = await this.prisma.productViewEvent.findMany({
        where: { productId },
        select: { timestamp: true, duration: true }
      });

      const peakHours = this.calculatePeakHours(viewEvents);
      const weekdayStats = this.calculateWeekdayStats(viewEvents);

      const analyticsData: iProductAnalytics = {
        id: `pa-${productId}`,
        productId,
        totalViews: views,
        uniqueVisitors: uniqueVisitors.length,
        averageViewDuration: durationStats._avg.duration || 0,
        peakHours,
        weekdayStats,
        lastUpdated: new Date()
      };

      // Update cache
      await this.setInCache(`product:${productId}`, analyticsData, this.cacheTtl);

      httpRequestCounter.inc({ endpoint: 'getProductAnalytics', status: StatusCodes.OK });
      end();
      return analyticsData;
    } catch (error) {
      httpRequestCounter.inc({ endpoint: 'getProductAnalytics', status: StatusCodes.INTERNAL_SERVER_ERROR });
      end();
      throw this.handleError(error, StatusCodes.INTERNAL_SERVER_ERROR);
    }
  }

  async getTrafficSourceMetrics(productId: string): Promise<iTrafficSource[]> {
    const end = httpRequestDuration.startTimer({ endpoint: 'getTrafficSourceMetrics' });

    try {
      // Check cache first
      const cached = await this.getFromCache<iTrafficSource[]>(`traffic:${productId}`);
      if (cached) {
        httpRequestCounter.inc({ endpoint: 'getTrafficSourceMetrics', status: StatusCodes.OK });
        end();
        return cached;
      }

      // Get traffic source data
      const trafficData = await this.prisma.productViewEvent.groupBy({
        by: ['source', 'deviceType'],
        where: { productId },
        _count: {
          id: true,
          userId: true
        },
        _avg: { duration: true }
      });

      // Process device distribution
      const deviceDistribution = trafficData.reduce((acc: Record<string, number>, curr: any) => {
        const deviceType = curr.deviceType || 'unknown';
        if (!acc[deviceType]) {
          acc[deviceType] = 0;
        }
        acc[deviceType] += curr._count.id;
        return acc;
      }, {} as Record<string, number>);

      // Format results
      const result = trafficData.map((item: any) => ({
        source: item.source || 'direct',
        viewCount: item._count.id,
        uniqueUsers: item._count.userId,
        averageDuration: item._avg.duration || 0,
        deviceDistribution: { ...deviceDistribution }
      }));

      // Update cache
      await this.setInCache(`traffic:${productId}`, result, this.cacheTtl);

      httpRequestCounter.inc({ endpoint: 'getTrafficSourceMetrics', status: StatusCodes.OK });
      end();
      return result.map((item: any) => ({
        id: item.id || `traffic_${Math.random()}`,
        businessId: item.businessId || 'default',
        source: item.source,
        medium: item.medium || 'organic',
        visitors: item.uniqueUsers || 0,
        sessions: item.viewCount || 0,
        bounceRate: item.bounceRate || 0,
        conversionRate: item.conversionRate || 0,
        lastUpdated: item.lastUpdated || new Date()
      }));
    } catch (error) {
      httpRequestCounter.inc({ endpoint: 'getTrafficSourceMetrics', status: StatusCodes.INTERNAL_SERVER_ERROR });
      end();
      throw this.handleError(error, StatusCodes.INTERNAL_SERVER_ERROR);
    }
  }

  private async getFromCache<T>(key: string): Promise<T | null> {
    return await this.redis.getFromCache<T>(key);
  }

  private async setInCache(key: string, value: any, ttl: number): Promise<void> {
    await this.redis.setInCache(key, value, ttl);
  }

  private calculatePeakHours(events: { timestamp: Date; duration: number | null }[]): Record<number, number> {
    const hourlyStats: Record<number, number> = {};

    for (const event of events) {
      const hour = event.timestamp.getHours();
      hourlyStats[hour] = (hourlyStats[hour] || 0) + 1;
    }

    return hourlyStats;
  }

  private calculateWeekdayStats(events: { timestamp: Date; duration: number | null }[]): Record<string, number> {
    const weekdayStats: Record<string, number> = {
      '0': 0, // Sunday
      '1': 0, // Monday
      '2': 0, // Tuesday
      '3': 0, // Wednesday
      '4': 0, // Thursday
      '5': 0, // Friday
      '6': 0  // Saturday
    };

    for (const event of events) {
      const day = event.timestamp.getDay();
      weekdayStats[day] = (weekdayStats[day] || 0) + 1;
    }

    return weekdayStats;
  }

  private handleError(error: any, defaultStatus: number): Error {
    console.error(`Database analytics error: ${error.message}`);
    return new Error(`Database analytics error: ${error.message}`);
  }
}



// Create singleton instance
export const databaseAnalytics = new DatabaseAnalytics(prisma);

// Product search caching function
export async function getProductSearchFromCache(query?: string) {
  const searchQuery = query || 'all_products';
  const cacheKey = CacheKeys.productSearch(searchQuery);

  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit(cacheKey);
      return cached;
    }

    trackCacheMiss(cacheKey);

    // Cache miss - fetch from database
    let products;

    if (!query) {
      // Return all products (limited to 20)
      products = await prisma.product.findMany({
        where: {
          isDeleted: false,
        },
        take: 20,
      });
    } else {
      // Search products by name and description with the same logic as the original
      products = await prisma.product.findMany({
        where: {
          isDeleted: false,
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
            { name: { contains: query.split(' ')[0], mode: 'insensitive' } }, // Match first word
            {
              name: {
                contains: query.split(' ').pop() || '',
                mode: 'insensitive',
              },
            }, // Match last word
          ],
        },
        take: 20,
      });
    }

    // Cache the result for 20 minutes (1200 seconds)
    await redisService.setInCache(cacheKey, products, 1200);

    return products;
  } catch (error) {
    console.error('Error in getProductSearchFromCache:', error);
    throw error;
  }
}

// Product details caching function
export async function getProductDetailsFromCache(productId: string) {
  const cacheKey = CacheKeys.productDetails(productId);

  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit(cacheKey);
      return cached;
    }

    trackCacheMiss(cacheKey);

    // Cache miss - fetch from database with all relations
    const product = await prisma.product.findUnique({
      where: {
        id: productId,
      },
      include: {
        business: true,
        reviews: {
          where: {
            isPublic: true,
            isDeleted: false,
          },
          include: {
            user: true,
          },
        },
      },
    });

    if (!product) {
      return null;
    }

    // Cache the result for 15 minutes (900 seconds)
    await redisService.setInCache(cacheKey, product, 900);

    return product;
  } catch (error) {
    console.error('Error in getProductDetailsFromCache:', error);
    throw error;
  }
}