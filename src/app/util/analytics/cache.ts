
/**
 * Cache management utilities for analytics
 */
import { redisService } from '../../lib/redis';
import { CacheStats } from './types';

// Cache versioning for schema changes
const CACHE_VERSION = 'v2';

// Cache key generators for standardized cache keys
function generateCacheKey(prefix: string, ...parts: string[]): string {
  return `reviewit:${CACHE_VERSION}:${prefix}:${parts.join(':')}`;
}

function hashQuery(query: string): string {
  return require('crypto').createHash('md5').update(query.toLowerCase().trim()).digest('hex');
}

// Specific key generators for each endpoint
export const CacheKeys = {
  topReviewers: (limit: number = 6) => generateCacheKey('top_reviewers', limit.toString()),
  productSearch: (query: string) => generateCacheKey('product_search', hashQuery(query)),
  productDetails: (productId: string) => generateCacheKey('product_details', productId),
  productReviews: (productId: string, isPublic: boolean) =>
    generateCacheKey('product_reviews', productId, isPublic.toString()),
  allProducts: () => generateCacheKey('all_products'),
  viewCount: (productId: string) => generateCacheKey('view_count', productId),
  adminRecentReviews: () => generateCacheKey('admin', 'recent_reviews'),
  adminReviewStats: () => generateCacheKey('admin', 'review_stats'),
  adminDashboardMetrics: () => generateCacheKey('admin', 'dashboard_metrics'),
  adminReports: () => generateCacheKey('admin', 'reports'),
  adminReportStats: () => generateCacheKey('admin', 'report_stats'),
  latestReviews: () => generateCacheKey('reviews', 'latest'),
  popularReviews: () => generateCacheKey('reviews', 'popular'),
  trendingReviews: () => generateCacheKey('reviews', 'trending')
};

// Cache performance tracking
const cacheStats = {
  hits: 0,
  misses: 0,
  errors: 0,
  lastError: null as Date | null
};

// Circuit breaker state
const circuitBreaker = {
  isOpen: false,
  failureCount: 0,
  lastFailureTime: null as Date | null,
  threshold: 5, // Open circuit after 5 failures
  timeout: 30000 // 30 seconds timeout
};

export function trackCacheHit(key: string): void {
  cacheStats.hits++;
  console.log(`Cache HIT for key: ${key} (Hit rate: ${getCacheHitRate()}%)`);
}

export function trackCacheMiss(key: string): void {
  cacheStats.misses++;
  console.log(`Cache MISS for key: ${key} (Hit rate: ${getCacheHitRate()}%)`);
}

export function getCacheStats(): CacheStats {
  const total = cacheStats.hits + cacheStats.misses;
  return {
    hits: cacheStats.hits,
    misses: cacheStats.misses,
    hitRate: total > 0 ? (cacheStats.hits / total) * 100 : 0,
    totalRequests: total
  };
}

export function getCacheHitRate(): number {
  return getCacheStats().hitRate;
}

// Circuit breaker functions
function recordCacheFailure(): void {
  cacheStats.errors++;
  cacheStats.lastError = new Date();
  circuitBreaker.failureCount++;
  circuitBreaker.lastFailureTime = new Date();
  
  if (circuitBreaker.failureCount >= circuitBreaker.threshold) {
    circuitBreaker.isOpen = true;
    console.warn(`Cache circuit breaker opened after ${circuitBreaker.failureCount} failures`);
  }
}

function resetCircuitBreaker(): void {
  circuitBreaker.isOpen = false;
  circuitBreaker.failureCount = 0;
  circuitBreaker.lastFailureTime = null;
  console.log('Cache circuit breaker reset');
}

function shouldUseCache(): boolean {
  if (!circuitBreaker.isOpen) {
    return true;
  }
  
  // Check if timeout has passed
  if (circuitBreaker.lastFailureTime) {
    const timeSinceLastFailure = Date.now() - circuitBreaker.lastFailureTime.getTime();
    if (timeSinceLastFailure > circuitBreaker.timeout) {
      resetCircuitBreaker();
      return true;
    }
  }
  
  return false;
}

// Safe cache operations with circuit breaker
export async function safeGetFromCache<T>(key: string): Promise<T | null> {
  if (!shouldUseCache()) {
    console.log(`Cache circuit breaker is open, skipping cache for key: ${key}`);
    return null;
  }
  
  try {
    const result = await redisService.get(key);
    if (result) {
      trackCacheHit(key);
      return JSON.parse(result);
    } else {
      trackCacheMiss(key);
      return null;
    }
  } catch (error) {
    recordCacheFailure();
    console.warn(`Cache get failed for key ${key}:`, error);
    return null;
  }
}

export async function safeSetToCache(key: string, value: any, ttl?: number): Promise<boolean> {
  if (!shouldUseCache()) {
    console.log(`Cache circuit breaker is open, skipping cache set for key: ${key}`);
    return false;
  }
  
  try {
    if (ttl) {
      await redisService.set(key, JSON.stringify(value), ttl);
    } else {
      await redisService.setInCache(key, value, 3600); // Default 1 hour TTL
    }
    return true;
  } catch (error) {
    recordCacheFailure();
    console.warn(`Cache set failed for key ${key}:`, error);
    return false;
  }
}

// Cache health check
export async function checkCacheHealth(): Promise<{
  isHealthy: boolean;
  stats: CacheStats & { errors: number; lastError: Date | null };
  circuitBreakerStatus: typeof circuitBreaker;
}> {
  const testKey = generateCacheKey('health_check', Date.now().toString());
  const testValue = { timestamp: new Date().toISOString() };
  
  try {
    // Test write
    await redisService.set(testKey, JSON.stringify(testValue), 10);
    
    // Test read
    const retrieved = await redisService.get(testKey);
    const isHealthy = retrieved !== null && JSON.parse(retrieved).timestamp === testValue.timestamp;
    
    // Cleanup
    await redisService.del(testKey);
    
    return {
      isHealthy,
      stats: {
        ...getCacheStats(),
        errors: cacheStats.errors,
        lastError: cacheStats.lastError
      },
      circuitBreakerStatus: { ...circuitBreaker }
    };
  } catch (error) {
    recordCacheFailure();
    return {
      isHealthy: false,
      stats: {
        ...getCacheStats(),
        errors: cacheStats.errors,
        lastError: cacheStats.lastError
      },
      circuitBreakerStatus: { ...circuitBreaker }
    };
  }
}

// Cache invalidation utilities
export async function invalidateProductCache(productId: string): Promise<void> {
  const keys = [
    CacheKeys.productDetails(productId),
    CacheKeys.productReviews(productId, true),
    CacheKeys.productReviews(productId, false)
  ];

  for (const key of keys) {
    try {
      await redisService.del(key);
      console.log(`Invalidated cache key: ${key}`);
    } catch (error) {
      console.warn(`Failed to invalidate cache key ${key}:`, error);
    }
  }
}

// Invalidate product cache when reviews are added/updated
export async function invalidateAggregatedCachesOnReviewChange(): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats(),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.popularReviews(),
      CacheKeys.trendingReviews(),
      CacheKeys.latestReviews(),
      CacheKeys.topReviewers(6)  // Invalidate top reviewers when review counts change
    ];

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
        console.log(`Successfully invalidated cache key: ${key}`);
      } catch (error) {
        console.warn(`Failed to invalidate cache key ${key}:`, error);
      }
    }

    console.log('Admin and aggregated caches invalidated due to review change.');
  } catch (error) {
    console.error('Error invalidating admin and aggregated caches on review change:', error);
  }
}

export async function invalidateSearchCache(): Promise<void> {
  try {
    const pattern = generateCacheKey('product_search', '*');
    const keys = await redisService.keys(pattern);
    if (keys.length > 0) {
      for (const key of keys) {
        await redisService.del(key);
      }
      console.log(`Invalidated ${keys.length} search cache keys`);
    }
  } catch (error) {
    console.warn('Failed to invalidate search cache:', error);
  }
}

export async function invalidateAdminCache(): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats(),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.adminReports(),
      CacheKeys.adminReportStats()
    ];

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
        console.log(`Successfully invalidated cache key: ${key}`);
      } catch (error) {
        console.warn(`Failed to invalidate cache key ${key}:`, error);
      }
    }

    console.log('Admin caches invalidated.');
  } catch (error) {
    console.error('Error invalidating admin caches:', error);
  }
}

export async function invalidateAllProductsCache(): Promise<void> {
  try {
    const allProductsKey = CacheKeys.allProducts();
    await redisService.del(allProductsKey);
    console.log('All products cache invalidated.');
  } catch (error) {
    console.error('Error invalidating all products cache:', error);
  }
}

export async function invalidateCachesOnComment(productId: string): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.productDetails(productId),
      CacheKeys.productReviews(productId, true),
      CacheKeys.productReviews(productId, false),
    ];

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
        console.log(`Successfully invalidated cache key: ${key}`);
      } catch (error) {
        console.warn(`Failed to invalidate cache key ${key}:`, error);
      }
    }

    console.log(`Caches invalidated for product ${productId} due to a vote.`);
  } catch (error) {
    console.error(`Error invalidating caches for product ${productId} on vote:`, error);
  }
}

// Business cache invalidation utilities
export async function invalidateBusinessCaches(businessId: string): Promise<void> {
  try {
    const pattern = generateCacheKey('business_analytics', businessId, '*');
    const keys = await redisService.keys(pattern);
    
    const keysToInvalidate = [
      ...keys,
      generateCacheKey('traffic_sources', businessId, '*'),
      generateCacheKey('promotions', 'business', businessId),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats()
    ];

    for (const key of keysToInvalidate) {
      try {
        if (key.includes('*')) {
          // Handle wildcard patterns
          const wildcardKeys = await redisService.keys(key);
          for (const wildcardKey of wildcardKeys) {
            await redisService.del(wildcardKey);
          }
        } else {
          await redisService.del(key);
        }
        console.log(`Successfully invalidated cache key: ${key}`);
      } catch (error) {
        console.warn(`Failed to invalidate cache key ${key}:`, error);
      }
    }

    console.log(`Business caches invalidated for business ${businessId}.`);
  } catch (error) {
    console.error(`Error invalidating business caches for ${businessId}:`, error);
  }
}

// Product ownership change cache invalidation
export async function invalidateCachesOnOwnershipChange(productId: string, businessId?: string): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.productDetails(productId),
      CacheKeys.productReviews(productId, true),
      CacheKeys.productReviews(productId, false),
      CacheKeys.allProducts(),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats()
    ];

    // Add business-specific cache invalidation if businessId is provided
    if (businessId) {
      const businessPattern = generateCacheKey('business_analytics', businessId, '*');
      const businessKeys = await redisService.keys(businessPattern);
      keysToInvalidate.push(...businessKeys);
      keysToInvalidate.push(generateCacheKey('promotions', 'business', businessId));
    }

    // Invalidate search cache as product ownership affects search results
    await invalidateSearchCache();

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
        console.log(`Successfully invalidated cache key: ${key}`);
      } catch (error) {
        console.warn(`Failed to invalidate cache key ${key}:`, error);
      }
    }

    console.log(`Ownership change caches invalidated for product ${productId}${businessId ? ` and business ${businessId}` : ''}.`);
  } catch (error) {
    console.error(`Error invalidating ownership change caches for product ${productId}:`, error);
  }
}

// Batch cache invalidation with circuit breaker pattern
export async function batchInvalidateCache(keys: string[], maxRetries: number = 3): Promise<void> {
  // Use the raw Redis client for pipeline operations
  const client = redisService.getClient();
  const pipeline = client.pipeline();
  
  for (const key of keys) {
    pipeline.del(key);
  }

  let retries = 0;
  while (retries < maxRetries) {
    try {
      await pipeline.exec();
      console.log(`Successfully batch invalidated ${keys.length} cache keys`);
      return;
    } catch (error) {
      retries++;
      console.warn(`Batch cache invalidation attempt ${retries} failed:`, error);
      
      if (retries >= maxRetries) {
        console.error(`Failed to batch invalidate cache after ${maxRetries} attempts`);
        // Fall back to individual deletions
        for (const key of keys) {
          try {
            await redisService.del(key);
          } catch (individualError) {
            console.warn(`Failed to individually delete cache key ${key}:`, individualError);
          }
        }
      } else {
        // Wait before retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000));
      }
    }
  }
}
