/**
 * Data formatting utilities for analytics
 */
import { iAnalyticsPeriod } from './types';

export function formatViewsPerDay(data: any[]): Record<string, number> {
  const result: Record<string, number> = {};
  data.forEach((item: any) => {
    const dateKey = item.date instanceof Date ? item.date.toISOString().split('T')[0] : item.date;
    result[dateKey] = parseInt(item.views) || 0;
  });
  return result;
}

export function formatTrafficSources(data: any[]): Record<string, number> {
  const result: Record<string, number> = {};
  data.forEach((item: any) => {
    const source = item.source || 'Direct';
    result[source] = item._count?.source || item.count || 0;
  });
  return result;
}

export function formatDeviceTypes(data: any[]): Record<string, number> {
  const result: Record<string, number> = {};
  data.forEach((item: any) => {
    const deviceType = item.deviceType || 'unknown';
    result[deviceType] = item._count?.deviceType || item.count || 0;
  });
  return result;
}

export function calculateConversionRate(viewCount: number, reviewCount: number): number {
  if (viewCount === 0) return 0;
  return (reviewCount / viewCount) * 100;
}

export function calculateUniqueVisitorConversionRate(uniqueVisitors: number, reviewCount: number): number {
  if (uniqueVisitors === 0) return 0;
  return (reviewCount / uniqueVisitors) * 100;
}

export function formatTopProducts(data: any[]): Array<{ id: string; name: string; views: number; conversion: number }> {
  return data.map((item: any) => ({
    id: item.id,
    name: item.name,
    views: parseInt(item.views) || 0,
    conversion: parseFloat(item.conversion) || 0
  }));
}

export function calculateSourceConversionRate(source: string, productIds: string[], period: iAnalyticsPeriod): number {
  // Implementation would go here
  return 0;
}