import { PrismaClient } from '@prisma/client';
import { UserDATA } from './Interfaces';

const prisma = new PrismaClient();

export async function createInitialAdmin(userData: UserDATA) {
    try {
        // Check if admin already exists
        const existingAdmin = await prisma.user.findFirst({
            where: {
                role: 'ADMIN',
                isDeleted: false,
            },
        });

        if (existingAdmin) {
            console.log('Admin user already exists');
            return existingAdmin;
        }

        // Create the initial admin user
        const admin = await prisma.user.create({
            data: {
                userName: userData.userName,
                email: userData.email,
                firstName: userData.firstName,
                lastName: userData.lastName,
                clerkUserId: userData.userId,
                avatar: userData.avatar || null,
                role: 'ADMIN',
                status: 'ACTIVE',
                lastLoginAt: new Date(),
                loginCount: 1,
            },
        });

        console.log('Initial admin user created successfully');
        return admin;
    } catch (error) {
        console.error('Error creating admin user:', error);
        throw error;
    }
}

export async function isUserAdmin(userId: string): Promise<boolean> {
    try {
        const user = await prisma.user.findUnique({
            where: {
                id: userId,
            },
            select: {
                role: true,
                status: true,
            },
        });

        return user?.role === 'ADMIN';
    } catch (error) {
        console.error('Error checking admin status:', error);
        return false;
    }
}

export async function requireAdmin(userId: string): Promise<boolean> {
    const isAdmin = await isUserAdmin(userId);
    if (!isAdmin) {
        throw new Error('Unauthorized: Admin access required');
    }
    return true;
} 