import { iComment, iProduct, iReview, iUser } from "./Interfaces";

/**
 * Checks if a comment is from the owner of the product being reviewed
 *
 * @param comment The comment to check
 * @param product The product associated with the review
 * @returns boolean indicating if the comment is from the product owner
 */
export function isOwnerComment(comment: iComment, product?: iProduct): boolean {
  if (!comment || !comment.user || !product) {
    return false;
  }

  // Check if the comment's user is the product owner
  const isOwner =
    (product.ownerId && comment.user.id === product.ownerId) ||
    (product.business?.ownerId && comment.user.id === product.business.ownerId);

  // Ensure we return a boolean value
  return isOwner === true;
}

/**
 * Checks if the current user is the owner of the product
 *
 * @param currentUser The current user
 * @param product The product to check ownership for
 * @returns boolean indicating if the current user is the product owner
 */
export function isCurrentUserProductOwner(
  currentUser: iUser,
  product?: iProduct
): boolean {
  if (!currentUser || !product) {
    return false;
  }

  // Check if the current user is the product owner
  const isOwner =
    (product.ownerId && currentUser.id === product.ownerId) ||
    (product.business?.ownerId && currentUser.id === product.business.ownerId);

  // Ensure we return a boolean value
  return isOwner === true;
}
