/**
 * Legacy databaseAnalytics.ts - Redirects to new modular analytics system
 * 
 * This file has been refactored into a modular structure:
 * - /analytics/types.ts - Type definitions
 * - /analytics/cache.ts - Cache management utilities
 * - /analytics/formatters.ts - Data formatting functions
 * - /analytics/product.ts - Product analytics
 * - /analytics/business.ts - Business analytics
 * - /analytics/admin.ts - Admin analytics
 * - /analytics/index.ts - Main entry point
 */

// Re-export everything from the new modular analytics system
export * from './analytics';

// Additional backward compatibility exports
export { 
  invalidateAllProductsCache, 
  invalidateSearchCache,
  invalidateBusinessCaches,
  invalidateCachesOnOwnershipChange,
  batchInvalidateCache,
  safeGetFromCache,
  safeSetToCache,
  checkCacheHealth
} from './analytics/cache';

// For backward compatibility, also export the main functions directly
export {
  databaseAnalytics,
  getBusinessAnalyticsFromDB,
  getTrafficSourcesFromDB,
  getTopReviewersFromCache,
  getAllProductsFromCache,
  getProductReviewsFromCache,
  getProductSearchFromCache,
  getProductDetailsFromCache,
  DatabaseAnalytics,
  // Cache functions
  trackCacheHit,
  trackCacheMiss,
  getCacheStats,
  invalidateProductCache,
  invalidateAdminCache,
  // Admin functions
  getAdminRecentReviewsFromCache,
  getAdminReviewStatsFromCache,
  getAdminDashboardMetricsFromCache,
  getPopularReviewsFromCache,
  getLatestReviewsFromCache,
  getTrendingReviewsFromCache,
  // View count functions
  incrementViewCountInRedis,
  getCurrentViewCount,
  flushViewCountsToDatabase,
  manualFlushViewCounts,
  testGetKenFromRedis
} from './analytics';