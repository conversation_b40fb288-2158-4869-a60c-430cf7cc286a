import {
  iBusinessAnalytics,
  iProductPerformance,
  iTrafficSource,
  iAnalyticsPeriod,
} from "./Interfaces";

/**
 * Get aggregated business analytics for a specific date range
 * @param businessId The business ID to get analytics for
 * @param period The date range period to fetch data for
 */
export async function getBusinessAnalytics(
  businessId: string,
  period: iAnalyticsPeriod
): Promise<iBusinessAnalytics> {
  // In a real implementation, this would fetch data from the backend
  try {
    console.log(`Fetching business analytics for business ID: ${businessId}`);
    const response = await fetch(
      `/api/analytics/business?id=${businessId}&start=${period.startDate.toISOString()}&end=${period.endDate.toISOString()}`,
      {
        credentials: 'include'
      }
    );

    if (!response.ok) {
      console.error(`API error: ${response.status} ${response.statusText}`);
      throw new Error(
        `Failed to fetch analytics data: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    console.log("Successfully fetched business analytics data");
    return data;
  } catch (error) {
    console.error("Error fetching business analytics:", error);
    // Return mock data as fallback
    console.log("Returning mock business analytics data");
    return getMockBusinessAnalytics(businessId);
  }
}

/**
 * Get traffic sources for a specific business and date range
 * @param businessId The business ID to get traffic sources for
 * @param period The date range period to fetch data for
 */
export async function getTrafficSources(
  businessId: string,
  period: iAnalyticsPeriod
): Promise<iTrafficSource[]> {
  // In a real implementation, this would fetch data from the backend
  try {
    console.log(`Fetching traffic sources for business ID: ${businessId}`);
    const response = await fetch(
      `/api/analytics/traffic?id=${businessId}&start=${period.startDate.toISOString()}&end=${period.endDate.toISOString()}`,
      {
        credentials: 'include'
      }
    );

    if (!response.ok) {
      console.error(`API error: ${response.status} ${response.statusText}`);
      throw new Error(
        `Failed to fetch traffic source data: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    console.log("Successfully fetched traffic sources data");
    return data;
  } catch (error) {
    console.error("Error fetching traffic sources:", error);
    // Return mock data as fallback
    console.log("Returning mock traffic sources data");
    return getMockTrafficSources(businessId);
  }
}

/**
 * Get product performance data for a specific business and date range
 * @param businessId The business ID to get product performance for
 * @param period The date range period to fetch data for
 */
export async function getProductPerformance(
  businessId: string,
  period: iAnalyticsPeriod
): Promise<iProductPerformance[]> {
  // In a real implementation, this would fetch data from the backend
  try {
    console.log(`Fetching product performance for business ID: ${businessId}`);
    const response = await fetch(
      `/api/analytics/products?id=${businessId}&start=${period.startDate.toISOString()}&end=${period.endDate.toISOString()}`,
      {
        credentials: 'include'
      }
    );

    if (!response.ok) {
      console.error(`API error: ${response.status} ${response.statusText}`);
      throw new Error(
        `Failed to fetch product performance data: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    console.log("Successfully fetched product performance data");
    return data;
  } catch (error) {
    console.error("Error fetching product performance:", error);
    // Return mock data as fallback
    console.log("Returning empty product performance data");
    return [];
  }
}

/**
 * Generate daily views data for the specified date range
 * @param startDate Start date
 * @param endDate End date
 * @param baseValue Base views value
 * @param growth Daily growth factor
 */
function generateDailyViewsData(
  startDate: Date,
  endDate: Date,
  baseValue: number = 300,
  growth: number = 1.02
): Record<string, number> {
  const result: Record<string, number> = {};
  const currentDate = new Date(startDate);
  let currentValue = baseValue;

  while (currentDate <= endDate) {
    const dateKey = currentDate.toISOString().split("T")[0];
    // Add some randomness to the data
    const randomFactor = 0.8 + Math.random() * 0.4; // 0.8 to 1.2
    result[dateKey] = Math.floor(currentValue * randomFactor);

    // Increase value for next day (simulating growth)
    currentValue *= growth;

    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return result;
}

/**
 * Get mock business analytics
 * @param businessId The business ID
 */
function getMockBusinessAnalytics(businessId: string): iBusinessAnalytics {
  // Generate daily views for the last 30 days
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30);

  const viewsPerDay = generateDailyViewsData(startDate, endDate);
  const totalViews = Object.values(viewsPerDay).reduce((a, b) => a + b, 0);

  return {
    id: `ba-${businessId}`,
    businessId,
    totalViews,
    uniqueVisitors: Math.floor(totalViews * 0.65), // 65% are unique
    totalReviews: Math.floor(totalViews * 0.013), // About 1.3% of views leave reviews
    averageRating: 4.2,
    viewsPerDay,
    trafficSources: {
      Google: Math.floor(totalViews * 0.45),
      Direct: Math.floor(totalViews * 0.22),
      "Social Media": Math.floor(totalViews * 0.15),
      Email: Math.floor(totalViews * 0.08),
      Referral: Math.floor(totalViews * 0.1),
    },
    deviceTypes: {
      Mobile: Math.floor(totalViews * 0.55),
      Desktop: Math.floor(totalViews * 0.35),
      Tablet: Math.floor(totalViews * 0.1),
    },
    conversionRate: 2.8,
    topProducts: [
      {
        id: "product1",
        name: "Premium Coffee Maker",
        views: Math.floor(totalViews * 0.25),
        conversion: 3.5,
      },
      {
        id: "product2",
        name: "Espresso Machine",
        views: Math.floor(totalViews * 0.17),
        conversion: 2.2,
      },
      {
        id: "product3",
        name: "Coffee Grinder Pro",
        views: Math.floor(totalViews * 0.15),
        conversion: 2.7,
      },
    ],
    lastUpdated: new Date(),
  };
}

/**
 * Get mock traffic sources
 * @param businessId The business ID
 */
function getMockTrafficSources(businessId: string): iTrafficSource[] {
  return [
    {
      id: "ts1",
      businessId,
      source: "Google",
      medium: "organic",
      visitors: 4560,
      sessions: 5120,
      bounceRate: 32.5,
      conversionRate: 3.2,
      lastUpdated: new Date(),
    },
    {
      id: "ts2",
      businessId,
      source: "Facebook",
      medium: "social",
      campaign: "Summer Promotion",
      visitors: 1250,
      sessions: 1380,
      bounceRate: 45.2,
      conversionRate: 2.1,
      lastUpdated: new Date(),
    },
    {
      id: "ts3",
      businessId,
      source: "Direct",
      medium: "none",
      visitors: 2830,
      sessions: 3210,
      bounceRate: 28.4,
      conversionRate: 4.5,
      lastUpdated: new Date(),
    },
    {
      id: "ts4",
      businessId,
      source: "Email",
      medium: "email",
      campaign: "Newsletter",
      visitors: 980,
      sessions: 1020,
      bounceRate: 18.9,
      conversionRate: 7.2,
      lastUpdated: new Date(),
    },
    {
      id: "ts5",
      businessId,
      source: "Instagram",
      medium: "social",
      visitors: 670,
      sessions: 720,
      bounceRate: 51.3,
      conversionRate: 1.8,
      lastUpdated: new Date(),
    },
  ];
}
