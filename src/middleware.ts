import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextRequest } from "next/server";
import { prisma } from "@/app/util/prismaClient";

/**
 * MIDDLEWARE ARCHITECTURE OVERVIEW:
 * 
 * This is the main Next.js middleware that runs on EVERY request before routes are matched.
 * It handles global concerns like:
 * 1. Authentication via Clerk
 * 2. Bot detection
 * 3. Public route exemptions
 * 4. Admin route protection
 * 
 * The specialized middleware in the /middleware folder is NOT automatically run.
 * Those middleware functions are imported and used within specific API routes or
 * page handlers as needed, providing more targeted functionality.
 */

const isOnLockDownList = createRouteMatcher([
  "/cr(.*)",
  "/submit(.*)",
]);
const isAdminRoute = createRouteMatcher(["/admin(.*)", "/api/admin/(.*)"]);
const isRootPath = (req: Request) => new URL(req.url).pathname === "/";

// Enhanced function to detect bots and social media crawlers
const isBot = (req: Request) => {
  const userAgent = req.headers.get("user-agent") || "";
  const lowerUserAgent = userAgent.toLowerCase();

  // Comprehensive list of social media crawlers and bots
  const botPatterns = [
    // Search engines
    "googlebot",
    "bingbot",
    "slurp",
    "duckduckbot",
    "yandex",
    "baiduspider",
    // Social media
    "facebookexternalhit",
    "twitterbot",
    "linkedinbot",
    "whatsapp",
    "slackbot",
    "discordbot",
    "telegrambot",
    "viber",
    "pinterest",
    // Generic bot identifiers
    "bot",
    "crawler",
    "spider"
  ];

  return botPatterns.some((pattern) => lowerUserAgent.includes(pattern));
};

/**
 * Determines if a route should be publicly accessible without authentication
 * This runs as part of the main middleware flow
 */
function isPublicApiRoute(req: NextRequest) {
  const path = req.nextUrl.pathname;

  // Check for all debug endpoints
  if (path.startsWith('/api/debug-') || path.startsWith('/api/test-')) {
    return true;
  }

  // Check for all OG image related routes
  if (path.startsWith('/api/og') || path.includes('/api/og/')) {
    return true;
  }

  // Allow access to any metadata-related endpoints
  if (path.includes('/api/metadata') || path.includes('/metadata')) {
    return true;
  }

  // Allow access to top reviewers endpoint
  if (path === '/api/top-reviewers') {
    return true;
  }

  // Check for the reviews page with ID parameter which needs to be shareable
  if (path.includes('/reviews') && req.nextUrl.searchParams.has('id')) {
    return true;
  }

  if (path.includes('/browse')) {
    return true;
  }
  // Check for the full review page (fr) with ID and productid parameters
  if (path.includes('/fr') &&
    req.nextUrl.searchParams.has('id') &&
    req.nextUrl.searchParams.has('productid')) {
    return true;
  }

  return false;
}

/**
 * Specialized admin route handler that runs as part of the main middleware
 * This is different from the admin-auth.ts middleware which is used within API routes
 */
async function handleAdminRoute(req: NextRequest) {
  try {
    // Use Clerk middleware to check authentication
    // @ts-ignore - Clerk middleware expects 2 args but works with 1
    const response = await clerkMiddleware()(req);

    // If response is null, undefined, or a Response object, handle appropriately
    if (!response || response instanceof Response) {
      return response || Response.redirect("https://accounts.reviewit.gy/sign-in");
    }

    // Get the auth object from the response
    // @ts-ignore - We know auth exists on the response
    const auth = response.auth;

    if (!auth || !auth.userId) {
      // Redirect to sign-in page
      return Response.redirect("https://accounts.reviewit.gy/sign-in");
    }

    // Check if the user has admin role in database
    const user = await prisma.user.findFirst({
      where: {
        clerkUserId: auth.userId
      },
      select: {
        role: true,
        status: true
      }
    });

    if (!user || user.role !== "ADMIN" || user.status !== "ACTIVE") {
      // Redirect to home page
      const redirectUrl = new URL("/", req.url);
      redirectUrl.searchParams.set("redirectSource", "adminCheck");
      return Response.redirect(redirectUrl);
    }

    // User is authenticated and has admin role, allow access
    return null;
  } catch (error) {
    console.error("Error in admin route middleware:", error);
    // Return a simple redirect to avoid header issues
    return Response.redirect("https://accounts.reviewit.gy/sign-in");
  }
}

/**
 * Main middleware function that runs on every request
 * This is the entry point for Next.js middleware processing
 */
export default async function middleware(req: NextRequest) {
  // Check for /products route and redirect to /browse
  if (req.nextUrl.pathname === '/products') {
    return Response.redirect(new URL('/browse', req.url));
  }

  // Bot check must be first - allow all bots to access the site
  if (isBot(req)) {
    return; // Let the request continue without auth
  }

  // Skip auth for public API routes like OG image generation
  if (isPublicApiRoute(req)) {
    return;
  }

  // Explicitly exempt the root path from Clerk processing
  if (isRootPath(req)) {
    return; // Serve the page directly without Clerk redirects
  }

  // Skip middleware for the root path if it's a redirect from admin check
  const url = new URL(req.url);
  const redirectSource = url.searchParams.get("redirectSource");
  if (isRootPath(req) && redirectSource === "adminCheck") {
    return;
  }

  // Handle admin routes separately
  if (isAdminRoute(req)) {
    return handleAdminRoute(req);
  }

  // For all other routes, use the standard Clerk middleware
  // @ts-ignore - Clerk middleware expects 2 args but works with 1
  return clerkMiddleware()(req);
}

export const config = {
  matcher: ["/((?!.*\\..*|_next).*)", "/", "/(api|trpc)(.*)"],
};
