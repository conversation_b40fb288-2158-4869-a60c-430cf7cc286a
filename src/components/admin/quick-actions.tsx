import { useRouter } from "next/navigation";
import {
  MessageSquare,
  Users,
  Flag,
  Settings,
  Database,
  FileCheck,
} from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

const quickActions = [
  {
    title: "Review Management",
    icon: MessageSquare,
    href: "/admin/reviews",
    description: "Manage pending, approved, and rejected reviews",
    color: "text-blue-500",
  },
  {
    title: "User Management",
    icon: Users,
    href: "/admin/users",
    description: "Manage user accounts and permissions",
    color: "text-green-500",
  },
  {
    title: "Product Management",
    icon: Database,
    href: "/admin/products",
    description: "Manage products in the database",
    color: "text-green-500",
  },
  {
    title: "Product Claims",
    icon: FileCheck,
    href: "/admin/claims",
    description: "Review and manage product ownership claims",
    color: "text-orange-500",
  },
  {
    title: "Word Management",
    icon: Database,
    href: "/admin/words",
    description: "Manage words in the database",
    color: "text-purple-500",
  },
  {
    title: "Reported Content",
    icon: Flag,
    href: "/admin/reports",
    description: "Review and manage reported reviews",
    color: "text-red-500",
  },
  {
    title: "System Settings",
    icon: Settings,
    href: "/admin/settings",
    description: "Configure system settings and preferences",
    color: "text-gray-500",
  },
] as const;

export function QuickActions() {
  const router = useRouter();

  return (
    <div className="px-4">
      <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {quickActions.map((action) => (
          <Card
            key={action.title}
            className="cursor-pointer hover:bg-accent/50 transition-colors"
            onClick={() => router.push(action.href)}
          >
            <CardHeader>
              <div className="flex items-center gap-2">
                <action.icon className={`w-5 h-5 ${action.color}`} />
                <CardTitle className="text-sm font-medium">
                  {action.title}
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                {action.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
