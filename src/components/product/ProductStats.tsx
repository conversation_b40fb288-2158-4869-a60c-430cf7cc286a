import React from 'react';
import { iProduct } from '@/app/util/Interfaces';
import { BarChart3, Eye, Calendar, Star, TrendingUp, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import dayjs from 'dayjs';

interface ProductStatsProps {
    product: iProduct;
}

const ProductStats: React.FC<ProductStatsProps> = ({ product }) => {
    // Calculate days since creation
    const daysSinceCreation = product.createdDate
        ? dayjs().diff(dayjs(product.createdDate), 'day')
        : 0;

    // Calculate review growth rate (reviews per day)
    const reviewGrowthRate = product.reviews && product.reviews.length > 0 && daysSinceCreation > 0
        ? (product.reviews.length / daysSinceCreation).toFixed(1)
        : 0;

    // Calculate verification status
    const isVerified = product.hasOwner && product.business?.isVerified === true;

    return (
        <Card className="w-full">
            <CardHeader>
                <CardTitle className="text-xl font-semibold">Product Statistics</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-2 gap-4">
                    {/* Views */}
                    <div className="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <Eye className="h-5 w-5 text-gray-500 dark:text-gray-400 mb-1" />
                        <span className="text-2xl font-bold">{product.viewCount || 0}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">Views</span>
                    </div>

                    {/* Reviews */}
                    <div className="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <Star className="h-5 w-5 text-yellow-500 mb-1" />
                        <span className="text-2xl font-bold">{product.reviews?.length || 0}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">Reviews</span>
                    </div>

                    {/* Age */}
                    <div className="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <Calendar className="h-5 w-5 text-blue-500 mb-1" />
                        <span className="text-2xl font-bold">{daysSinceCreation}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">Days Listed</span>
                    </div>

                    {/* Growth Rate */}
                    <div className="flex flex-col items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <TrendingUp className="h-5 w-5 text-green-500 mb-1" />
                        <span className="text-2xl font-bold">{reviewGrowthRate}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">Reviews/Day</span>
                    </div>
                </div>

                {/* Verification Status */}
                <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg flex items-center justify-between">
                    <div className="flex items-center">
                        <CheckCircle className={`h-5 w-5 mr-2 ${isVerified ? 'text-green-500' : 'text-gray-400'}`} />
                        <span className="text-sm font-medium">Verification Status</span>
                    </div>
                    <span className={`text-sm font-medium ${isVerified ? 'text-green-500' : 'text-gray-500'}`}>
                        {isVerified ? 'Verified' : 'Unverified'}
                    </span>
                </div>
            </CardContent>
        </Card>
    );
};

export default ProductStats; 