import React from 'react';
import { Star } from 'lucide-react';
import { iProduct, iReview } from '@/app/util/Interfaces';
import { calculateReviewsSummary } from "@/app/util/calculateReviewSummary";
import { MINIMUM_REVIEWS } from '@/app/config/rating';

interface RatingDistributionProps {
    product?: iProduct;
    reviews?: iReview[];
}

const RatingDistribution: React.FC<RatingDistributionProps> = ({ product, reviews: propReviews }) => {
    // Use reviews from props if provided, otherwise use product.reviews
    const reviews = propReviews || product?.reviews || [];

    // Use the helper function to calculate distribution directly from reviews
    const reviewSummaryData = calculateReviewsSummary(reviews);
    const totalReviews = reviews.length;

    // Log the calculated summary for debugging
    console.log('Calculated reviewSummaryData:', reviewSummaryData);
    console.log('Total reviews from array length:', totalReviews);

    return (
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Rating Distribution</h2>
            <div className="space-y-3">
                {[5, 4, 3, 2, 1].map((star) => {
                    // Get percentage and count from the calculated summary
                    const percentage = reviewSummaryData[`percentage${star}Stars` as keyof typeof reviewSummaryData];
                    // Access count using a specific key type
                    const starKey = star as keyof typeof reviewSummaryData.counts;
                    const count = reviewSummaryData.counts[starKey];

                    return (
                        <div key={star} className="flex items-center">
                            <div className="flex items-center w-24">
                                <span className="text-sm font-medium mr-2">{star}</span>
                                <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                            </div>
                            <div className="flex-1 h-2 bg-gray-200 rounded-full mx-2">
                                {count > 0 ? (
                                    <div
                                        className="h-2 bg-yellow-400 rounded-full"
                                        style={{ width: `${percentage}%` }}
                                    ></div>
                                ) : (
                                    <div className="h-2 bg-gray-200 rounded-full w-0"></div>
                                )}
                            </div>
                            <div className="w-16 text-right">
                                <span className="text-sm font-medium">{count}</span>
                            </div>
                        </div>
                    );
                })}
            </div>
            <div className="mt-4 text-center">
                <div className="text-sm text-gray-500 mb-2">
                    Based on {totalReviews} {totalReviews === 1 ? 'review' : 'reviews'}
                </div>
                {totalReviews < MINIMUM_REVIEWS && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div className="text-sm text-blue-800">
                            <strong>Note:</strong> Average ratings are displayed after {MINIMUM_REVIEWS} reviews to ensure reliability.
                            <div className="mt-1 text-xs text-blue-600">
                                {MINIMUM_REVIEWS - totalReviews} more review{MINIMUM_REVIEWS - totalReviews !== 1 ? 's' : ''} needed
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default RatingDistribution; 