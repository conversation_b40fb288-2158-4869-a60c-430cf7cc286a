"use client";

import React from "react";
import { iProduct } from "@/app/util/Interfaces";
import { GoogleMap, LoadScript, Marker } from "@react-google-maps/api";
import Link from "next/link";
import { MapPin } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface ProductMapProps {
  product: iProduct;
}

const ProductMap: React.FC<ProductMapProps> = ({ product }) => {
  // Check if product has location data
  if (!product.latitude || !product.longitude) {
    return null;
  }

  const location = {
    lat: product.latitude,
    lng: product.longitude,
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-lg font-semibold">Location</h2>
        <Link href={`/location?id=${product.id}`}>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
          >
            <MapPin size={16} />
            <span>View Full Map</span>
          </Button>
        </Link>
      </div>
      <div className="h-[300px]">
        <LoadScript
          googleMapsApiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ""}
        >
          <GoogleMap
            mapContainerStyle={{ width: "100%", height: "100%" }}
            center={location}
            zoom={15}
          >
            <Marker position={location} />
          </GoogleMap>
        </LoadScript>
      </div>
      {product.address && (
        <div className="p-4 border-t border-gray-100">
          <Link
            href={`/location?id=${product.id}`}
            className="text-sm text-gray-600 hover:text-blue-600 transition-colors flex items-center"
          >
            <MapPin size={14} className="mr-1 flex-shrink-0" />
            <span>{product.address}</span>
          </Link>
        </div>
      )}
    </div>
  );
};

export default ProductMap;
