import React from "react";
import { Phone, Mail, Globe, MapPin, Clock } from "lucide-react";
import { iProduct } from "@/app/util/Interfaces";
import OpeningHours from "@/app/components/OpeningHours";
import Link from "next/link";

interface ContactInformationProps {
  product: iProduct;
}

const ContactInformation: React.FC<ContactInformationProps> = ({ product }) => {
  return (
    <section className="bg-white rounded-xl shadow-sm p-6 mb-6">
      <h2 className="text-xl font-semibold mb-4">Contact Information</h2>
      <div className="space-y-4">
        {product.telephone && (
          <div className="flex items-start gap-3">
            <Phone
              className="h-5 w-5 text-gray-500 mt-0.5"
              aria-hidden="true"
            />
            <div>
              <h3 className="font-medium">Phone</h3>
              <a
                href={`tel:${product.telephone}`}
                className="text-blue-600 hover:underline"
              >
                {product.telephone}
              </a>
            </div>
          </div>
        )}

        {product.email && (
          <div className="flex items-start gap-3">
            <Mail className="h-5 w-5 text-gray-500 mt-0.5" aria-hidden="true" />
            <div>
              <h3 className="font-medium">Email</h3>
              <a
                href={`mailto:${product.email}`}
                className="text-blue-600 hover:underline"
              >
                {product.email}
              </a>
            </div>
          </div>
        )}

        {product.website && product.website.length > 0 && (
          <div className="flex items-start gap-3">
            <Globe
              className="h-5 w-5 text-gray-500 mt-0.5"
              aria-hidden="true"
            />
            <div>
              <h3 className="font-medium">Website</h3>
              {product.website.map((site, i) => (
                <a
                  key={i}
                  href={site}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block text-blue-600 hover:underline"
                >
                  {site}
                </a>
              ))}
            </div>
          </div>
        )}

        {product.address && (
          <div className="flex items-start gap-3">
            <MapPin
              className="h-5 w-5 text-gray-500 mt-0.5"
              aria-hidden="true"
            />
            <div>
              <h3 className="font-medium">Address</h3>
              {product.latitude && product.longitude ? (
                <Link
                  href={`/location?id=${product.id}`}
                  className="text-blue-600 hover:underline"
                >
                  {product.address}
                </Link>
              ) : (
                <p>{product.address}</p>
              )}
            </div>
          </div>
        )}

        {product.openingHrs &&
          product.closingHrs &&
          product.openingDays &&
          product.openingDays.length > 0 && (
            <div className="flex items-start gap-3">
              <Clock
                className="h-5 w-5 text-gray-500 mt-0.5"
                aria-hidden="true"
              />
              <div>
                <h3 className="font-medium">Business Hours</h3>
                <OpeningHours product={product} />
              </div>
            </div>
          )}
      </div>
    </section>
  );
};

export default ContactInformation;
