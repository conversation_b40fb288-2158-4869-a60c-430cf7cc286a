'use client'

import React, { useEffect } from 'react';
import { iProduct } from '@/app/util/Interfaces';
import ProductHero from '@/components/product/ProductHero';
import ProductGallery from '@/components/product/ProductGallery';
import RatingDistribution from '@/components/product/RatingDistribution';
import ContactInformation from '@/components/product/ContactInformation';
import BusinessInformation from '@/components/product/BusinessInformation';
import RecentReviews from '@/components/product/RecentReviews';
import RelatedProducts from '@/components/product/RelatedProducts';
import ProductStats from '@/components/product/ProductStats';
import ProductPromotions from '@/components/product/ProductPromotions';
import TrustBadge from '@/components/product/TrustBadge';
import ProductMap from '@/components/product/ProductMap';
import { useProductAnalytics } from '@/app/hooks/useProductAnalytics';
import FAQSection from '@/app/components/FAQSection';

interface ProductPageClientProps {
    product: iProduct;
}

const ProductPageClient: React.FC<ProductPageClientProps> = ({ product }) => {
    // Initialize analytics tracking with all options enabled
    const { isTracking, logManualEvent } = useProductAnalytics(product.id || '', {
        trackDuration: true,
        trackScrollDepth: true,
        trackClicks: true,
    });

    // Ensure product ID exists
    if (!product.id) {
        console.error('Product ID is missing');
        return null;
    }

    // Log specific user interactions
    const handleGalleryView = () => {
        logManualEvent('gallery_view');
    };

    const handleContactView = () => {
        logManualEvent('contact_info_view');
    };

    const handleMapView = () => {
        logManualEvent('map_view');
    };

    const handleFAQView = () => {
        logManualEvent('faq_view');
    };

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Main Content */}
                <div className="lg:col-span-2 space-y-6">
                    <ProductHero product={product} />
                    <div onClick={handleGalleryView}>
                        <ProductGallery product={product} />
                    </div>
                    <RatingDistribution product={product} />
                    <RecentReviews product={product} />
                    <div onClick={handleFAQView}>
                        <FAQSection
                            product={product}
                            category={product.tags[0]}
                            productName={product.name}
                        />
                    </div>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                    <div onClick={handleContactView}>
                        <ContactInformation product={product} />
                    </div>
                    <div onClick={handleMapView}>
                        <ProductMap product={product} />
                    </div>
                    <ProductPromotions product={product} />
                    <BusinessInformation product={product} />
                    <ProductStats product={product} />
                    <TrustBadge product={product} />
                    <RelatedProducts product={product} />
                </div>
            </div>
        </div>
    );
};

export default ProductPageClient; 