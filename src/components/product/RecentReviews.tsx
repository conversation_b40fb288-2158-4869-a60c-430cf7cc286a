import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ThumbsUp, MessageCircle } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import StarRating from './StarRating';
import { iProduct } from '@/app/util/Interfaces';
import DOMPurify from 'dompurify';

interface RecentReviewsProps {
    product: iProduct;
}

const RecentReviews: React.FC<RecentReviewsProps> = ({ product }) => {

    const createMarkup = (htmlContent: string) => {
        if (typeof window !== 'undefined') {
            return { __html: DOMPurify.sanitize(htmlContent) };
        }
        return { __html: '' };
    };

    return (
        <section className="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Recent Reviews</h2>
                <Link
                    href={`/reviews?id=${product.id}`}
                    className="text-blue-600 hover:underline"
                >
                    See all reviews
                </Link>
            </div>

            {product.reviews && product.reviews.length > 0 ? (
                <div className="space-y-6">
                    {product.reviews.slice(0, 3).map((review) => (
                        <div key={review.id} className="border-b pb-6 last:border-0">
                            <div className="flex justify-between mb-2">
                                <div className="flex items-center gap-2">
                                    <Avatar>
                                        <AvatarImage
                                            src={review.user?.avatar || ""}
                                            alt={`${review.user?.firstName} ${review.user?.lastName}`}
                                        />
                                        <AvatarFallback>
                                            {review.user?.firstName?.charAt(0)}
                                            {review.user?.lastName?.charAt(0)}
                                        </AvatarFallback>
                                    </Avatar>
                                    <div>
                                        <p className="font-medium">
                                            {review.user?.firstName} {review.user?.lastName}
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            {review.createdDate ? new Date(review.createdDate).toLocaleDateString() : 'No date'}
                                        </p>
                                    </div>
                                </div>
                                <StarRating rating={review.rating} />
                            </div>
                            <h3 className="font-medium mb-1">{review.title}</h3>
                            <div
                                className="text-gray-700 prose-sm"
                                dangerouslySetInnerHTML={createMarkup(review.body)}
                            />

                            {review.images && review.images.length > 0 && (
                                <div className="flex gap-2 mt-3">
                                    {review.images.slice(0, 4).map((image, i) => (
                                        <div key={i} className="h-16 w-16 rounded overflow-hidden">
                                            <Image
                                                src={image}
                                                alt={`Review image ${i + 1}`}
                                                width={64}
                                                height={64}
                                                className="object-cover w-full h-full"
                                                loading="lazy"
                                            />
                                        </div>
                                    ))}
                                    {review.images.length > 4 && (
                                        <div className="h-16 w-16 rounded bg-gray-100 flex items-center justify-center">
                                            <span className="text-sm text-gray-500">
                                                +{review.images.length - 4}
                                            </span>
                                        </div>
                                    )}
                                </div>
                            )}

                            <div className="flex gap-4 mt-3">
                                <div className="flex items-center gap-1 text-sm text-gray-500">
                                    <ThumbsUp className="h-4 w-4" aria-hidden="true" />
                                    <span>{review.helpfulVotes || 0}</span>
                                </div>
                                <div className="flex items-center gap-1 text-sm text-gray-500">
                                    <MessageCircle className="h-4 w-4" aria-hidden="true" />
                                    <span>{review.comments?.length || 0}</span>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="text-center py-8 text-gray-500">
                    <p>No reviews yet. Be the first to review this product!</p>
                    <Link href={`/cr/?id=${product.id}&rating=3`}>
                        <Button className="mt-4">Write a Review</Button>
                    </Link>
                </div>
            )}
        </section>
    );
};

export default RecentReviews; 