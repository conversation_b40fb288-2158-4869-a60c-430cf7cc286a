import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { ThumbsUp, MessageCircle } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import StarRating from './StarRating';
import ReviewFilter, { SortOption, RatingFilter } from './ReviewFilter';
import ReviewPagination from './ReviewPagination';
import { iProduct, iReview } from '@/app/util/Interfaces';

interface AllReviewsProps {
    product: iProduct;
}

const REVIEWS_PER_PAGE = 5;

const AllReviews: React.FC<AllReviewsProps> = ({ product }) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [sort, setSort] = useState<SortOption>('newest');
    const [ratingFilter, setRatingFilter] = useState<RatingFilter>(0);
    const [hasImages, setHasImages] = useState(false);
    const [hasVideos, setHasVideos] = useState(false);
    const [filteredReviews, setFilteredReviews] = useState<iReview[]>([]);
    const [totalPages, setTotalPages] = useState(1);

    // Apply filters and sorting
    useEffect(() => {
        let reviews = product.reviews || [];

        // Validate reviews array
        if (!Array.isArray(reviews)) {
            console.error('Invalid reviews data:', reviews);
            reviews = [];
        }

        // Apply rating filter
        if (ratingFilter > 0) {
            reviews = reviews.filter(review => review.rating >= ratingFilter);
        }

        // Apply media filters
        if (hasImages) {
            reviews = reviews.filter(review => review.images && review.images.length > 0);
        }

        if (hasVideos) {
            reviews = reviews.filter(review => review.videos && review.videos.length > 0);
        }

        // Apply sorting
        reviews = [...reviews].sort((a, b) => {
            switch (sort) {
                case 'newest':
                    return new Date(b.createdDate || 0).getTime() - new Date(a.createdDate || 0).getTime();
                case 'oldest':
                    return new Date(a.createdDate || 0).getTime() - new Date(b.createdDate || 0).getTime();
                case 'highest':
                    return b.rating - a.rating;
                case 'lowest':
                    return a.rating - b.rating;
                case 'most-helpful':
                    return (b.helpfulVotes || 0) - (a.helpfulVotes || 0);
                default:
                    return 0;
            }
        });

        // Calculate total pages
        setTotalPages(Math.ceil(reviews.length / REVIEWS_PER_PAGE));

        // Reset to first page when filters change
        setCurrentPage(1);

        // Update filtered reviews
        setFilteredReviews(reviews);
    }, [product.reviews, sort, ratingFilter, hasImages, hasVideos]);

    // Get current reviews for pagination
    const currentReviews = filteredReviews.slice(
        (currentPage - 1) * REVIEWS_PER_PAGE,
        currentPage * REVIEWS_PER_PAGE
    );

    // Handle page change
    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        // Scroll to top of reviews section
        window.scrollTo({
            top: document.getElementById('all-reviews')?.offsetTop || 0,
            behavior: 'smooth'
        });
    };

    return (
        <section id="all-reviews" className="bg-white rounded-xl shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">All Reviews</h2>

            <ReviewFilter
                onSortChange={setSort}
                onRatingFilterChange={setRatingFilter}
                onHasImagesChange={setHasImages}
                onHasVideosChange={setHasVideos}
                selectedSort={sort}
                selectedRating={ratingFilter}
                hasImages={hasImages}
                hasVideos={hasVideos}
                totalReviews={filteredReviews.length}
            />

            {currentReviews.length > 0 ? (
                <div className="space-y-6">
                    {currentReviews.map((review) => (
                        <div key={review.id} className="border-b pb-6 last:border-0">
                            <div className="flex justify-between mb-2">
                                <div className="flex items-center gap-2">
                                    <Avatar>
                                        <AvatarImage
                                            src={review.user?.avatar || ""}
                                            alt={`${review.user?.firstName} ${review.user?.lastName}`}
                                        />
                                        <AvatarFallback>
                                            {review.user?.firstName?.charAt(0)}
                                            {review.user?.lastName?.charAt(0)}
                                        </AvatarFallback>
                                    </Avatar>
                                    <div>
                                        <p className="font-medium">
                                            {review.user?.firstName} {review.user?.lastName}
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            {review.createdDate ? new Date(review.createdDate).toLocaleDateString() : 'No date'}
                                        </p>
                                    </div>
                                </div>
                                <StarRating rating={review.rating} />
                            </div>
                            <h3 className="font-medium mb-1">{review.title}</h3>
                            <p className="text-gray-700">{review.body}</p>

                            {/* Review Images */}
                            {review.images && review.images.length > 0 && (
                                <div className="flex gap-2 mt-3 overflow-x-auto pb-2">
                                    {review.images.map((image, i) => (
                                        <div key={i} className="h-24 w-24 rounded overflow-hidden flex-shrink-0">
                                            <Image
                                                src={image}
                                                alt={`Review image ${i + 1}`}
                                                width={96}
                                                height={96}
                                                className="object-cover w-full h-full"
                                                loading="lazy"
                                            />
                                        </div>
                                    ))}
                                </div>
                            )}

                            {/* Review Videos */}
                            {review.videos && review.videos.length > 0 && (
                                <div className="flex gap-2 mt-3 overflow-x-auto pb-2">
                                    {review.videos.map((video, i) => (
                                        <div key={i} className="h-32 w-48 rounded overflow-hidden flex-shrink-0">
                                            <video
                                                src={video}
                                                controls
                                                className="w-full h-full"
                                                aria-label={`Review video ${i + 1}`}
                                            />
                                        </div>
                                    ))}
                                </div>
                            )}

                            <div className="flex gap-4 mt-3">
                                <Button variant="ghost" size="sm" className="flex items-center gap-1 text-gray-500">
                                    <ThumbsUp className="h-4 w-4" />
                                    Helpful ({review.helpfulVotes || 0})
                                </Button>
                                <div className="flex items-center gap-1 text-sm text-gray-500">
                                    <MessageCircle className="h-4 w-4" />
                                    <span>{review.comments?.length || 0} comments</span>
                                </div>
                            </div>
                        </div>
                    ))}

                    <ReviewPagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={handlePageChange}
                    />
                </div>
            ) : (
                <div className="text-center py-8 text-gray-500">
                    <p>No reviews match your filter criteria.</p>
                    <Button
                        variant="outline"
                        className="mt-4"
                        onClick={() => {
                            setRatingFilter(0);
                            setHasImages(false);
                            setHasVideos(false);
                            setSort('newest');
                        }}
                    >
                        Clear Filters
                    </Button>
                </div>
            )}
        </section>
    );
};

export default AllReviews; 