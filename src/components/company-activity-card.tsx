"'use client'"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Star, ThumbsUp, MessageCircle, Award, TrendingUp, CheckCircle, CalendarDays, BarChart3 } from "lucide-react"
import { iProduct, iReview } from "@/app/util/Interfaces"; // Import interfaces
import dayjs from 'dayjs'; // Import dayjs
import relativeTime from 'dayjs/plugin/relativeTime'; // Import relativeTime plugin
dayjs.extend(relativeTime); // Extend dayjs

// Define props interface
interface CompanyActivityCardProps {
  product: iProduct | null;
  reviews: iReview[] | null;
}

// Keep the rest of the component for now, we'll modify the logic next
export function CompanyActivityCard({ product, reviews }: CompanyActivityCardProps) {
  const dynamicActivities = [];

  // Guard clause if no product data
  if (!product) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="text-xl font-semibold">Product Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-500">Loading activity...</p>
        </CardContent>
      </Card>
    );
  }

  // 1. Product Listed Date
  if (product.createdDate) {
    dynamicActivities.push({
      icon: CalendarDays,
      text: "Product Listed",
      date: dayjs(product.createdDate).fromNow(),
      timestamp: new Date(product.createdDate).getTime(),
    });
  }

  // 2. Latest Review
  if (reviews && reviews.length > 0) {
    // Ensure reviews are sorted by date if not already
    const sortedReviews = [...reviews].sort((a, b) =>
      new Date(b.createdDate || 0).getTime() - new Date(a.createdDate || 0).getTime()
    );
    const latestReview = sortedReviews[0];
    dynamicActivities.push({
      icon: Star,
      text: `Latest ${latestReview.rating}-star review by ${latestReview.user?.userName || 'User'}`,
      date: dayjs(latestReview.createdDate).fromNow(),
      timestamp: new Date(latestReview.createdDate || 0).getTime(),
    });
  }

  // 3. Total Reviews Count
  const totalReviews = reviews?.length || 0;
  if (totalReviews > 0) { // Only show if there are reviews
    dynamicActivities.push({
      icon: BarChart3,
      text: `Total Reviews: ${totalReviews}`,
      date: "Current count", // Static date text
      timestamp: Date.now() - 1000, // Place it slightly earlier than 'claimed'
    });
  }

  // 4. Product Owner & Verification Status
  console.log("Product hasOwner:", product.hasOwner);
  console.log("Product business:", product.business);
  console.log("Product business isVerified:", product.business?.isVerified);

  if (product.hasOwner === true) {
    // If it has an owner, show claimed and verified status
    dynamicActivities.push({
      icon: CheckCircle, // Changed from Award for consistency
      text: "Product Claimed",
      date: "Business Verified", // Always show as verified if claimed
      timestamp: product.updatedAt ? new Date(product.updatedAt).getTime() : Date.now(), // Use updatedAt if available
    });
  } else {
    // If no owner, indicate it's unclaimed
    dynamicActivities.push({
      icon: CheckCircle, // Use CheckCircle with a different context or color? Or another icon?
      text: "Unclaimed Product",
      date: "Ready for ownership",
      timestamp: Date.now(), // Make this recent if it's important
    });
  }

  // Sort activities by timestamp (most recent first) and take top 5
  const sortedActivities = dynamicActivities
    .sort((a, b) => b.timestamp - a.timestamp)
    .slice(0, 5);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Product Activity</CardTitle>
      </CardHeader>
      <CardContent>
        {sortedActivities.length > 0 ? (
          <ul className="space-y-4">
            {sortedActivities.map((activity, index) => (
              <li key={index} className="flex items-start space-x-4">
                <div className={`rounded-full p-2 ${activity.icon === CheckCircle && product.hasOwner === true ? 'bg-green-100 dark:bg-green-900' :
                  activity.icon === CheckCircle && product.hasOwner === false ? 'bg-blue-100 dark:bg-blue-900' :
                    'bg-gray-100 dark:bg-gray-800'
                  }`}>
                  <activity.icon className={`h-5 w-5 ${activity.icon === CheckCircle && product.hasOwner === true ? 'text-green-600 dark:text-green-400' :
                    activity.icon === CheckCircle && product.hasOwner === false ? 'text-blue-600 dark:text-blue-400' :
                      'text-gray-600 dark:text-gray-400'
                    }`} />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium leading-none text-gray-900 dark:text-gray-100">{activity.text}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{activity.date}</p>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-sm text-gray-500">No recent activity to display.</p>
        )}
      </CardContent>
    </Card>
  );
}
