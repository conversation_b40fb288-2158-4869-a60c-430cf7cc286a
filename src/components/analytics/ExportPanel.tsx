"use client";

import { format } from "date-fns";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Download, Share2, Info } from "lucide-react";
import { iBusinessAnalytics, iAnalyticsPeriod } from "@/app/util/Interfaces";

interface ExportPanelProps {
  analyticsData: iBusinessAnalytics;
  selectedPeriod: iAnalyticsPeriod;
}

export function ExportPanel({ analyticsData, selectedPeriod }: ExportPanelProps) {
  // Function to export data as CSV
  const exportAsCSV = () => {
    // Create headers for CSV file
    const headers = ["Date", "Views", "Unique Visitors", "Conversions"];

    // Create data rows from viewsPerDay
    const dataRows = Object.entries(analyticsData.viewsPerDay).map(
      ([date, views]) => {
        // Estimate unique visitors and conversions for each day
        const uniqueVisitors = Math.floor(views * 0.65); // 65% are unique
        const conversions = Math.floor(
          views * (analyticsData.conversionRate / 100)
        );

        return [date, views, uniqueVisitors, conversions];
      }
    );

    // Combine headers and data rows
    const csvContent = [
      headers.join(","),
      ...dataRows.map((row) => row.join(",")),
    ].join("\n");

    // Create a Blob containing the CSV data
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });

    // Create a link element to download the file
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `analytics_${format(new Date(), "yyyy-MM-dd")}.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Function to prepare data for PDF export
  const exportAsPDF = () => {
    // In a real implementation, this would generate a PDF
    alert(
      "PDF Export functionality would be implemented with a library like jsPDF"
    );
  };

  // Function to share report
  const shareReport = () => {
    // In a real implementation, this might open a modal with sharing options
    alert(
      "Sharing functionality would allow sending reports via email or generating shareable links"
    );
  };

  return (
    <>
      {/* Export Data Panel */}
      <Card>
        <CardHeader>
          <CardTitle>Analytics Reports</CardTitle>
          <CardDescription>
            Download or share your analytics data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button variant="outline" onClick={exportAsCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export as CSV
            </Button>
            <Button variant="outline" onClick={exportAsPDF}>
              <Download className="h-4 w-4 mr-2" />
              Export as PDF
            </Button>
            <Button variant="outline" onClick={shareReport}>
              <Share2 className="h-4 w-4 mr-2" />
              Share Report
            </Button>
          </div>
        </CardContent>
      </Card>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>About your analytics data</AlertTitle>
        <AlertDescription>
          Data shown is from {format(selectedPeriod.startDate, "MMMM d, yyyy")}{" "}
          to {format(selectedPeriod.endDate, "MMMM d, yyyy")}. Analytics are
          updated every 24 hours. Some metrics may be delayed.
        </AlertDescription>
      </Alert>
    </>
  );
}