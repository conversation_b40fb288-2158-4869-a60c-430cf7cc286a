"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip } from "recharts";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { iBusinessAnalytics } from "@/app/util/Interfaces";

interface ProductsTabProps {
  analyticsData: iBusinessAnalytics;
}

export function ProductsTab({ analyticsData }: ProductsTabProps) {
  // Format large numbers
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US", {
      notation: num > 9999 ? "compact" : "standard",
      maximumFractionDigits: 1,
    }).format(num);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Product Performance</CardTitle>
          <CardDescription>
            Compare views and conversions across your products. Blue bars show total page views while green bars represent actual conversions (reviews, inquiries, or purchases). Use this to identify your top-performing products and those needing attention.
          </CardDescription>
        </CardHeader>
        <CardContent className="min-h-[400px]">
          <ResponsiveContainer width="100%" height={400}>
            <BarChart
              data={analyticsData.topProducts.map((product) => ({
                name: product.name,
                Views: product.views,
                Conversion: Math.floor(
                  product.views * (product.conversion / 100)
                ),
              }))}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 70,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="name"
                angle={-45}
                textAnchor="end"
                height={70}
              />
              <YAxis tickFormatter={(value) => formatNumber(value)} />
              <Tooltip
                formatter={(value) => [formatNumber(value as number), ""]}
              />
              <Bar dataKey="Views" fill="#3b82f6" name="Views" />
              <Bar dataKey="Conversion" fill="#10b981" name="Conversions" />
            </BarChart>
          </ResponsiveContainer>
          <div className="flex justify-center mt-8">
            <Button asChild>
              <Link href="/product">
                View Public Product Pages
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}