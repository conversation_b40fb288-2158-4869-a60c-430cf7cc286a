"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON>ltip, <PERSON><PERSON><PERSON>, <PERSON>, Legend } from "recharts";
import { format } from "date-fns";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Download } from "lucide-react";
import { iBusinessAnalytics, iTrafficSource } from "@/app/util/Interfaces";

interface TrafficTabProps {
  analyticsData: iBusinessAnalytics;
  trafficSourcesData: iTrafficSource[] | null | undefined;
  isLoadingTraffic: boolean;
  isErrorTraffic: boolean;
}

// Mock traffic sources for fallback
const mockTrafficSources: iTrafficSource[] = [
  {
    id: "ts1",
    businessId: "business1",
    source: "Google",
    medium: "organic",
    visitors: 4560,
    sessions: 5120,
    bounceRate: 32.5,
    conversionRate: 3.2,
    lastUpdated: new Date(),
  },
  {
    id: "ts2",
    businessId: "business1",
    source: "Facebook",
    medium: "social",
    campaign: "Summer Promotion",
    visitors: 1250,
    sessions: 1380,
    bounceRate: 45.2,
    conversionRate: 2.1,
    lastUpdated: new Date(),
  },
  {
    id: "ts3",
    businessId: "business1",
    source: "Direct",
    medium: "none",
    visitors: 2830,
    sessions: 3210,
    bounceRate: 28.4,
    conversionRate: 4.5,
    lastUpdated: new Date(),
  },
  {
    id: "ts4",
    businessId: "business1",
    source: "Email",
    medium: "email",
    campaign: "Newsletter",
    visitors: 980,
    sessions: 1020,
    bounceRate: 18.9,
    conversionRate: 7.2,
    lastUpdated: new Date(),
  },
  {
    id: "ts5",
    businessId: "business1",
    source: "Instagram",
    medium: "social",
    visitors: 670,
    sessions: 720,
    bounceRate: 51.3,
    conversionRate: 1.8,
    lastUpdated: new Date(),
  },
];

export function TrafficTab({
  analyticsData,
  trafficSourcesData,
  isLoadingTraffic,
  isErrorTraffic
}: TrafficTabProps) {
  // Format large numbers
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US", {
      notation: num > 9999 ? "compact" : "standard",
      maximumFractionDigits: 1,
    }).format(num);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <div className="space-y-6">
      {/* Traffic Sources Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Traffic Sources</CardTitle>
          <CardDescription>
            Visitor counts by referral source. Compare the effectiveness of different channels like search engines, social media, direct visits, and referral sites to understand which marketing efforts drive the most traffic.
          </CardDescription>
        </CardHeader>
        <CardContent className="aspect-[3/1] min-h-[350px] w-full">
          <ResponsiveContainer width="100%" height={350}>
            <BarChart
              data={Object.entries(analyticsData.trafficSources).map(
                ([source, count]) => ({
                  name: source,
                  value: count,
                })
              )}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 20,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis tickFormatter={(value) => formatNumber(value)} />
              <Tooltip
                formatter={(value) => [
                  formatNumber(value as number),
                  "Visitors",
                ]}
              />
              <Bar dataKey="value" fill="#3b82f6" name="Visitors" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Traffic Sources Table */}
      <Card>
        <CardHeader>
          <CardTitle>Traffic Sources Detail</CardTitle>
          <CardDescription>Detailed performance metrics for each traffic source including visitor counts, session data, bounce rates, and conversion rates. Use this data to optimize your marketing strategy and focus on high-performing channels.</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingTraffic ? (
            <div className="flex items-center justify-center py-16">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : isErrorTraffic ? (
            <div className="text-center py-8 text-red-500">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
              <p>Error loading traffic sources data</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 font-medium">Source</th>
                    <th className="text-left py-3 font-medium">Medium</th>
                    <th className="text-right py-3 font-medium">
                      Visitors
                    </th>
                    <th className="text-right py-3 font-medium">
                      Sessions
                    </th>
                    <th className="text-right py-3 font-medium">
                      Bounce Rate
                    </th>
                    <th className="text-right py-3 font-medium">
                      Conv. Rate
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {(trafficSourcesData || mockTrafficSources).map(
                    (source) => (
                      <tr key={source.id} className="border-b">
                        <td className="py-3">{source.source}</td>
                        <td className="py-3">{source.medium}</td>
                        <td className="py-3 text-right">
                          {formatNumber(source.visitors)}
                        </td>
                        <td className="py-3 text-right">
                          {formatNumber(source.sessions)}
                        </td>
                        <td className="py-3 text-right">
                          {formatPercentage(source.bounceRate)}
                        </td>
                        <td className="py-3 text-right">
                          {formatPercentage(source.conversionRate)}
                        </td>
                      </tr>
                    )
                  )}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm" className="ml-auto">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </CardFooter>
      </Card>

      {/* Traffic Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Traffic Trends</CardTitle>
          <CardDescription>
            Daily traffic breakdown by source over the last 14 days. Track which channels drive the most visitors and identify growth patterns across Google search, direct visits, social media, and other referral sources.
          </CardDescription>
        </CardHeader>
        <CardContent className="aspect-video min-h-[350px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={Object.entries(analyticsData.viewsPerDay)
                .slice(-14) // Last 14 days
                .map(([date, views], index) => {
                  // Create artificial data for different traffic sources
                  const googleShare = 0.4 + Math.random() * 0.1; // 40-50%
                  const directShare = 0.2 + Math.random() * 0.1; // 20-30%
                  const socialShare = 0.3 + Math.random() * 0.1; // 30-40%
                  // Remaining is other sources

                  return {
                    date,
                    Total: views,
                    Google: Math.floor(views * googleShare),
                    Direct: Math.floor(views * directShare),
                    Social: Math.floor(views * socialShare),
                    Other: Math.floor(
                      views * (1 - googleShare - directShare - socialShare)
                    ),
                  };
                })}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 20,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="date"
                tickFormatter={(date) => format(new Date(date), "MMM d")}
              />
              <YAxis tickFormatter={(value) => formatNumber(value)} />
              <Tooltip
                formatter={(value, name) => [
                  formatNumber(value as number),
                  name,
                ]}
                labelFormatter={(label) =>
                  format(new Date(label as string), "MMMM d, yyyy")
                }
              />
              <Legend
                wrapperStyle={{ paddingTop: '20px' }}
                iconType="line"
              />
              <Line
                type="monotone"
                dataKey="Google"
                stroke="#4285F4"
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 6 }}
              />
              <Line
                type="monotone"
                dataKey="Direct"
                stroke="#10b981"
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 6 }}
              />
              <Line
                type="monotone"
                dataKey="Social"
                stroke="#f97316"
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 6 }}
              />
              <Line
                type="monotone"
                dataKey="Other"
                stroke="#9333ea"
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 6 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}