"use client";
import { RocketIcon } from "lucide-react";
import {
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { getUser } from "@/app/util/serverFunctions";
import LoadingSpinner from "@/app/components/LoadingSpinner";
import { iUser } from "@/app/util/Interfaces";
import Link from "next/link";
import { FaPlus } from "react-icons/fa";
import { ownerNotificationsAtom } from "@/app/store/store";
import { useAtomValue } from "jotai";
import BusinessProductCard from "@/components/BusinessProductCard";
import {
  TrendingUpIcon,
  TrendingDownIcon,
  ClockIcon,
  InfoIcon,
  StarIcon,
  EyeIcon,
  MessageSquareIcon,
  BarChart3Icon,
  AlertCircleIcon,
  ThumbsUpIcon,
  TargetIcon,
  TrendingUp,
  UsersIcon,
  ArrowUpRight,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { isOwnerComment } from "@/app/util/commentHelpers";

export function BusinessDashboardComponent() {
  const auth = useAuth();
  const notifications = useAtomValue(ownerNotificationsAtom);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["user", auth.userId],
    queryFn: async () => await getUser(),
    refetchOnWindowFocus: false,
  }) as any;

  const { data: analyticsData, isLoading: analyticsLoading } = useQuery({
    queryKey: ["businessAnalytics", data?.data?.businesses?.[0]?.id],
    queryFn: async () => {
      if (!data?.data?.businesses?.[0]?.id) return null;
      const res = await fetch(
        `/api/business/${data.data.businesses[0].id}/analytics`,
      );
      return res.json();
    },
    enabled: !!data?.data?.businesses?.[0]?.id,
  });

  if (isLoading) return <LoadingSpinner />;
  if (isError) return <p>{error?.toString()}</p>;
  if (!data?.data) return <p>No user data available</p>;

  const user: iUser = data.data as iUser;
  if (!user) return <p>Unable to load user profile</p>;

  // This might come in handy later, this keeps tha business umbrella
  //   const businessProducts = user.businesses?.map(business => ({
  //   businessId: business.id,
  //   products: business.products
  // })) || [];

  if ((user.businesses?.length || 0) < 1) {
    return (
      <div className="container mx-auto p-6 bg-gray-100 min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-2xl ">
          <CardHeader>
            <CardTitle className="text-3xl font-bold text-center">
              Contribute to Review It
            </CardTitle>
          </CardHeader>
          <CardContent className="text-start">
            <RocketIcon className="w-24 h-24 mx-auto text-blue-500 mb-6" />
            <p className="text-base md:text-xl mb-4">
              Help local businesses thrive by adding them to our platform.
              Anyone can contribute!
            </p>
            <ul className="text-base md:text-xl text-start mb-4 list-disc pl-6 space-y-2">
              <li>Support small businesses in your community</li>
              <li>Help customers discover great local services</li>
              <li>Contribute to a transparent review ecosystem</li>
              <li>Empower businesses with valuable customer feedback</li>
              <li>Foster local economic growth through increased visibility</li>
            </ul>
            <Link
              href="/submit"
              className="bg-blue-600 text-white hover:bg-blue-700 py-3 px-6 rounded-full text-lg font-semibold transition-colors duration-300 shadow-lg hover:shadow-xl"
            >
              Add a Business
            </Link>
          </CardContent>
          <CardFooter className="text-center text-gray-600">
            Join our community effort to boost local businesses and help them
            reach their full potential!
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Get only products that belong to businesses
  const allProducts = user.businesses?.flatMap((business) => business.products || []) || [];

  // Calculate your business metrics
  const totalViews = allProducts.reduce((sum, product) => sum + (product.viewCount || 0), 0);
  const totalReviews = allProducts.reduce((sum, product) => sum + (product.reviews?.length || 0), 0);
  const averageRating = allProducts.reduce((sum, product) => sum + (product.rating || 0), 0) / (allProducts.length || 1);

  

  return (
    <div className="container mx-auto p-4 sm:p-6 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold mb-6 text-center text-gray-800">
        Your Business Dashboard
      </h1>

      {/* Business Performance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 p-2 rounded-lg">
              <EyeIcon className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Views</p>
              <p className="text-xl font-semibold">{totalViews.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center gap-3">
            <div className="bg-green-100 p-2 rounded-lg">
              <MessageSquareIcon className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Reviews</p>
              <p className="text-xl font-semibold">{totalReviews}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center gap-3">
            <div className="bg-yellow-100 p-2 rounded-lg">
              <StarIcon className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Average Rating</p>
              <p className="text-xl font-semibold">{averageRating.toFixed(1)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center gap-3">
            <div className="bg-red-100 p-2 rounded-lg">
              <AlertCircleIcon className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Needs Attention</p>
              <p className="text-xl font-semibold">{allProducts.filter(product => {
                const recentReviews = product.reviews?.slice(-3) || [];
                const recentAverage = recentReviews.reduce((sum, review) => sum + (review.rating || 0), 0) / (recentReviews.length || 1);
                return recentAverage < 3;
              }).length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Business Performance Section */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <TargetIcon className="w-5 h-5 text-blue-600" />
            Your Business Performance
          </h2>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-gray-600">Customer Satisfaction</p>
            </div>
            <p className="text-2xl font-bold">{averageRating.toFixed(1)}</p>
            <p className="text-xs text-gray-600 mt-1">
              {averageRating >= 4.0
                ? "Excellent customer satisfaction!"
                : averageRating >= 3.5
                  ? "Good customer satisfaction"
                  : "Consider reviewing customer feedback to improve satisfaction"}
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-gray-600">Review Response Rate</p>
            </div>
            {analyticsLoading ? <LoadingSpinner /> : <p className="text-2xl font-bold">{analyticsData?.negativeReviewResponseRate?.toFixed(1) || 0}%</p>}
            <p className="text-xs text-gray-600 mt-1">
              {analyticsData?.negativeReviewResponseRate >= 80
                ? "Outstanding response rate!"
                : analyticsData?.negativeReviewResponseRate >= 60
                  ? "Good response rate"
                  : "Try to respond to more reviews to improve engagement"}
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-gray-600">Response Time</p>
            </div>
            {analyticsLoading ? <LoadingSpinner /> : <p className="text-2xl font-bold">{analyticsData?.averageReviewResponseTime?.toFixed(1) || 0}h</p>}
            <p className="text-xs text-gray-600 mt-1">
              {analyticsData?.averageReviewResponseTime <= 12
                ? "Excellent response time!"
                : analyticsData?.averageReviewResponseTime <= 24
                  ? "Good response time"
                  : "Aim to respond within 24 hours to improve customer satisfaction"}
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-gray-600">Content Freshness</p>
            </div>
            {analyticsLoading ? <LoadingSpinner /> : <p className="text-2xl font-bold">{analyticsData?.productContentFreshnessScore?.toFixed(1) || 0}%</p>}
            <p className="text-xs text-gray-600 mt-1">
              {analyticsData?.productContentFreshnessScore >= 80
                ? "Excellent content freshness!"
                : analyticsData?.productContentFreshnessScore >= 60
                  ? "Good content freshness"
                  : "Update your product info regularly to improve your score"}
            </p>
          </div>
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Understanding Your Metrics</h3>
          <ul className="text-sm text-blue-800 space-y-2">
            <li className="flex items-start gap-2">
              <span className="text-blue-600">•</span>
              <span>These metrics help you understand how your business is performing and where you can improve.</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600">•</span>
              <span>Each metric has specific targets that indicate good performance in your industry.</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600">•</span>
              <span>Focus on improving metrics that are below target to enhance customer satisfaction.</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Management Links */}
      <div className="flex flex-col sm:flex-row justify-center gap-4 mb-8">
        <Link
          href="/submit"
          className="flex items-center bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-all duration-300 transform hover:scale-105 shadow-md"
        >
          <FaPlus className="w-4 h-4 mr-2" />
          Add New Business
        </Link>

        <Link
          href="/owner-admin"
          className="flex items-center bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-all duration-300 transform hover:scale-105 shadow-md"
        >
          <RocketIcon className="w-4 h-4 mr-2" />
          Advanced Admin Dashboard
        </Link>
      </div>

      {/* Responsive Products Grid - Adjusts based on screen size */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {allProducts.map((product) => (
          <BusinessProductCard
            key={product.id}
            product={product}
            notifications={notifications || []}
          />
        ))}
      </div>

      {/* Show message if no products */}
      {allProducts.length === 0 && (
        <div className="text-center py-8 bg-white rounded-lg shadow-md">
          <p className="text-gray-500 text-lg">No products yet. Add a business to get started.</p>
        </div>
      )}

      {/* Dashboard Legend and Help Section */}
      <div className="mt-12 bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <InfoIcon className="w-5 h-5 text-blue-600" />
          Understanding Your Dashboard
        </h2>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Performance Metrics */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Performance Metrics</h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="bg-blue-100 p-2 rounded-lg mt-1">
                  <EyeIcon className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Views</p>
                  <p className="text-sm text-gray-600">Track customer interest and optimize your product visibility. Higher views often correlate with increased sales opportunities.</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="bg-green-100 p-2 rounded-lg mt-1">
                  <MessageSquareIcon className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Reviews</p>
                  <p className="text-sm text-gray-600">Customer feedback is crucial for growth. More reviews build trust and provide valuable insights for improvement.</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="bg-yellow-100 p-2 rounded-lg mt-1">
                  <StarIcon className="w-4 h-4 text-yellow-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Rating</p>
                  <p className="text-sm text-gray-600">Your overall customer satisfaction score. Aim for 4+ stars to maintain a strong market position.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Trend Indicators */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Trend Indicators</h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="bg-green-50 p-2 rounded-lg mt-1 border border-green-200">
                  <TrendingUpIcon className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Trending Up</p>
                  <p className="text-sm text-gray-600">Your product is gaining momentum with positive feedback. Consider expanding inventory or features.</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="bg-red-50 p-2 rounded-lg mt-1 border border-red-200">
                  <TrendingDownIcon className="w-4 h-4 text-red-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Needs Attention</p>
                  <p className="text-sm text-gray-600">Address customer concerns promptly. Review recent feedback and implement improvements to maintain quality.</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="bg-gray-50 p-2 rounded-lg mt-1 border border-gray-200">
                  <ClockIcon className="w-4 h-4 text-gray-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Stable</p>
                  <p className="text-sm text-gray-600">Maintain your current quality while looking for opportunities to enhance customer experience.</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Separator className="my-6" />

        {/* Enhanced Tips Section */}
        <div className="space-y-3">
          <h3 className="text-lg font-medium text-gray-900">Business Growth Strategies</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Quick Wins</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="text-green-600">•</span>
                  <span>Respond to reviews within 24 hours to improve your response rate and customer satisfaction.</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-600">•</span>
                  <span>Update product images and descriptions weekly to maintain freshness and SEO ranking.</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-600">•</span>
                  <span>Address negative reviews within 48 hours to prevent reputation damage.</span>
                </li>
              </ul>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Long-term Growth</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="text-blue-600">•</span>
                  <span>Analyze competitor reviews to identify market gaps and opportunities.</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-600">•</span>
                  <span>Implement a review collection strategy to increase review volume.</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-600">•</span>
                  <span>Use customer feedback to guide product development and improvements.</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
