"use client";

import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import {
  Search,
  Package,
  Tag,
  Calendar,
  Star,
  Eye,
  AlertCircle,
  Edit,
  Trash2,
  ClockIcon
} from "lucide-react";
import EditProductForm from "@/app/components/EditProductForm";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import OpeningHours from '@/app/components/OpeningHours';

import { getAllProducts, getProducts, updateProductStatus, deleteProduct } from "@/app/util/adminFunctions";
import { iProduct } from "@/app/util/Interfaces";
import { format } from 'date-fns';

export default function ProductsManagement() {
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState('all');
  const [sortBy, setSortBy] = useState('createdDate');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedProduct, setSelectedProduct] = useState<iProduct | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [pendingDeleteProduct, setPendingDeleteProduct] = useState<iProduct | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<iProduct | null>(null);

  const queryClient = useQueryClient();

  const { data, isLoading, error } = useQuery({
    queryKey: ['products', page, search, status, sortBy, sortOrder],
    queryFn: () =>
      getProducts({
        page,
        limit: 10,
        search,
        status,
        sortBy,
        sortOrder,
      }),
  });

  const updateStatusMutation = useMutation({
    mutationFn: (params: { id: string; data: { isDeleted?: boolean; featuredPosition?: number | null } }) =>
      updateProductStatus(params.id, params.data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });

  const handleStatusChange = async (productId: string, isDeleted: boolean) => {
    await updateStatusMutation.mutateAsync({
      id: productId,
      data: { isDeleted },
    });
  };

  const handleDelete = async (productId: string) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      await deleteMutation.mutateAsync(productId);
    }
  };

  const handleFeature = async (productId: string, position: number | null) => {
    await updateStatusMutation.mutateAsync({
      id: productId,
      data: { featuredPosition: position },
    });
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error loading products</div>;
  }

  // Extract products and pagination from the response
  const products = data?.data?.products || [];
  const pagination = data?.data?.pagination || { page: 1, total: 0, totalPages: 1, limit: 10 };

  return (
    <div className="space-y-6 overflow-x-hidden max-w-full">
      <Card className="max-w-full">
        <CardHeader>
          <CardTitle>Products Management</CardTitle>
        </CardHeader>
        <CardContent className="max-w-full overflow-x-hidden">
          <div className="flex items-center justify-between mb-4">
            <div className="flex flex-col gap-2 w-full md:flex-row md:items-center md:space-x-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search products..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="deleted">Deleted</SelectItem>
                </SelectContent>
              </Select>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdDate">Created Date</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="w-full md:w-auto"
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </Button>
            </div>
          </div>

          {/* Desktop Table View */}
          <div className="hidden md:block">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Created By</TableHead>
                  <TableHead>Rating</TableHead>
                  <TableHead>Reviews</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {products.length > 0 ? (
                  products.map((product: iProduct) => (
                    <TableRow key={product.id}>
                      <TableCell>{product.name}</TableCell>
                      <TableCell>
                        {product.createdBy?.firstName} {product.createdBy?.lastName}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                          {product.rating.toFixed(1)}
                        </div>
                      </TableCell>
                      <TableCell>{product._count?.reviews || 0}</TableCell>
                      <TableCell>
                        <Badge
                          variant={product.isDeleted ? 'destructive' : 'default'}
                        >
                          {product.isDeleted ? 'Deleted' : 'Active'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {format(new Date(product.createdDate), 'MMM d, yyyy')}
                      </TableCell>
                      <TableCell>
                      <div className="flex items-center space-x-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => setSelectedProduct(product)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle className="text-xl font-bold">Product Details</DialogTitle>
                            </DialogHeader>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                              {/* Left Column */}
                              <div className="space-y-6">
                                {/* Product Image */}
                                {product.display_image && (
                                  <div className="aspect-video relative rounded-lg overflow-hidden bg-gray-100">
                                    <Image
                                      src={product.display_image}
                                      alt={product.name}
                                      fill
                                      className="object-cover"
                                    />
                                  </div>
                                )}
                                
                                {/* Basic Info */}
                                <div className="space-y-4">
                                  <div>
                                    <h3 className="font-semibold text-lg mb-2">Product Information</h3>
                                    <div className="space-y-2">
                                      <div>
                                        <span className="font-medium text-sm text-gray-600">Name:</span>
                                        <p className="text-lg font-medium">{product.name}</p>
                                      </div>
                                      <div>
                                        <span className="font-medium text-sm text-gray-600">Description:</span>
                                        <p className="text-gray-700 leading-relaxed">{product.description}</p>
                                      </div>
                                      <div className="flex items-center gap-4">
                                        <div className="flex items-center">
                                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                                          <span className="font-medium">{product.rating.toFixed(1)}</span>
                                        </div>
                                        <span className="text-sm text-gray-600">({product._count?.reviews || 0} reviews)</span>
                                      </div>
                                    </div>
                                  </div>
                                  
                                  {/* Tags */}
                                  <div>
                                    <h4 className="font-medium text-sm text-gray-600 mb-2">Tags</h4>
                                    <div className="flex flex-wrap gap-2">
                                      {product.tags?.length > 0 ? (
                                        product.tags.map((tag) => (
                                          <Badge key={tag} variant="secondary" className="text-xs">
                                            {tag}
                                          </Badge>
                                        ))
                                      ) : (
                                        <p className="text-sm text-muted-foreground">No tags</p>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              
                              {/* Right Column */}
                              <div className="space-y-6">
                                {/* Owner Information */}
                                <div>
                                  <h3 className="font-semibold text-lg mb-3">Owner Information</h3>
                                  <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                                    <div className="flex items-center justify-between">
                                      <span className="font-medium text-sm text-gray-600">Has Owner:</span>
                                      <Badge variant={product.hasOwner ? "default" : "secondary"}>
                                        {product.hasOwner ? "Yes" : "No"}
                                      </Badge>
                                    </div>
                                    
                                    {product.createdBy && (
                                      <div>
                                        <span className="font-medium text-sm text-gray-600 block mb-2">Created By:</span>
                                        <div className="flex items-center space-x-3 p-2 bg-white rounded-md border hover:bg-gray-50 transition-colors cursor-pointer"
                                             onClick={() => {
                                               // Navigate to user profile or show user details
                                               console.log('Navigate to user:', product.createdBy?.id);
                                             }}>
                                          {product.createdBy.avatar ? (
                                            <Image
                                              src={product.createdBy.avatar}
                                              alt={`${product.createdBy.firstName} ${product.createdBy.lastName}`}
                                              width={32}
                                              height={32}
                                              className="rounded-full"
                                            />
                                          ) : (
                                            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                              <span className="text-xs font-medium text-gray-600">
                                                {product.createdBy.firstName?.[0]}{product.createdBy.lastName?.[0]}
                                              </span>
                                            </div>
                                          )}
                                          <div>
                                            <p className="font-medium text-sm">
                                              {product.createdBy.firstName} {product.createdBy.lastName}
                                            </p>
                                            <p className="text-xs text-gray-500">@{product.createdBy.userName}</p>
                                          </div>
                                        </div>
                                      </div>
                                    )}
                                    
                                    {product.business && (
                                      <div>
                                        <span className="font-medium text-sm text-gray-600 block mb-2">Business:</span>
                                        <div className="p-2 bg-white rounded-md border">
                                          <p className="font-medium text-sm">{product.business.ownerName || 'Business Owner'}</p>
                                          {product.business.businessDescription && (
                                            <p className="text-xs text-gray-600 mt-1">{product.business.businessDescription}</p>
                                          )}
                                          <div className="flex items-center mt-2 space-x-2">
                                            <Badge variant={product.business.isVerified ? "default" : "secondary"} className="text-xs">
                                              {product.business.isVerified ? "Verified" : "Unverified"}
                                            </Badge>
                                            <Badge variant={product.business.subscriptionStatus === 'ACTIVE' ? "default" : "secondary"} className="text-xs">
                                              {product.business.subscriptionStatus}
                                            </Badge>
                                          </div>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                
                                {/* Contact Information */}
                                <div>
                                  <h3 className="font-semibold text-lg mb-3">Contact Information</h3>
                                  <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                                    {product.email && (
                                      <div className="flex justify-between">
                                        <span className="font-medium text-sm text-gray-600">Email:</span>
                                        <a href={`mailto:${product.email}`} className="text-sm text-blue-600 hover:underline">
                                          {product.email}
                                        </a>
                                      </div>
                                    )}
                                    {product.telephone && (
                                      <div className="flex justify-between">
                                        <span className="font-medium text-sm text-gray-600">Phone:</span>
                                        <a href={`tel:${product.telephone}`} className="text-sm text-blue-600 hover:underline">
                                          {product.telephone}
                                        </a>
                                      </div>
                                    )}
                                    {product.website?.length > 0 && (
                                      <div>
                                        <span className="font-medium text-sm text-gray-600 block mb-1">Website:</span>
                                        <div className="space-y-1">
                                          {product.website.map((site, index) => (
                                            <a key={index} href={site} target="_blank" rel="noopener noreferrer" 
                                               className="text-sm text-blue-600 hover:underline block">
                                              {site}
                                            </a>
                                          ))}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                
                                {/* Business Hours */}
                                {product.openingHrs && product.closingHrs && product.openingDays && product.openingDays.length > 0 && (
                                  <div>
                                    <h3 className="font-semibold text-lg mb-3">Business Hours</h3>
                                    <div className="bg-gray-50 rounded-lg p-4">
                                      <div className="flex items-center text-gray-700">
                                        <ClockIcon className="w-4 h-4 mr-2 text-gray-500" />
                                        <OpeningHours product={product} />
                                      </div>
                                    </div>
                                  </div>
                                )}
                                
                                {/* Additional Info */}
                                <div>
                                  <h3 className="font-semibold text-lg mb-3">Additional Information</h3>
                                  <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                                    <div className="flex justify-between">
                                      <span className="font-medium text-sm text-gray-600">Created:</span>
                                      <span className="text-sm">{format(new Date(product.createdDate), 'MMM d, yyyy')}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="font-medium text-sm text-gray-600">Status:</span>
                                      <Badge variant={product.isDeleted ? 'destructive' : 'default'} className="text-xs">
                                        {product.isDeleted ? 'Deleted' : 'Active'}
                                      </Badge>
                                    </div>
                                    {product.viewCount !== undefined && (
                                      <div className="flex justify-between">
                                        <span className="font-medium text-sm text-gray-600">Views:</span>
                                        <span className="text-sm">{product.viewCount}</span>
                                      </div>
                                    )}
                                    {product.featuredPosition && (
                                      <div className="flex justify-between">
                                        <span className="font-medium text-sm text-gray-600">Featured Position:</span>
                                        <Badge variant="default" className="text-xs">#{product.featuredPosition}</Badge>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setEditingProduct(product);
                                setEditDialogOpen(true);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>Edit Product</DialogTitle>
                            </DialogHeader>
                            {editingProduct && (
                              <EditProductForm
                                initialProduct={editingProduct}
                                onSuccess={() => {
                                  setEditDialogOpen(false);
                                  setEditingProduct(null);
                                  queryClient.invalidateQueries({ queryKey: ['products'] });
                                }}
                              />
                            )}
                          </DialogContent>
                        </Dialog>
                        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                if (!product.isDeleted) {
                                  setPendingDeleteProduct(product);
                                  setDeleteDialogOpen(true);
                                } else {
                                  handleStatusChange(product.id!, !product.isDeleted);
                                }
                              }}
                            >
                              {product.isDeleted ? (
                                <AlertCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <Trash2 className="h-4 w-4 text-red-500" />
                              )}
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Confirm Deletion</DialogTitle>
                            </DialogHeader>
                            <div>Are you sure you want to delete the product <b>{pendingDeleteProduct?.name}</b>?</div>
                            <div className="flex justify-end gap-2 mt-4">
                              <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                                Cancel
                              </Button>
                              <Button
                                variant="destructive"
                                onClick={async () => {
                                  if (pendingDeleteProduct) {
                                    await handleStatusChange(pendingDeleteProduct.id!, true);
                                    setDeleteDialogOpen(false);
                                    setPendingDeleteProduct(null);
                                  }
                                }}
                              >
                                Delete
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      No products found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Mobile Card View */}
          <div className="md:hidden space-y-4 max-w-full overflow-x-hidden">
            {products.length > 0 ? (
              products.map((product: iProduct) => (
                <Card key={product.id} className="p-4 w-full max-w-full">
                  <div className="space-y-3">
                    {/* Product Header */}
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg leading-tight">{product.name}</h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          by {product.createdBy?.firstName} {product.createdBy?.lastName}
                        </p>
                      </div>
                      <Badge
                        variant={product.isDeleted ? 'destructive' : 'default'}
                        className="ml-2"
                      >
                        {product.isDeleted ? 'Deleted' : 'Active'}
                      </Badge>
                    </div>

                    {/* Rating and Reviews */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                          <span className="font-medium">{product.rating.toFixed(1)}</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {product._count?.reviews || 0} reviews
                        </span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {format(new Date(product.createdDate), 'MMM d, yyyy')}
                      </span>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-end space-x-2 pt-2 border-t">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedProduct(product)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle className="text-xl font-bold">Product Details</DialogTitle>
                          </DialogHeader>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Left Column */}
                            <div className="space-y-6">
                              {/* Product Image */}
                              {product.display_image && (
                                <div className="aspect-video relative rounded-lg overflow-hidden bg-gray-100">
                                  <Image
                                    src={product.display_image}
                                    alt={product.name}
                                    fill
                                    className="object-cover"
                                  />
                                </div>
                              )}
                              
                              {/* Basic Info */}
                              <div className="space-y-4">
                                <div>
                                  <h3 className="font-semibold text-lg mb-2">Product Information</h3>
                                  <div className="space-y-2">
                                    <div>
                                      <span className="font-medium text-sm text-gray-600">Name:</span>
                                      <p className="text-lg font-medium">{product.name}</p>
                                    </div>
                                    <div>
                                      <span className="font-medium text-sm text-gray-600">Description:</span>
                                      <p className="text-gray-700 leading-relaxed">{product.description}</p>
                                    </div>
                                    <div className="flex items-center gap-4">
                                      <div className="flex items-center">
                                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                                        <span className="font-medium">{product.rating.toFixed(1)}</span>
                                      </div>
                                      <span className="text-sm text-gray-600">({product._count?.reviews || 0} reviews)</span>
                                    </div>
                                  </div>
                                </div>
                                
                                {/* Tags */}
                                <div>
                                  <h4 className="font-medium text-sm text-gray-600 mb-2">Tags</h4>
                                  <div className="flex flex-wrap gap-2">
                                    {product.tags?.length > 0 ? (
                                      product.tags.map((tag) => (
                                        <Badge key={tag} variant="secondary" className="text-xs">
                                          {tag}
                                        </Badge>
                                      ))
                                    ) : (
                                      <p className="text-sm text-muted-foreground">No tags</p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            {/* Right Column */}
                            <div className="space-y-6">
                              {/* Owner Information */}
                              <div>
                                <h3 className="font-semibold text-lg mb-3">Owner Information</h3>
                                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                                  <div className="flex items-center justify-between">
                                    <span className="font-medium text-sm text-gray-600">Has Owner:</span>
                                    <Badge variant={product.hasOwner ? "default" : "secondary"}>
                                      {product.hasOwner ? "Yes" : "No"}
                                    </Badge>
                                  </div>
                                  
                                  {product.createdBy && (
                                    <div>
                                      <span className="font-medium text-sm text-gray-600 block mb-2">Created By:</span>
                                      <div className="flex items-center space-x-3 p-2 bg-white rounded-md border hover:bg-gray-50 transition-colors cursor-pointer"
                                           onClick={() => {
                                             // Navigate to user profile or show user details
                                             console.log('Navigate to user:', product.createdBy?.id);
                                           }}>
                                        {product.createdBy.avatar ? (
                                          <Image
                                            src={product.createdBy.avatar}
                                            alt={`${product.createdBy.firstName} ${product.createdBy.lastName}`}
                                            width={32}
                                            height={32}
                                            className="rounded-full"
                                          />
                                        ) : (
                                          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                            <span className="text-xs font-medium text-gray-600">
                                              {product.createdBy.firstName?.[0]}{product.createdBy.lastName?.[0]}
                                            </span>
                                          </div>
                                        )}
                                        <div>
                                          <p className="font-medium text-sm">
                                            {product.createdBy.firstName} {product.createdBy.lastName}
                                          </p>
                                          <p className="text-xs text-gray-500">@{product.createdBy.userName}</p>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                  
                                  {product.business && (
                                    <div>
                                      <span className="font-medium text-sm text-gray-600 block mb-2">Business:</span>
                                      <div className="p-2 bg-white rounded-md border">
                                        <p className="font-medium text-sm">{product.business.ownerName || 'Business Owner'}</p>
                                        {product.business.businessDescription && (
                                          <p className="text-xs text-gray-600 mt-1">{product.business.businessDescription}</p>
                                        )}
                                        <div className="flex items-center mt-2 space-x-2">
                                          <Badge variant={product.business.isVerified ? "default" : "secondary"} className="text-xs">
                                            {product.business.isVerified ? "Verified" : "Unverified"}
                                          </Badge>
                                          <Badge variant={product.business.subscriptionStatus === 'ACTIVE' ? "default" : "secondary"} className="text-xs">
                                            {product.business.subscriptionStatus}
                                          </Badge>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                              
                              {/* Contact Information */}
                              <div>
                                <h3 className="font-semibold text-lg mb-3">Contact Information</h3>
                                <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                                  {product.email && (
                                    <div className="flex justify-between">
                                      <span className="font-medium text-sm text-gray-600">Email:</span>
                                      <a href={`mailto:${product.email}`} className="text-sm text-blue-600 hover:underline">
                                        {product.email}
                                      </a>
                                    </div>
                                  )}
                                  {product.telephone && (
                                    <div className="flex justify-between">
                                      <span className="font-medium text-sm text-gray-600">Phone:</span>
                                      <a href={`tel:${product.telephone}`} className="text-sm text-blue-600 hover:underline">
                                        {product.telephone}
                                      </a>
                                    </div>
                                  )}
                                  {product.website?.length > 0 && (
                                    <div>
                                      <span className="font-medium text-sm text-gray-600 block mb-1">Website:</span>
                                      <div className="space-y-1">
                                        {product.website.map((site, index) => (
                                          <a key={index} href={site} target="_blank" rel="noopener noreferrer" 
                                             className="text-sm text-blue-600 hover:underline block">
                                            {site}
                                          </a>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                              
                              {/* Business Hours */}
                              {product.openingHrs && product.closingHrs && product.openingDays && product.openingDays.length > 0 && (
                                <div>
                                  <h3 className="font-semibold text-lg mb-3">Business Hours</h3>
                                  <div className="bg-gray-50 rounded-lg p-4">
                                    <div className="flex items-center text-gray-700">
                                      <ClockIcon className="w-4 h-4 mr-2 text-gray-500" />
                                      <OpeningHours product={product} />
                                    </div>
                                  </div>
                                </div>
                              )}
                              
                              {/* Additional Info */}
                              <div>
                                <h3 className="font-semibold text-lg mb-3">Additional Information</h3>
                                <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                                  <div className="flex justify-between">
                                    <span className="font-medium text-sm text-gray-600">Created:</span>
                                    <span className="text-sm">{format(new Date(product.createdDate), 'MMM d, yyyy')}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="font-medium text-sm text-gray-600">Status:</span>
                                    <Badge variant={product.isDeleted ? 'destructive' : 'default'} className="text-xs">
                                      {product.isDeleted ? 'Deleted' : 'Active'}
                                    </Badge>
                                  </div>
                                  {product.viewCount !== undefined && (
                                    <div className="flex justify-between">
                                      <span className="font-medium text-sm text-gray-600">Views:</span>
                                      <span className="text-sm">{product.viewCount}</span>
                                    </div>
                                  )}
                                  {product.featuredPosition && (
                                    <div className="flex justify-between">
                                      <span className="font-medium text-sm text-gray-600">Featured Position:</span>
                                      <Badge variant="default" className="text-xs">#{product.featuredPosition}</Badge>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
                        <DialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setEditingProduct(product);
                              setEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle>Edit Product</DialogTitle>
                          </DialogHeader>
                          {editingProduct && (
                            <EditProductForm
                              initialProduct={editingProduct}
                              onSuccess={() => {
                                setEditDialogOpen(false);
                                setEditingProduct(null);
                                queryClient.invalidateQueries({ queryKey: ['products'] });
                              }}
                            />
                          )}
                        </DialogContent>
                      </Dialog>
                      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                        <DialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              if (!product.isDeleted) {
                                setPendingDeleteProduct(product);
                                setDeleteDialogOpen(true);
                              } else {
                                handleStatusChange(product.id!, !product.isDeleted);
                              }
                            }}
                          >
                            {product.isDeleted ? (
                              <>
                                <AlertCircle className="h-4 w-4 mr-1 text-green-500" />
                                Restore
                              </>
                            ) : (
                              <>
                                <Trash2 className="h-4 w-4 mr-1 text-red-500" />
                                Delete
                              </>
                            )}
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Confirm Deletion</DialogTitle>
                          </DialogHeader>
                          <div>Are you sure you want to delete the product <b>{pendingDeleteProduct?.name}</b>?</div>
                          <div className="flex justify-end gap-2 mt-4">
                            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                              Cancel
                            </Button>
                            <Button
                              variant="destructive"
                              onClick={async () => {
                                if (pendingDeleteProduct) {
                                  await handleStatusChange(pendingDeleteProduct.id!, true);
                                  setDeleteDialogOpen(false);
                                  setPendingDeleteProduct(null);
                                }
                              }}
                            >
                              Delete
                            </Button>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </Card>
              ))
            ) : (
              <Card className="p-8 text-center">
                <p className="text-muted-foreground">No products found</p>
              </Card>
            )}
          </div>

          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} products
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page >= pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}