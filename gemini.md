1. On wide screen or pc show tables but for mobile use cards instead as tables are usually too wide for mobile
2. The project is a far way into developement so most likely a function, component or functionality already exist and i rated use that instead of making something new.
3. if you need to modify a component or function that existed know that some part of the app uses this piece and changing it might break the app so be careful or add optionals if it's safe to do so.
4. i don't want any new component to new more that 300l ines of code in one file.
